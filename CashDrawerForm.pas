unit CashDrawerForm;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Spin, DataModule, Logging;

type
  TfrmCashDrawer = class(TForm)
    pnlMain: TPanel;
    pnlButtons: TPanel;
    lblTitle: TLabel;
    lblAmount: TLabel;
    lblReason: TLabel;
    edtAmount: TFloatSpinEdit;
    memoReason: TMemo;
    btnOK: TButton;
    btnCancel: TButton;
    
    procedure FormCreate(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure edtAmountKeyPress(Sender: TObject; var Key: char);
    
  private
    FShiftID: Integer;
    FTransactionType: string;
    
  public
    procedure SetupCashIn(AShiftID: Integer);
    procedure SetupCashOut(AShiftID: Integer);
  end;

var
  frmCashDrawer: TfrmCashDrawer;

implementation

{$R *.lfm}

procedure TfrmCashDrawer.FormCreate(Sender: TObject);
begin
  Position := poScreenCenter;
  BorderStyle := bsDialog;
  
  edtAmount.DecimalPlaces := 2;
  edtAmount.MinValue := 0.01;
  edtAmount.MaxValue := 9999.99;
  edtAmount.Value := 0;
end;

procedure TfrmCashDrawer.SetupCashIn(AShiftID: Integer);
begin
  FShiftID := AShiftID;
  FTransactionType := 'CASH_IN';
  
  Caption := 'Cash In';
  lblTitle.Caption := 'Cash In Transaction';
  lblTitle.Font.Color := clGreen;
  
  memoReason.Text := 'Cash added to drawer';
  
  if edtAmount.CanFocus then
    edtAmount.SetFocus;
end;

procedure TfrmCashDrawer.SetupCashOut(AShiftID: Integer);
begin
  FShiftID := AShiftID;
  FTransactionType := 'CASH_OUT';
  
  Caption := 'Cash Out';
  lblTitle.Caption := 'Cash Out Transaction';
  lblTitle.Font.Color := clRed;
  
  memoReason.Text := 'Cash removed from drawer';
  
  if edtAmount.CanFocus then
    edtAmount.SetFocus;
end;

procedure TfrmCashDrawer.btnOKClick(Sender: TObject);
var
  Amount: Currency;
  Reason: string;
  Success: Boolean;
begin
  try
    Amount := edtAmount.Value;
    Reason := Trim(memoReason.Text);
    
    if Amount <= 0 then
    begin
      ShowMessage('Please enter a valid amount');
      edtAmount.SetFocus;
      Exit;
    end;
    
    if Reason = '' then
    begin
      ShowMessage('Please enter a reason for this transaction');
      memoReason.SetFocus;
      Exit;
    end;
    
    btnOK.Enabled := False;
    btnOK.Caption := 'Processing...';
    Application.ProcessMessages;
    
    try
      if FTransactionType = 'CASH_IN' then
        Success := dmMain.CashIn(FShiftID, Amount, Reason)
      else
        Success := dmMain.CashOut(FShiftID, Amount, Reason);
      
      if Success then
      begin
        ShowMessage('Transaction completed successfully');
        ModalResult := mrOK;
      end
      else
      begin
        ShowMessage('Transaction failed');
      end;
      
    finally
      btnOK.Enabled := True;
      btnOK.Caption := 'OK';
    end;
    
  except
    on E: Exception do
    begin
      Logger.LogError('Cash drawer transaction error: ' + E.Message);
      ShowMessage('Transaction error: ' + E.Message);
      btnOK.Enabled := True;
      btnOK.Caption := 'OK';
    end;
  end;
end;

procedure TfrmCashDrawer.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

procedure TfrmCashDrawer.edtAmountKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // Enter key
  begin
    Key := #0;
    if memoReason.CanFocus then
      memoReason.SetFocus;
  end;
end;

end.
