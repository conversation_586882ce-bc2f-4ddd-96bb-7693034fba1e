object frmConfig: TfrmConfig
  Left = 372
  Height = 450
  Top = 216
  Width = 500
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'System Configuration'
  ClientHeight = 450
  ClientWidth = 500
  Color = clBtnFace
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  OnCreate = FormCreate
  OnShow = FormShow
  Position = poScreenCenter
  LCLVersion = '*******'
  object pnlMain: TPanel
    Left = 0
    Height = 450
    Top = 0
    Width = 500
    Align = alClient
    BevelOuter = bvNone
    ClientHeight = 450
    ClientWidth = 500
    TabOrder = 0
    object pgConfig: TPageControl
      Left = 10
      Height = 380
      Top = 10
      Width = 480
      ActivePage = tsDatabase
      TabIndex = 0
      TabOrder = 0
      object tsDatabase: TTabSheet
        Caption = 'Database'
        ClientHeight = 352
        ClientWidth = 472
        object lblDBHost: TLabel
          Left = 20
          Height = 13
          Top = 30
          Width = 27
          Caption = 'Host:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtDBHost: TEdit
          Left = 100
          Height = 21
          Top = 27
          Width = 200
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 0
        end
        object lblDBPort: TLabel
          Left = 20
          Height = 13
          Top = 60
          Width = 25
          Caption = 'Port:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtDBPort: TEdit
          Left = 100
          Height = 21
          Top = 57
          Width = 100
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 1
        end
        object lblDBPath: TLabel
          Left = 20
          Height = 13
          Top = 90
          Width = 71
          Caption = 'Database Path:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtDBPath: TEdit
          Left = 100
          Height = 21
          Top = 87
          Width = 300
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 2
        end
        object btnBrowseDB: TButton
          Left = 410
          Height = 21
          Top = 87
          Width = 30
          Caption = '...'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 3
          OnClick = btnBrowseDBClick
        end
        object lblDBUser: TLabel
          Left = 20
          Height = 13
          Top = 120
          Width = 55
          Caption = 'Username:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtDBUser: TEdit
          Left = 100
          Height = 21
          Top = 117
          Width = 150
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 4
        end
        object lblDBPassword: TLabel
          Left = 20
          Height = 13
          Top = 150
          Width = 53
          Caption = 'Password:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtDBPassword: TEdit
          Left = 100
          Height = 21
          Top = 147
          Width = 150
          EchoMode = emPassword
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          PasswordChar = '*'
          TabOrder = 5
        end
        object btnTestConnection: TButton
          Left = 100
          Height = 30
          Top = 190
          Width = 120
          Caption = 'Test Connection'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 6
          OnClick = btnTestConnectionClick
        end
      end
      object tsCompany: TTabSheet
        Caption = 'Company'
        ClientHeight = 352
        ClientWidth = 472
        object lblCompanyName: TLabel
          Left = 20
          Height = 13
          Top = 30
          Width = 80
          Caption = 'Company Name:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtCompanyName: TEdit
          Left = 120
          Height = 21
          Top = 27
          Width = 300
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 0
        end
        object lblCompanyAddress: TLabel
          Left = 20
          Height = 13
          Top = 60
          Top = 60
          Width = 88
          Caption = 'Company Address:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object memoCompanyAddress: TMemo
          Left = 120
          Height = 80
          Top = 57
          Width = 300
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          ScrollBars = ssAutoVertical
          TabOrder = 1
        end
        object lblReceiptPrinter: TLabel
          Left = 20
          Height = 13
          Top = 160
          Width = 82
          Caption = 'Receipt Printer:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object cbReceiptPrinter: TComboBox
          Left = 120
          Height = 21
          Top = 157
          Width = 250
          ItemHeight = 13
          Style = csDropDownList
          TabOrder = 2
        end
        object btnRefreshPrinters: TButton
          Left = 380
          Height = 21
          Top = 157
          Width = 60
          Caption = 'Refresh'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 3
          OnClick = btnRefreshPrintersClick
        end
      end
      object tsSystem: TTabSheet
        Caption = 'System'
        ClientHeight = 352
        ClientWidth = 472
        object lblTaxRate: TLabel
          Left = 20
          Height = 13
          Top = 30
          Width = 52
          Caption = 'Tax Rate:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtTaxRate: TEdit
          Left = 100
          Height = 21
          Top = 27
          Width = 100
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 0
          Text = '0.00'
        end
        object lblPercent: TLabel
          Left = 210
          Height = 13
          Top = 30
          Width = 11
          Caption = '%'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object chkAutoBackup: TCheckBox
          Left = 20
          Height = 19
          Top = 70
          Width = 89
          Caption = 'Auto Backup'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 1
        end
        object lblBackupPath: TLabel
          Left = 40
          Height = 13
          Top = 100
          Width = 70
          Caption = 'Backup Path:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object edtBackupPath: TEdit
          Left = 120
          Height = 21
          Top = 97
          Width = 250
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 2
        end
        object btnBrowseBackup: TButton
          Left = 380
          Height = 21
          Top = 97
          Width = 30
          Caption = '...'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          TabOrder = 3
          OnClick = btnBrowseBackupClick
        end
        object chkEnableLogging: TCheckBox
          Left = 20
          Height = 19
          Top = 140
          Width = 95
          Caption = 'Enable Logging'
          Checked = True
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
          State = cbChecked
          TabOrder = 4
        end
        object lblLogLevel: TLabel
          Left = 40
          Height = 13
          Top = 170
          Width = 56
          Caption = 'Log Level:'
          Font.Height = -11
          Font.Name = 'Tahoma'
          ParentFont = False
        end
        object cbLogLevel: TComboBox
          Left = 120
          Height = 21
          Top = 167
          Width = 120
          ItemHeight = 13
          Items.Strings = (
            'Error'
            'Warning'
            'Info'
            'Debug'
          )
          Style = csDropDownList
          TabOrder = 5
          Text = 'Info'
        end
      end
    end
    object pnlButtons: TPanel
      Left = 0
      Height = 60
      Top = 390
      Width = 500
      Align = alBottom
      BevelOuter = bvNone
      ClientHeight = 60
      ClientWidth = 500
      TabOrder = 1
      object btnSave: TButton
        Left = 200
        Height = 35
        Top = 12
        Width = 100
        Caption = '&Save'
        Default = True
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        TabOrder = 0
        OnClick = btnSaveClick
      end
      object btnCancel: TButton
        Left = 320
        Height = 35
        Top = 12
        Width = 100
        Cancel = True
        Caption = '&Cancel'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
        TabOrder = 1
        OnClick = btnCancelClick
      end
    end
  end
  object OpenDialog1: TOpenDialog
    Filter = 'Firebird Database|*.fdb|All Files|*.*'
    Left = 440
    Top = 40
  end
  object SelectDirectoryDialog1: TSelectDirectoryDialog
    Left = 440
    Top = 80
  end
end