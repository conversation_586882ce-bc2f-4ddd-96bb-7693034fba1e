unit ConfigUtils;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, sqlite3conn, sqldb, Logging;

type
  TConfigUtils = class
  private
    FConnection: TSQLite3Connection;
    FTransaction: TSQLTransaction;
    FQuery: TSQLQuery;
    FConfigPath: string;
    FPassword: string;

    procedure InitializeDatabase;
    procedure CreateConfigTables;
    
  public  
    function GetValue(const Section, Name: string; const Default: string = ''): string;
    procedure SetValue(const Section, Name, Value: string);
    function GetInt(const Section, Name: string; Default: Integer = 0): Integer;
    procedure SetInt(const Section, Name: string; Value: Integer);
    function GetFloat(const Section, Name: string; Default: Double = 0): Double;
    procedure SetFloat(const Section, Name: string; Value: Double);
    function GetBool(const Section, Name: string; Default: Boolean = False): Boolean;
    procedure SetBool(const Section, Name: string; Value: Boolean);

  public
    constructor Create;
    destructor Destroy; override;


    // Utility methods
    procedure LoadConfig;
    procedure SaveConfig;
    function IsConfigured: Boolean;
    procedure ResetToDefaults;
    procedure ChangePassword(const ANewPassword: string);

    // Backup and restore
    procedure BackupConfig(const ABackupFile: string);
    procedure RestoreConfig(const ABackupFile: string);
  end;

var
  Config: TConfigUtils;

implementation

uses
  FileUtil, LazFileUtils;

const
  DEFAULT_CONFIG_PASSWORD = 'PoolSnookerPOS2024!';

constructor TConfigUtils.Create;
begin
  inherited Create;

  FConfigPath := ExtractFilePath(Application.ExeName) + 'config.db';
  FPassword := DEFAULT_CONFIG_PASSWORD;

  FConnection := TSQLite3Connection.Create(nil);
  FTransaction := TSQLTransaction.Create(nil);
  FQuery := TSQLQuery.Create(nil);

  try
    InitializeDatabase;
    Logger.LogInfo('ConfigUtils initialized with SQLiteCipher database');
  except
    on E: Exception do
    begin
      Logger.LogError('Failed to initialize ConfigUtils: ' + E.Message);
      raise;
    end;
  end;
end;

destructor TConfigUtils.Destroy;
begin
  try
    if Assigned(FQuery) then
    begin
      FQuery.Close;
      FQuery.Free;
    end;

    if Assigned(FTransaction) then
    begin
      if FTransaction.Active then
        FTransaction.Commit;
      FTransaction.Free;
    end;

    if Assigned(FConnection) then
    begin
      if FConnection.Connected then
        FConnection.Close;
      FConnection.Free;
    end;

    Logger.LogInfo('ConfigUtils destroyed');
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error destroying ConfigUtils: ' + E.Message);
    end;
  end;

  inherited Destroy;
end;

procedure TConfigUtils.InitializeDatabase;
begin
  try
    FConnection.DatabaseName := FConfigPath;
    FConnection.CharSet := 'UTF8';

    // Set SQLiteCipher password
    FConnection.Params.Clear;
    FConnection.Params.Add('Password=' + FPassword);
    FConnection.Params.Add('foreign_keys=ON');
    FConnection.Params.Add('journal_mode=WAL');
    FConnection.Params.Add('synchronous=NORMAL');

    FTransaction.DataBase := FConnection;
    FQuery.DataBase := FConnection;
    FQuery.Transaction := FTransaction;

    FConnection.Open;
    FTransaction.StartTransaction;

    CreateConfigTables;

    FTransaction.Commit;

  except
    on E: Exception do
    begin
      if FTransaction.Active then
        FTransaction.Rollback;
      Logger.LogError('Error initializing config database: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TConfigUtils.CreateConfigTables;
begin
  try
    // Create config table
    FQuery.SQL.Text :=
      'CREATE TABLE IF NOT EXISTS config (' +
      '  id INTEGER PRIMARY KEY AUTOINCREMENT,' +
      '  section TEXT NOT NULL,' +
      '  key_name TEXT NOT NULL,' +
      '  value_text TEXT,' +
      '  value_int INTEGER,' +
      '  value_float REAL,' +
      '  value_bool INTEGER,' +
      '  data_type TEXT NOT NULL,' +
      '  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,' +
      '  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,' +
      '  UNIQUE(section, key_name)' +
      ')';
    FQuery.ExecSQL;

    // Create index for faster lookups
    FQuery.SQL.Text :=
      'CREATE INDEX IF NOT EXISTS idx_config_section_key ON config(section, key_name)';
    FQuery.ExecSQL;

    // Create trigger to update updated_at
    FQuery.SQL.Text :=
      'CREATE TRIGGER IF NOT EXISTS update_config_timestamp ' +
      'AFTER UPDATE ON config ' +
      'BEGIN ' +
      '  UPDATE config SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; ' +
      'END';
    FQuery.ExecSQL;

    Logger.LogInfo('Config database tables created/verified');

  except
    on E: Exception do
    begin
      Logger.LogError('Error creating config tables: ' + E.Message);
      raise;
    end;
  end;
end;

function TConfigUtils.GetValue(const Section, Name: string; const Default: string): string;
begin
  Result := Default;
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'SELECT value_text FROM config WHERE section = :section AND key_name = :key_name';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.Open;
    if not FQuery.EOF then Result := FQuery.FieldByName('value_text').AsString;
    FQuery.Close;
  except
    on E: Exception do
      Logger.LogError('Error getting config value [' + Section + '.' + Name + ']: ' + E.Message);
  end;
end;

procedure TConfigUtils.SetValue(const Section, Name, Value: string);
begin
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'INSERT OR REPLACE INTO config (section, key_name, value_text, data_type) VALUES (:section, :key_name, :value_text, ''STRING'')';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.ParamByName('value_text').AsString := Value;
    FQuery.ExecSQL;
    FTransaction.Commit;
  except
    on E: Exception do
    begin
      if FTransaction.Active then FTransaction.Rollback;
      Logger.LogError('Error setting config value [' + Section + '.' + Name + ']: ' + E.Message);
      raise;
    end;
  end;
end;

function TConfigUtils.GetInt(const Section, Name: string; Default: Integer): Integer;
begin
  Result := Default;
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'SELECT value_int FROM config WHERE section = :section AND key_name = :key_name';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.Open;
    if not FQuery.EOF then Result := FQuery.FieldByName('value_int').AsInteger;
    FQuery.Close;
  except
    on E: Exception do
      Logger.LogError('Error getting config int value [' + Section + '.' + Name + ']: ' + E.Message);
  end;
end;

procedure TConfigUtils.SetInt(const Section, Name: string; Value: Integer);
begin
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'INSERT OR REPLACE INTO config (section, key_name, value_int, data_type) VALUES (:section, :key_name, :value_int, ''INTEGER'')';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.ParamByName('value_int').AsInteger := Value;
    FQuery.ExecSQL;
    FTransaction.Commit;
  except
    on E: Exception do
    begin
      if FTransaction.Active then FTransaction.Rollback;
      Logger.LogError('Error setting config int value [' + Section + '.' + Name + ']: ' + E.Message);
      raise;
    end;
  end;
end;

function TConfigUtils.GetFloat(const Section, Name: string; Default: Double): Double;
begin
  Result := Default;
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'SELECT value_float FROM config WHERE section = :section AND key_name = :key_name';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.Open;
    if not FQuery.EOF then Result := FQuery.FieldByName('value_float').AsFloat;
    FQuery.Close;
  except
    on E: Exception do
      Logger.LogError('Error getting config float value [' + Section + '.' + Name + ']: ' + E.Message);
  end;
end;

procedure TConfigUtils.SetFloat(const Section, Name: string; Value: Double);
begin
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'INSERT OR REPLACE INTO config (section, key_name, value_float, data_type) VALUES (:section, :key_name, :value_float, ''FLOAT'')';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.ParamByName('value_float').AsFloat := Value;
    FQuery.ExecSQL;
    FTransaction.Commit;
  except
    on E: Exception do
    begin
      if FTransaction.Active then FTransaction.Rollback;
      Logger.LogError('Error setting config float value [' + Section + '.' + Name + ']: ' + E.Message);
      raise;
    end;
  end;
end;

function TConfigUtils.GetBool(const Section, Name: string; Default: Boolean): Boolean;
begin
  Result := Default;
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'SELECT value_bool FROM config WHERE section = :section AND key_name = :key_name';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.Open;
    if not FQuery.EOF then Result := FQuery.FieldByName('value_bool').AsInteger = 1;
    FQuery.Close;
  except
    on E: Exception do
      Logger.LogError('Error getting config bool value [' + Section + '.' + Name + ']: ' + E.Message);
  end;
end;

procedure TConfigUtils.SetBool(const Section, Name: string; Value: Boolean);
begin
  try
    if not FConnection.Connected then FConnection.Open;
    if not FTransaction.Active then FTransaction.StartTransaction;
    FQuery.Close;
    FQuery.SQL.Text := 'INSERT OR REPLACE INTO config (section, key_name, value_bool, data_type) VALUES (:section, :key_name, :value_bool, ''BOOLEAN'')';
    FQuery.ParamByName('section').AsString := Section;
    FQuery.ParamByName('key_name').AsString := Name;
    FQuery.ParamByName('value_bool').AsInteger := Ord(Value);
    FQuery.ExecSQL;
    FTransaction.Commit;
  except
    on E: Exception do
    begin
      if FTransaction.Active then FTransaction.Rollback;
      Logger.LogError('Error setting config bool value [' + Section + '.' + Name + ']: ' + E.Message);
      raise;
    end;
  end;
end;


// Utility methods
procedure TConfigUtils.LoadConfig;
begin
  try
    // Check if config database exists and has data
    if not FileExists(FConfigPath) then
    begin
      Logger.LogInfo('Config database not found, creating with defaults');
      ResetToDefaults;
    end
    else
    begin
      Logger.LogInfo('Config database loaded from: ' + FConfigPath);
    end;
  except
    on E: Exception do
    begin
      Logger.LogError('Error loading config: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TConfigUtils.SaveConfig;
begin
  try
    if FTransaction.Active then
      FTransaction.Commit;
    Logger.LogInfo('Configuration saved to database');
  except
    on E: Exception do
    begin
      if FTransaction.Active then
        FTransaction.Rollback;
      Logger.LogError('Error saving config: ' + E.Message);
      raise;
    end;
  end;
end;

function TConfigUtils.IsConfigured: Boolean;
begin
  Result := False;

  try
    if not FileExists(FConfigPath) then
      Exit;

    if not FConnection.Connected then
      FConnection.Open;

    if not FTransaction.Active then
      FTransaction.StartTransaction;

    FQuery.Close;
    FQuery.SQL.Text := 'SELECT COUNT(*) as config_count FROM config';
    FQuery.Open;

    Result := FQuery.FieldByName('config_count').AsInteger > 0;

    FQuery.Close;

  except
    on E: Exception do
    begin
      Logger.LogError('Error checking if configured: ' + E.Message);
      Result := False;
    end;
  end;
end;

procedure TConfigUtils.ResetToDefaults;
begin
  try
    if not FConnection.Connected then
      FConnection.Open;

    if not FTransaction.Active then
      FTransaction.StartTransaction;

    // Clear existing config
    FQuery.Close;
    FQuery.SQL.Text := 'DELETE FROM config';
    FQuery.ExecSQL;

    // Set default values
    // Database defaults
    SetValue('Database', 'Host', 'localhost');
    SetInt('Database', 'Port', 3050);
    SetValue('Database', 'Path', ExtractFilePath(Application.ExeName) + 'snpPOS.fdb');
    SetValue('Database', 'User', 'SYSDBA');
    SetValue('Database', 'Password', 'masterkey');
    SetValue('Database', 'CharSet', 'UTF8');

    // Company defaults
    SetValue('Company', 'Name', 'Pool & Snooker Center');
    SetValue('Company', 'Address', '123 Main Street, City, State');
    SetValue('Company', 'Phone', '(*************');
    SetValue('Company', 'Email', '<EMAIL>');
    SetValue('Company', 'TaxNumber', '');

    // Application defaults
    SetValue('Application', 'ReceiptPrinter', '');
    SetValue('Application', 'ReportPrinter', '');
    SetValue('Application', 'LogLevel', 'INFO');
    SetValue('Application', 'LogFile', 'poolsnooker.log');
    SetValue('Application', 'BackupPath', ExtractFilePath(Application.ExeName) + 'backups');
    SetInt('Application', 'ReceiptWidth', 40);
    SetInt('Application', 'ReceiptCopies', 1);
    SetBool('Application', 'AutoPrint', True);
    SetValue('Application', 'Currency', '$');
    SetBool('Application', 'EnableLogging', True);
    SetBool('Application', 'AutoBackup', True);

    // Shift defaults
    SetValue('Shifts', 'MorningStart', '06:00');
    SetValue('Shifts', 'MorningEnd', '18:00');
    SetValue('Shifts', 'EveningStart', '18:00');
    SetValue('Shifts', 'EveningEnd', '06:00');

    // Tax defaults
    SetFloat('Tax', 'TaxRate', 0.0);
    SetValue('Tax', 'TaxName', 'Tax');
    SetBool('Tax', 'TaxEnabled', False);
    SetBool('Tax', 'TaxInclusive', False);

    // Security defaults
    SetInt('Security', 'MinPasswordLength', 6);
    SetBool('Security', 'RequireUppercase', False);
    SetBool('Security', 'RequireNumbers', False);
    SetBool('Security', 'RequireSpecialChars', False);
    SetInt('Security', 'SessionTimeout', 60);
    SetBool('Security', 'ForcePasswordChange', False);

    FTransaction.Commit;

    Logger.LogInfo('Configuration reset to defaults');

  except
    on E: Exception do
    begin
      if FTransaction.Active then
        FTransaction.Rollback;
      Logger.LogError('Error resetting config to defaults: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TConfigUtils.ChangePassword(const ANewPassword: string);
var
  OldConnection: TSQLite3Connection;
  OldTransaction: TSQLTransaction;
  OldQuery: TSQLQuery;
  TempFile: string;
begin
  try
    if Trim(ANewPassword) = '' then
      raise Exception.Create('Password cannot be empty');

    TempFile := FConfigPath + '.tmp';

    // Create new connection with new password
    OldConnection := TSQLite3Connection.Create(nil);
    OldTransaction := TSQLTransaction.Create(nil);
    OldQuery := TSQLQuery.Create(nil);

    try
      // Setup new connection
      OldConnection.DatabaseName := TempFile;
      OldConnection.CharSet := 'UTF8';
      OldConnection.Params.Clear;
      OldConnection.Params.Add('Password=' + ANewPassword);
      OldConnection.Params.Add('foreign_keys=ON');
      OldConnection.Params.Add('journal_mode=WAL');
      OldConnection.Params.Add('synchronous=NORMAL');

      OldTransaction.DataBase := OldConnection;
      OldQuery.DataBase := OldConnection;
      OldQuery.Transaction := OldTransaction;

      OldConnection.Open;
      OldTransaction.StartTransaction;

      // Create tables in new database
      OldQuery.SQL.Text :=
        'CREATE TABLE config (' +
        '  id INTEGER PRIMARY KEY AUTOINCREMENT,' +
        '  section TEXT NOT NULL,' +
        '  key_name TEXT NOT NULL,' +
        '  value_text TEXT,' +
        '  value_int INTEGER,' +
        '  value_float REAL,' +
        '  value_bool INTEGER,' +
        '  data_type TEXT NOT NULL,' +
        '  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,' +
        '  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,' +
        '  UNIQUE(section, key_name)' +
        ')';
      OldQuery.ExecSQL;

      // Copy data from old database
      if not FConnection.Connected then
        FConnection.Open;

      if not FTransaction.Active then
        FTransaction.StartTransaction;

      FQuery.Close;
      FQuery.SQL.Text := 'SELECT * FROM config';
      FQuery.Open;

      while not FQuery.EOF do
      begin
        OldQuery.Close;
        OldQuery.SQL.Text :=
          'INSERT INTO config (section, key_name, value_text, value_int, value_float, value_bool, data_type) ' +
          'VALUES (:section, :key_name, :value_text, :value_int, :value_float, :value_bool, :data_type)';
        OldQuery.ParamByName('section').AsString := FQuery.FieldByName('section').AsString;
        OldQuery.ParamByName('key_name').AsString := FQuery.FieldByName('key_name').AsString;
        OldQuery.ParamByName('value_text').AsString := FQuery.FieldByName('value_text').AsString;
        OldQuery.ParamByName('value_int').AsInteger := FQuery.FieldByName('value_int').AsInteger;
        OldQuery.ParamByName('value_float').AsFloat := FQuery.FieldByName('value_float').AsFloat;
        OldQuery.ParamByName('value_bool').AsInteger := FQuery.FieldByName('value_bool').AsInteger;
        OldQuery.ParamByName('data_type').AsString := FQuery.FieldByName('data_type').AsString;
        OldQuery.ExecSQL;

        FQuery.Next;
      end;

      FQuery.Close;
      OldTransaction.Commit;

      // Close old connection
      if FTransaction.Active then
        FTransaction.Commit;
      FConnection.Close;

      // Close new connection
      OldConnection.Close;

      // Replace old file with new file
      if FileExists(FConfigPath) then
        DeleteFile(FConfigPath);

      RenameFile(TempFile, FConfigPath);

      // Update password and reconnect
      FPassword := ANewPassword;
      FConnection.Params.Clear;
      FConnection.Params.Add('Password=' + FPassword);
      FConnection.Params.Add('foreign_keys=ON');
      FConnection.Params.Add('journal_mode=WAL');
      FConnection.Params.Add('synchronous=NORMAL');

      FConnection.Open;

      Logger.LogInfo('Config database password changed successfully');

    finally
      OldQuery.Free;
      OldTransaction.Free;
      OldConnection.Free;

      // Clean up temp file if it exists
      if FileExists(TempFile) then
        DeleteFile(TempFile);
    end;

  except
    on E: Exception do
    begin
      Logger.LogError('Error changing config password: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TConfigUtils.BackupConfig(const ABackupFile: string);
var
  BackupPath: string;
begin
  try
    BackupPath := ExtractFilePath(ABackupFile);
    if not DirectoryExists(BackupPath) then
      ForceDirectories(BackupPath);

    // Close connection to allow file copy
    if FConnection.Connected then
      FConnection.Close;

    // Copy config database
    if FileExists(FConfigPath) then
    begin
      CopyFile(FConfigPath, ABackupFile);
      Logger.LogInfo('Config backup created: ' + ABackupFile);
    end
    else
    begin
      raise Exception.Create('Config database file not found: ' + FConfigPath);
    end;

    // Reconnect
    FConnection.Open;

  except
    on E: Exception do
    begin
      Logger.LogError('Error backing up config: ' + E.Message);
      // Try to reconnect
      try
        if not FConnection.Connected then
          FConnection.Open;
      except
        // Ignore reconnection errors
      end;
      raise;
    end;
  end;
end;

procedure TConfigUtils.RestoreConfig(const ABackupFile: string);
begin
  try
    if not FileExists(ABackupFile) then
      raise Exception.Create('Backup file not found: ' + ABackupFile);

    // Close connection
    if FConnection.Connected then
      FConnection.Close;

    // Backup current config if it exists
    if FileExists(FConfigPath) then
    begin
      CopyFile(FConfigPath, FConfigPath + '.bak');
      DeleteFile(FConfigPath);
    end;

    // Restore from backup
    CopyFile(ABackupFile, FConfigPath);

    // Reconnect
    FConnection.Open;

    Logger.LogInfo('Config restored from backup: ' + ABackupFile);

  except
    on E: Exception do
    begin
      Logger.LogError('Error restoring config: ' + E.Message);

      // Try to restore from .bak file if restore failed
      if FileExists(FConfigPath + '.bak') then
      begin
        try
          if FileExists(FConfigPath) then
            DeleteFile(FConfigPath);
          RenameFile(FConfigPath + '.bak', FConfigPath);
          FConnection.Open;
          Logger.LogInfo('Config restored from backup file');
        except
          on E2: Exception do
          begin
            Logger.LogError('Failed to restore from backup: ' + E2.Message);
          end;
        end;
      end;

      raise;
    end;
  end;
end;

initialization
  Config := TConfigUtils.Create;

finalization
  if Assigned(Config) then
    Config.Free;

end.
