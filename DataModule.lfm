object dmMain: TdmMain
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  OldCreateOrder = False
  Height = 480
  HorizontalOffset = 686
  VerticalOffset = 124
  Width = 640
  object conFirebird: TSQLConnector
    Connected = False
    LoginPrompt = False
    KeepConnection = False
    Transaction = transMain
    ConnectorType = 'Firebird'
    Left = 56
    Top = 24
  end
  object transMain: TSQLTransaction
    Active = False
    Database = conFirebird
    Left = 56
    Top = 80
  end
  object qryUsers: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 24
  end
  object qryItems: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 80
  end
  object qryCategories: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 136
  end
  object qrySales: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 192
  end
  object qrySaleItems: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 248
  end
  object qryCashTransactions: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 304
  end
  object qryShifts: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 360
  end
  object qryGeneral: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 152
    Top = 416
  end
  object qryReports: TSQLQuery
    FieldDefs = <>
    Database = conFirebird
    Transaction = transMain
    SQL.Strings = (
      ''
    )
    Params = <>
    Macros = <>
    Left = 248
    Top = 24
  end
  object dsUsers: TDataSource
    DataSet = qryUsers
    Left = 344
    Top = 24
  end
  object dsItems: TDataSource
    DataSet = qryItems
    Left = 344
    Top = 80
  end
  object dsCategories: TDataSource
    DataSet = qryCategories
    Left = 344
    Top = 136
  end
  object dsSales: TDataSource
    DataSet = qrySales
    Left = 344
    Top = 192
  end
  object dsSaleItems: TDataSource
    DataSet = qrySaleItems
    Left = 344
    Top = 248
  end
  object dsCashTransactions: TDataSource
    DataSet = qryCashTransactions
    Left = 344
    Top = 304
  end
  object dsShifts: TDataSource
    DataSet = qryShifts
    Left = 344
    Top = 360
  end
  object dsGeneral: TDataSource
    DataSet = qryGeneral
    Left = 344
    Top = 416
  end
  object dsReports: TDataSource
    DataSet = qryReports
    Left = 440
    Top = 24
  end
end
