unit DataModule;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, DB, SQLDB, IBConnection, Forms, Controls,
  Printers, Dialogs, Logging, FileUtil;

type

  { TdmMain }

  TdmMain = class(TDataModule)
    conFirebird: TSQLConnector;
    transMain: TSQLTransaction;
    qryUsers: TSQLQuery;
    qryItems: TSQLQuery;
    qryCategories: TSQLQuery;
    qrySales: TSQLQuery;
    qrySaleItems: TSQLQuery;
    qryCashTransactions: TSQLQuery;
    qryShifts: TSQLQuery;
    qryGeneral: TSQLQuery;
    qryReports: TSQLQuery;

    dsUsers: TDataSource;
    dsItems: TDataSource;
    dsCategories: TDataSource;
    dsSales: TDataSource;
    dsSaleItems: TDataSource;
    dsCashTransactions: TDataSource;
    dsShifts: TDataSource;
    dsGeneral: TDataSource;
    dsReports: TDataSource;

    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
    
  private

  public
    // POS Methods
    function GetPOSCategories: TSQLQuery;
    function GetPOSItemsByCategory(CategoryID: Integer): TSQLQuery;
    function GetPOSItemDetails(ItemID: Integer): Boolean;
    function FastSearchItems(const SearchTerm: string): TSQLQuery;
    
    // User management
    function AuthenticateUser(const Username, Password: string): Boolean;
    function GetUserInfo(const Username: string): Boolean;
    function CreateUser(const Username, Password, FullName, Role: string): Boolean;
    function UpdateUser(UserID: Integer; const Username, FullName, Role: string): Boolean;
    function ChangeUserPassword(UserID: Integer; const NewPassword: string): Boolean;
    function DeleteUser(UserID: Integer): Boolean;
    function GetUsers: Boolean;
    
    // Category management
    function GetCategories: Boolean;
    function GetCategoryList: TStringList;
    function GetCategoryByID(CategoryID: Integer): Boolean;
    function GetCategoryByName(const CategoryName: string): Integer;
    function AddCategory(const CategoryName, Description: string): Boolean;
    function UpdateCategory(CategoryID: Integer; const CategoryName, Description: string): Boolean;
    function DeleteCategory(CategoryID: Integer): Boolean;
    function SetCategoryPicture(CategoryID: Integer; PictureData: TStream): Boolean;
    function GetCategoryPicture(CategoryID: Integer; PictureData: TStream): Boolean;
    
    // Item management
    function GetItems(CategoryID: Integer = 0): Boolean;
    function GetItemByCode(const ItemCode: string): Boolean;
    function GetItemByBarcode(const Barcode: string): Boolean;
    function SearchItems(const SearchTerm: string): Boolean;
    function AddItem(const ItemCode, ItemName: string; CategoryID: Integer; UnitPrice: Currency; const Barcode: string = ''): Boolean;
    function UpdateItem(ItemID: Integer; const ItemCode, ItemName: string; CategoryID: Integer; UnitPrice: Currency; const Barcode: string = ''): Boolean;
    function DeleteItem(ItemID: Integer): Boolean;
    function SetItemPicture(ItemID: Integer; PictureData: TStream): Boolean;
    function GetItemPicture(ItemID: Integer; PictureData: TStream): Boolean;
    function UpdateItemStock(ItemID: Integer; QuantitySold: Integer): Boolean;
    function CheckStockAvailability(ItemID: Integer; RequiredQty: Integer): Boolean;
    function GetItemStock(ItemID: Integer): Integer;

    // Sales management
    function CreateSale(const ReceiptNo: string; UserID: Integer; const ShiftType: string; 
                       Subtotal, Discount, Total: Currency): Integer;
    function AddSaleItem(SaleID, ItemID, Quantity: Integer; UnitPrice: Currency): Boolean;
    function UpdateSalePayment(SaleID: Integer; const PaymentMethod: string; 
                              AmountReceived, ChangeAmount: Currency;
                              const CardNumber, AuthCode, QRCode, QRReference: string): Boolean;
    function GetSaleDetails(SaleID: Integer): Boolean;
    function GetSaleItems(SaleID: Integer): Boolean;
    function GetNextReceiptNumber: string;

    // Cash reporting methods
    function GetCashSalesTotal(ADate: TDateTime; const ShiftType: string): Currency;
    function GetCashInTotal(ADate: TDateTime; const ShiftType: string): Currency;  
    function GetCashOutTotal(ADate: TDateTime; const ShiftType: string): Currency;
    function GetSalesData(ADate: TDateTime): Boolean;
   
    // Cash management
    function AddCashTransaction(UserID: Integer; const TransactionType: string; 
                               Amount: Currency; const Description: string; 
                               SaleID: Integer; const ShiftType: string): Boolean;
    function GetCashBalance(const ShiftType: string; TransactionDate: TDate): Currency;
    function GetCashTransactions(const ShiftType: string; TransactionDate: TDate): Boolean;
    
    // Shift management
    function StartShift(UserID: Integer; const ShiftType: string; OpeningCash: Currency): Integer;
    function EndShift(ShiftID: Integer; ClosingCash: Currency; const Notes: string): Boolean;
    function GetCurrentShift(const ShiftType: string): Integer;
    function GetShiftSummary(ShiftID: Integer): Boolean;
    function GetShiftReport(ShiftID: Integer): Boolean;
    function GetAllShifts: Boolean;
    function GetShiftSales(ShiftID: Integer): Boolean;
    
    // Reports
    function GetDailySalesReport(DateFrom, DateTo: TDate; const UserFilter, ShiftFilter: string): Boolean;
    function GetItemSalesReport(DateFrom, DateTo: TDate; const UserFilter, ShiftFilter: string): Boolean;
    function GetUserSalesReport(DateFrom, DateTo: TDate; const ShiftFilter: string): Boolean;
  end;

var
  dmMain: TdmMain;

implementation

uses
  HashUtils, ConfigUtils;

{$R *.lfm}

procedure TdmMain.DataModuleCreate(Sender: TObject);
begin
  try
    // Configure Firebird connection with performance optimizations
    conFirebird.ConnectorType := 'Firebird';
    conFirebird.DatabaseName := ExtractFilePath(Application.ExeName) + 'snpPOS.fdb';
    conFirebird.UserName := 'SYSDBA';
    conFirebird.Password := 'masterkey';
    conFirebird.CharSet := 'UTF8';

    // Performance optimization parameters
    conFirebird.Params.Clear;
    conFirebird.Params.Add('page_size=8192');           // Optimal page size for POS operations
    conFirebird.Params.Add('default_dbkey_scope=1');    // Session-level DBKEY scope
    conFirebird.Params.Add('sql_dialect=3');            // Use SQL dialect 3
    conFirebird.Params.Add('lc_ctype=UTF8');            // Character set
    conFirebird.Params.Add('cache_manager=1');          // Enable cache manager

    conFirebird.Connected := True;

    // Configure transaction with optimized settings
    transMain.Database := conFirebird;
    transMain.Params.Clear;
    transMain.Params.Add('read_committed');             // Read committed isolation
    transMain.Params.Add('rec_version');                // Record version for better concurrency
    transMain.Params.Add('nowait');                     // Don't wait for locks

    // Configure all queries with prepared statement optimization
    qryUsers.Database := conFirebird;
    qryUsers.Transaction := transMain;
    qryUsers.Options := qryUsers.Options + [sqoKeepOpenOnCommit]; // Keep prepared

    qryItems.Database := conFirebird;
    qryItems.Transaction := transMain;
    qryItems.Options := qryItems.Options + [sqoKeepOpenOnCommit];

    qryCategories.Database := conFirebird;
    qryCategories.Transaction := transMain;
    qryCategories.Options := qryCategories.Options + [sqoKeepOpenOnCommit];

    qrySales.Database := conFirebird;
    qrySales.Transaction := transMain;
    qrySales.Options := qrySales.Options + [sqoKeepOpenOnCommit];

    qrySaleItems.Database := conFirebird;
    qrySaleItems.Transaction := transMain;
    qrySaleItems.Options := qrySaleItems.Options + [sqoKeepOpenOnCommit];

    qryCashTransactions.Database := conFirebird;
    qryCashTransactions.Transaction := transMain;
    qryCashTransactions.Options := qryCashTransactions.Options + [sqoKeepOpenOnCommit];

    qryShifts.Database := conFirebird;
    qryShifts.Transaction := transMain;
    qryShifts.Options := qryShifts.Options + [sqoKeepOpenOnCommit];

    qryGeneral.Database := conFirebird;
    qryGeneral.Transaction := transMain;

    qryReports.Database := conFirebird;
    qryReports.Transaction := transMain;

    // Configure DataSources
    dsUsers.DataSet := qryUsers;
    dsItems.DataSet := qryItems;
    dsCategories.DataSet := qryCategories;
    dsSales.DataSet := qrySales;
    dsSaleItems.DataSet := qrySaleItems;
    dsCashTransactions.DataSet := qryCashTransactions;
    dsShifts.DataSet := qryShifts;
    dsGeneral.DataSet := qryGeneral;
    dsReports.DataSet := qryReports;

    transMain.Active := True;

    if Assigned(Logger) then
      Logger.LogInfo('DataModule created and connected successfully with performance optimizations');

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('DataModule creation error: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TdmMain.DataModuleDestroy(Sender: TObject);
begin
  try
    if transMain.Active then
      transMain.Active := False;
    if conFirebird.Connected then
      conFirebird.Connected := False;
      
    if Assigned(Logger) then
      Logger.LogInfo('DataModule destroyed and connection released');
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('DataModule destruction error: ' + E.Message);
    end;
  end;
end;

// User management functions
function TdmMain.AuthenticateUser(const Username, Password: string): Boolean;
begin
  Result := False;
  
  try
    qryUsers.SQL.Clear;
    qryUsers.SQL.Add('SELECT ID, PASSWORD_HASH FROM USERS WHERE USERNAME = :username AND IS_ACTIVE = 1');
    qryUsers.ParamByName('username').AsString := Username;
    qryUsers.Open;
    
    if not qryUsers.EOF then
    begin
      if VerifyPassword(Password, qryUsers.FieldByName('PASSWORD_HASH').AsString) then
      begin
        // Update last login
        qryGeneral.SQL.Clear;
        qryGeneral.SQL.Add('UPDATE USERS SET CREATED_DATE = CURRENT_TIMESTAMP WHERE USERNAME = :username');
        qryGeneral.ParamByName('username').AsString := Username;
        qryGeneral.ExecSQL;
        transMain.Commit;
        
        Result := True;
      end;
    end;
    
    qryUsers.Close;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('AuthenticateUser error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetUserInfo(const Username: string): Boolean;
begin
  Result := False;
  
  try
    qryUsers.SQL.Clear;
    qryUsers.SQL.Add('SELECT * FROM USERS WHERE USERNAME = :username AND IS_ACTIVE = 1');
    qryUsers.ParamByName('username').AsString := Username;
    qryUsers.Open;
    
    Result := not qryUsers.EOF;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetUserInfo error: ' + E.Message);
    end;
  end;
end;

function TdmMain.CreateUser(const Username, Password, FullName, Role: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO USERS (USERNAME, PASSWORD_HASH, FULL_NAME, ROLE) VALUES (');
    qryGeneral.SQL.Add(':username, :password_hash, :full_name, :role)');
    qryGeneral.ParamByName('username').AsString := Username;
    qryGeneral.ParamByName('password_hash').AsString := HashPassword(Password);
    qryGeneral.ParamByName('full_name').AsString := FullName;
    qryGeneral.ParamByName('role').AsString := Role;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('CreateUser error: ' + E.Message);
    end;
  end;
end;


function TdmMain.UpdateUser(UserID: Integer; const Username, FullName, Role: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE USERS SET USERNAME = :username, FULL_NAME = :full_name, ROLE = :role WHERE ID = :id');
    qryGeneral.ParamByName('username').AsString := Username;
    qryGeneral.ParamByName('full_name').AsString := FullName;
    qryGeneral.ParamByName('role').AsString := Role;
    qryGeneral.ParamByName('id').AsInteger := UserID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('UpdateUser error: ' + E.Message);
    end;
  end;
end;


function TdmMain.ChangeUserPassword(UserID: Integer; const NewPassword: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE USERS SET PASSWORD_HASH = :password_hash WHERE ID = :id');
    qryGeneral.ParamByName('password_hash').AsString := HashPassword(NewPassword);
    qryGeneral.ParamByName('id').AsInteger := UserID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('ChangeUserPassword error: ' + E.Message);
    end;
  end;
end;

function TdmMain.DeleteUser(UserID: Integer): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE USERS SET IS_ACTIVE = 0 WHERE ID = :id');
    qryGeneral.ParamByName('id').AsInteger := UserID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('DeleteUser error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetUsers: Boolean;
begin
  Result := False;
  
  try
    qryUsers.SQL.Clear;
    qryUsers.SQL.Add('SELECT * FROM USERS WHERE IS_ACTIVE = 1 ORDER BY USERNAME');
    qryUsers.Open;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetUsers error: ' + E.Message);
    end;
  end;
end;

// Category management functions
function TdmMain.GetCategories: Boolean;
begin
  Result := False;
  try
    qryCategories.Close;
    qryCategories.SQL.Clear;
    qryCategories.SQL.Add('SELECT ID, CATEGORY_NAME, DESCRIPTION, IS_ACTIVE, CREATED_DATE FROM CATEGORIES WHERE IS_ACTIVE = 1 ORDER BY CATEGORY_NAME');
    qryCategories.Open;
    
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetCategories successful, Count: ' + IntToStr(qryCategories.RecordCount));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCategories error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetCategoryList: TStringList;
begin
  Result := TStringList.Create;
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT ID, CATEGORY_NAME FROM CATEGORIES WHERE IS_ACTIVE = 1 ORDER BY CATEGORY_NAME');
    qryGeneral.Open;
    
    while not qryGeneral.EOF do
    begin
      // Fix: Use PtrInt to properly cast integer to pointer
      Result.AddObject(qryGeneral.FieldByName('CATEGORY_NAME').AsString, 
                      TObject(PtrInt(qryGeneral.FieldByName('ID').AsInteger)));
      qryGeneral.Next;
    end;
    
    qryGeneral.Close;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetCategoryList successful, Count: ' + IntToStr(Result.Count));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCategoryList error: ' + E.Message);
      FreeAndNil(Result);
    end;
  end;
end;


function TdmMain.GetCategoryByID(CategoryID: Integer): Boolean;
begin
  Result := False;
  try
    qryCategories.Close;
    qryCategories.SQL.Clear;
    qryCategories.SQL.Add('SELECT * FROM CATEGORIES WHERE ID = :id AND IS_ACTIVE = 1');
    qryCategories.ParamByName('id').AsInteger := CategoryID;
    qryCategories.Open;
    
    Result := not qryCategories.EOF;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCategoryByID error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetCategoryByName(const CategoryName: string): Integer;
begin
  Result := 0;
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT ID FROM CATEGORIES WHERE CATEGORY_NAME = :name AND IS_ACTIVE = 1');
    qryGeneral.ParamByName('name').AsString := CategoryName;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('ID').AsInteger;
      
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCategoryByName error: ' + E.Message);
      Result := 0;
    end;
  end;
end;

function TdmMain.AddCategory(const CategoryName, Description: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO CATEGORIES (CATEGORY_NAME, DESCRIPTION) VALUES (:category_name, :description)');
    qryGeneral.ParamByName('category_name').AsString := CategoryName;
    qryGeneral.ParamByName('description').AsString := Description;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('AddCategory error: ' + E.Message);
    end;
  end;
end;

function TdmMain.UpdateCategory(CategoryID: Integer; const CategoryName, Description: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE CATEGORIES SET CATEGORY_NAME = :category_name, DESCRIPTION = :description WHERE ID = :id');
    qryGeneral.ParamByName('category_name').AsString := CategoryName;
    qryGeneral.ParamByName('description').AsString := Description;
    qryGeneral.ParamByName('id').AsInteger := CategoryID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('UpdateCategory error: ' + E.Message);
    end;
  end;
end;

function TdmMain.DeleteCategory(CategoryID: Integer): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE CATEGORIES SET IS_ACTIVE = 0 WHERE ID = :id');
    qryGeneral.ParamByName('id').AsInteger := CategoryID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('DeleteCategory error: ' + E.Message);
    end;
  end;
end;

function TdmMain.SetCategoryPicture(CategoryID: Integer; PictureData: TStream): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE CATEGORIES SET PICTURE = :picture WHERE ID = :id');
    qryGeneral.ParamByName('id').AsInteger := CategoryID;
    
    PictureData.Position := 0;
    qryGeneral.ParamByName('picture').LoadFromStream(PictureData, ftBlob);
    
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('SetCategoryPicture error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCategoryPicture(CategoryID: Integer; PictureData: TStream): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT PICTURE FROM CATEGORIES WHERE ID = :id AND IS_ACTIVE = 1');
    qryGeneral.ParamByName('id').AsInteger := CategoryID;
    qryGeneral.Open;
    
    if not qryGeneral.EOF and not qryGeneral.FieldByName('PICTURE').IsNull then
    begin
      PictureData.Position := 0;
      TBlobField(qryGeneral.FieldByName('PICTURE')).SaveToStream(PictureData);
      PictureData.Position := 0;
      Result := True;
    end;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCategoryPicture error: ' + E.Message);
    end;
  end;
end;

// Item management functions
function TdmMain.GetItems(CategoryID: Integer = 0): Boolean;
begin
  Result := False;
  try
    qryItems.Close;
    qryItems.SQL.Clear;
    
    if CategoryID = 0 then
    begin
      // Get all active items with category names
      qryItems.SQL.Add('SELECT i.*, c.CATEGORY_NAME ');
      qryItems.SQL.Add('FROM ITEMS i');
      qryItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID');
      qryItems.SQL.Add('WHERE i.IS_ACTIVE = 1');
      qryItems.SQL.Add('ORDER BY i.ITEM_NAME');
    end
    else
    begin
      // Get items filtered by category
      qryItems.SQL.Add('SELECT i.*, c.CATEGORY_NAME ');
      qryItems.SQL.Add('FROM ITEMS i');
      qryItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID');
      qryItems.SQL.Add('WHERE i.IS_ACTIVE = 1 AND i.CATEGORY_ID = :category_id');
      qryItems.SQL.Add('ORDER BY i.ITEM_NAME');
      qryItems.ParamByName('category_id').AsInteger := CategoryID;
    end;
    
    qryItems.Open;
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetItems successful, RecordCount: ' + IntToStr(qryItems.RecordCount));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetItems error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetItemByCode(const ItemCode: string): Boolean;
begin
  Result := False;
  try
    qryItems.Close;
    qryItems.SQL.Clear;
    qryItems.SQL.Add('SELECT i.ID, i.ITEM_CODE, i.ITEM_NAME, i.CATEGORY_ID, c.CATEGORY_NAME, i.UNIT_PRICE');
    qryItems.SQL.Add('FROM ITEMS i');
    qryItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID');
    qryItems.SQL.Add('WHERE i.IS_ACTIVE = 1 AND i.ITEM_CODE = :itemcode');
    qryItems.ParamByName('itemcode').AsString := ItemCode;
    qryItems.Open;
    
    Result := not qryItems.EOF;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetItemByCode error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetItemByBarcode(const Barcode: string): Boolean;
begin
  Result := False;
  try
    qryItems.Close;
    qryItems.SQL.Clear;
    qryItems.SQL.Add('SELECT i.ID, i.ITEM_CODE, i.ITEM_NAME, i.CATEGORY_ID, c.CATEGORY_NAME, i.UNIT_PRICE');
    qryItems.SQL.Add('FROM ITEMS i');
    qryItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID');
    qryItems.SQL.Add('WHERE i.IS_ACTIVE = 1 AND i.ITEM_CODE = :barcode'); // Assuming barcode is stored in item_code
    qryItems.ParamByName('barcode').AsString := Barcode;
    qryItems.Open;
    
    Result := not qryItems.EOF;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetItemByBarcode error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.SearchItems(const SearchTerm: string): Boolean;
begin
  Result := False;
  
  try
    // Ensure query is closed
    if qryItems.Active then
      qryItems.Close;
      
    qryItems.SQL.Clear;
    
    if Trim(SearchTerm) = '' then
    begin
      // Load all items if no search term
      qryItems.SQL.Add('SELECT i.*, c.CATEGORY_NAME FROM ITEMS i ');
      qryItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID ');
      qryItems.SQL.Add('WHERE i.IS_ACTIVE = 1 ORDER BY i.ITEM_NAME');
    end
    else
    begin
      // Search with term
qryItems.SQL.Add('SELECT i.*, c.CATEGORY_NAME FROM ITEMS i ');
qryItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID ');
qryItems.SQL.Add('WHERE (UPPER(c.CATEGORY_NAME) LIKE UPPER(:search) ');
qryItems.SQL.Add('   OR UPPER(i.ITEM_CODE) LIKE UPPER(:search) ');
qryItems.SQL.Add('   OR UPPER(i.ITEM_NAME) LIKE UPPER(:search)) ');
qryItems.SQL.Add('AND i.IS_ACTIVE = 1 ');
qryItems.SQL.Add('ORDER BY i.ITEM_NAME');
qryItems.ParamByName('search').AsString := '%' + Trim(SearchTerm) + '%';

    end;
    
    qryItems.Open;
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('SearchItems executed successfully. Term: "' + SearchTerm + '", Results: ' + IntToStr(qryItems.RecordCount));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('SearchItems error: ' + E.Message + ', SearchTerm: "' + SearchTerm + '"');
      Result := False;
    end;
  end;
end;


function TdmMain.AddItem(const ItemCode, ItemName: string; CategoryID: Integer; UnitPrice: Currency; const Barcode: string = ''): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY_ID, UNIT_PRICE) VALUES (');
    qryGeneral.SQL.Add(':item_code, :item_name, :category_id, :unit_price)');
    qryGeneral.ParamByName('item_code').AsString := ItemCode;
    qryGeneral.ParamByName('item_name').AsString := ItemName;
    qryGeneral.ParamByName('category_id').AsInteger := CategoryID;
    qryGeneral.ParamByName('unit_price').AsCurrency := UnitPrice;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('AddItem error: ' + E.Message);
    end;
  end;
end;

function TdmMain.UpdateItem(ItemID: Integer; const ItemCode, ItemName: string; CategoryID: Integer; UnitPrice: Currency; const Barcode: string = ''): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE ITEMS SET ITEM_CODE = :item_code, ITEM_NAME = :item_name, ');
    qryGeneral.SQL.Add('CATEGORY_ID = :category_id, UNIT_PRICE = :unit_price WHERE ID = :id');
    qryGeneral.ParamByName('item_code').AsString := ItemCode;
    qryGeneral.ParamByName('item_name').AsString := ItemName;
    qryGeneral.ParamByName('category_id').AsInteger := CategoryID;
    qryGeneral.ParamByName('unit_price').AsCurrency := UnitPrice;
    qryGeneral.ParamByName('id').AsInteger := ItemID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('UpdateItem error: ' + E.Message);
    end;
  end;
end;

function TdmMain.DeleteItem(ItemID: Integer): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE ITEMS SET IS_ACTIVE = 0 WHERE ID = :id');
    qryGeneral.ParamByName('id').AsInteger := ItemID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('DeleteItem error: ' + E.Message);
    end;
  end;
end;

function TdmMain.SetItemPicture(ItemID: Integer; PictureData: TStream): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE ITEMS SET PICTURE = :picture WHERE ID = :id');
    qryGeneral.ParamByName('id').AsInteger := ItemID;
    
    PictureData.Position := 0;
    qryGeneral.ParamByName('picture').LoadFromStream(PictureData, ftBlob);
    
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('SetItemPicture error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetItemPicture(ItemID: Integer; PictureData: TStream): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT PICTURE FROM ITEMS WHERE ID = :id AND IS_ACTIVE = 1');
    qryGeneral.ParamByName('id').AsInteger := ItemID;
    qryGeneral.Open;
    
    if not qryGeneral.EOF and not qryGeneral.FieldByName('PICTURE').IsNull then
    begin
      PictureData.Position := 0;
      TBlobField(qryGeneral.FieldByName('PICTURE')).SaveToStream(PictureData);
      PictureData.Position := 0;
      Result := True;
    end;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetItemPicture error: ' + E.Message);
    end;
  end;
end;

function TdmMain.UpdateItemStock(ItemID: Integer; QuantitySold: Integer): Boolean;
begin
  Result := False;
  try
    if Assigned(Logger) then
      Logger.LogInfo('UpdateItemStock called: ItemID=' + IntToStr(ItemID) + ', QuantitySold=' + IntToStr(QuantitySold));
    
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    // Use COALESCE to treat NULL as 0
    qryGeneral.SQL.Add('UPDATE ITEMS SET STOCK_QTY = COALESCE(STOCK_QTY, 0) - :qty WHERE ID = :itemid AND IS_ACTIVE = 1');
    qryGeneral.ParamByName('qty').AsInteger := QuantitySold;
    qryGeneral.ParamByName('itemid').AsInteger := ItemID;
    qryGeneral.ExecSQL;
    
    // Check if any rows were affected
    if qryGeneral.RowsAffected > 0 then
    begin
      transMain.Commit;
      Result := True;
      if Assigned(Logger) then
        Logger.LogInfo('Stock updated successfully for ItemID ' + IntToStr(ItemID));
    end
    else
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogWarning('No rows affected - ItemID may not exist: ' + IntToStr(ItemID));
    end;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('UpdateItemStock error: ' + E.Message);
      Result := False;
    end;
  end;
end;


function TdmMain.GetItemStock(ItemID: Integer): Integer;
begin
  Result := 0;
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    // Use COALESCE to return 0 instead of NULL
    qryGeneral.SQL.Add('SELECT COALESCE(STOCK_QTY, 0) as STOCK_QTY FROM ITEMS WHERE ID = :itemid AND IS_ACTIVE = 1');
    qryGeneral.ParamByName('itemid').AsInteger := ItemID;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('STOCK_QTY').AsInteger;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetItemStock error: ' + E.Message);
      Result := 0;
    end;
  end;
end;


function TdmMain.CheckStockAvailability(ItemID: Integer; RequiredQty: Integer): Boolean;
var
  CurrentStock: Integer;
begin
  Result := False;
  try
    CurrentStock := GetItemStock(ItemID);
    Result := CurrentStock >= RequiredQty;
    
    if not Result then
    begin
      if Assigned(Logger) then
        Logger.LogWarning('Insufficient stock: ItemID=' + IntToStr(ItemID) + 
                         ', Required=' + IntToStr(RequiredQty) + 
                         ', Available=' + IntToStr(CurrentStock));
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('CheckStockAvailability error: ' + E.Message);
      Result := False;
    end;
  end;
end;

// Sales management functions
function TdmMain.CreateSale(const ReceiptNo: string; UserID: Integer; const ShiftType: string; 
                           Subtotal, Discount, Total: Currency): Integer;
begin
  Result := 0;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO SALES (RECEIPT_NO, CASHIER_ID, SHIFT_TYPE, SUBTOTAL, DISCOUNT_AMOUNT, TOTAL_AMOUNT, PAYMENT_METHOD) VALUES (');
    qryGeneral.SQL.Add(':receipt_no, :cashier_id, :shift_type, :subtotal, :discount_amount, :total_amount, :payment_method)');
    qryGeneral.SQL.Add('RETURNING ID');
    qryGeneral.ParamByName('receipt_no').AsString := ReceiptNo;
    qryGeneral.ParamByName('cashier_id').AsInteger := UserID;
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.ParamByName('subtotal').AsCurrency := Subtotal;
    qryGeneral.ParamByName('discount_amount').AsCurrency := Discount;
    qryGeneral.ParamByName('total_amount').AsCurrency := Total;
    qryGeneral.ParamByName('payment_method').AsString := 'CASH'; // Default payment
    qryGeneral.Open;
    Result := qryGeneral.FieldByName('ID').AsInteger;
    qryGeneral.Close;
    
    transMain.Commit;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('CreateSale error: ' + E.Message);
    end;
  end;
end;

function TdmMain.AddSaleItem(SaleID, ItemID, Quantity: Integer; UnitPrice: Currency): Boolean;
var
  TotalPrice: Currency;
begin
  Result := False;
  
  try
    TotalPrice := Quantity * UnitPrice;
    
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO SALE_ITEMS (SALE_ID, ITEM_ID, QUANTITY, UNIT_PRICE, TOTAL_PRICE) VALUES (');
    qryGeneral.SQL.Add(':sale_id, :item_id, :quantity, :unit_price, :total_price)');
    qryGeneral.ParamByName('sale_id').AsInteger := SaleID;
    qryGeneral.ParamByName('item_id').AsInteger := ItemID;
    qryGeneral.ParamByName('quantity').AsInteger := Quantity;
    qryGeneral.ParamByName('unit_price').AsCurrency := UnitPrice;
    qryGeneral.ParamByName('total_price').AsCurrency := TotalPrice;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('AddSaleItem error: ' + E.Message);
    end;
  end;
end;

function TdmMain.UpdateSalePayment(SaleID: Integer; const PaymentMethod: string; 
                                  AmountReceived, ChangeAmount: Currency;
                                  const CardNumber, AuthCode, QRCode, QRReference: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('UPDATE SALES SET PAYMENT_METHOD = :payment_method, ');
    qryGeneral.SQL.Add('AMOUNT_RECEIVED = :amount_received, CHANGE_AMOUNT = :change_amount, ');
    qryGeneral.SQL.Add('CARD_NUMBER = :card_number, AUTH_CODE = :auth_code, ');
    qryGeneral.SQL.Add('QR_CODE = :qr_code, QR_REFERENCE = :qr_reference WHERE ID = :id');
    qryGeneral.ParamByName('payment_method').AsString := PaymentMethod;
    qryGeneral.ParamByName('amount_received').AsCurrency := AmountReceived;
    qryGeneral.ParamByName('change_amount').AsCurrency := ChangeAmount;
    qryGeneral.ParamByName('card_number').AsString := CardNumber;
    qryGeneral.ParamByName('auth_code').AsString := AuthCode;
    qryGeneral.ParamByName('qr_code').AsString := QRCode;
    qryGeneral.ParamByName('qr_reference').AsString := QRReference;
    qryGeneral.ParamByName('id').AsInteger := SaleID;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('UpdateSalePayment error: ' + E.Message);
    end;
  end;
end;


function TdmMain.GetSaleDetails(SaleID: Integer): Boolean;
begin
  Result := False;
  try
    qrySales.Close;
    qrySales.SQL.Clear;
    qrySales.SQL.Add('SELECT s.*, u.FULL_NAME as USERNAME');
    qrySales.SQL.Add('FROM SALES s');
    qrySales.SQL.Add('LEFT JOIN USERS u ON s.CASHIER_ID = u.ID');
    qrySales.SQL.Add('WHERE s.ID = :saleid');
    qrySales.ParamByName('saleid').AsInteger := SaleID;
    qrySales.Open;
    
    Result := not qrySales.EOF;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetSaleDetails for SaleID ' + IntToStr(SaleID) + ': ' + BoolToStr(Result, True));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetSaleDetails error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetSaleItems(SaleID: Integer): Boolean;
begin
  Result := False;
  try
    qrySaleItems.Close;
    qrySaleItems.SQL.Clear;
    qrySaleItems.SQL.Add('SELECT si.*, i.ITEM_NAME, i.ITEM_CODE, c.CATEGORY_NAME');
    qrySaleItems.SQL.Add('FROM SALE_ITEMS si');
    qrySaleItems.SQL.Add('LEFT JOIN ITEMS i ON si.ITEM_ID = i.ID');
    qrySaleItems.SQL.Add('LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID');
    qrySaleItems.SQL.Add('WHERE si.SALE_ID = :saleid');
    qrySaleItems.SQL.Add('ORDER BY si.ID');
    qrySaleItems.ParamByName('saleid').AsInteger := SaleID;
    qrySaleItems.Open;
    
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetSaleItems for SaleID ' + IntToStr(SaleID) + ': ' + IntToStr(qrySaleItems.RecordCount) + ' items');
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetSaleItems error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetNextReceiptNumber: string;
var
  Year, Month, Day: Word;
  Counter: Integer;
begin
  try
    DecodeDate(Date, Year, Month, Day);
    
    // Get today's receipt count
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT COUNT(*) as RECEIPT_COUNT FROM SALES WHERE CAST(SALE_DATE AS DATE) = CURRENT_DATE');
    qryGeneral.Open;
    Counter := qryGeneral.FieldByName('RECEIPT_COUNT').AsInteger + 1;
    qryGeneral.Close;
    
    Result := Format('R%04d%02d%02d%04d', [Year, Month, Day, Counter]);
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetNextReceiptNumber error: ' + E.Message);
      Result := GenerateReceiptNumber; // Fallback to hash utils function
    end;
  end;
end;

// Cash management functions
function TdmMain.AddCashTransaction(UserID: Integer; const TransactionType: string; 
                                   Amount: Currency; const Description: string; 
                                   SaleID: Integer; const ShiftType: string): Boolean;
begin
  Result := False;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO CASH_TRANSACTIONS (USER_ID, TRANSACTION_TYPE, AMOUNT, REASON, SALE_ID, SHIFT_TYPE) VALUES (');
    qryGeneral.SQL.Add(':user_id, :transaction_type, :amount, :reason, :sale_id, :shift_type)');
    qryGeneral.ParamByName('user_id').AsInteger := UserID;
    qryGeneral.ParamByName('transaction_type').AsString := TransactionType;
    qryGeneral.ParamByName('amount').AsCurrency := Amount;
    qryGeneral.ParamByName('reason').AsString := Description;
    if SaleID > 0 then
      qryGeneral.ParamByName('sale_id').AsInteger := SaleID
    else
      qryGeneral.ParamByName('sale_id').Clear;
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.ExecSQL;
    transMain.Commit;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('AddCashTransaction error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCashBalance(const ShiftType: string; TransactionDate: TDate): Currency;
begin
  Result := 0;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT ');
    qryGeneral.SQL.Add('  SUM(CASE WHEN TRANSACTION_TYPE IN (''CASH_IN'', ''SALE'') THEN AMOUNT ELSE 0 END) - ');
    qryGeneral.SQL.Add('  SUM(CASE WHEN TRANSACTION_TYPE = ''CASH_OUT'' THEN AMOUNT ELSE 0 END) as BALANCE ');
    qryGeneral.SQL.Add('FROM CASH_TRANSACTIONS ');
    qryGeneral.SQL.Add('WHERE SHIFT_TYPE = :shift_type AND CAST(TRANSACTION_DATE AS DATE) = :transaction_date');
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.ParamByName('transaction_date').AsDate := TransactionDate;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('BALANCE').AsCurrency;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashBalance error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCashTransactions(const ShiftType: string; TransactionDate: TDate): Boolean;
begin
  Result := False;
  
  try
    qryCashTransactions.SQL.Clear;
    qryCashTransactions.SQL.Add('SELECT ct.*, u.FULL_NAME as USER_NAME FROM CASH_TRANSACTIONS ct ');
    qryCashTransactions.SQL.Add('LEFT JOIN USERS u ON ct.USER_ID = u.ID ');
    qryCashTransactions.SQL.Add('WHERE ct.SHIFT_TYPE = :shift_type AND CAST(ct.TRANSACTION_DATE AS DATE) = :transaction_date ');
    qryCashTransactions.SQL.Add('ORDER BY ct.TRANSACTION_DATE DESC');
    qryCashTransactions.ParamByName('shift_type').AsString := ShiftType;
    qryCashTransactions.ParamByName('transaction_date').AsDate := TransactionDate;
    qryCashTransactions.Open;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashTransactions error: ' + E.Message);
    end;
  end;
end;

// Shift management functions
function TdmMain.StartShift(UserID: Integer; const ShiftType: string; OpeningCash: Currency): Integer;
begin
  Result := 0;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('INSERT INTO SHIFTS (SHIFT_DATE, SHIFT_TYPE, USER_ID, OPENING_CASH) VALUES (');
    qryGeneral.SQL.Add('CURRENT_DATE, :shift_type, :user_id, :opening_cash)');
    qryGeneral.SQL.Add('RETURNING ID');
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.ParamByName('user_id').AsInteger := UserID;
    qryGeneral.ParamByName('opening_cash').AsCurrency := OpeningCash;
    qryGeneral.Open;
    Result := qryGeneral.FieldByName('ID').AsInteger;
    qryGeneral.Close;
    
    transMain.Commit;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('StartShift error: ' + E.Message);
    end;
  end;
end;

function TdmMain.EndShift(ShiftID: Integer; ClosingCash: Currency; const Notes: string): Boolean;
var
  Variance: Currency;
begin
  Result := False;
  
  try
    // Calculate variance (closing cash - expected cash)
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT OPENING_CASH FROM SHIFTS WHERE ID = :id');
    qryGeneral.ParamByName('id').AsInteger := ShiftID;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
    begin
      Variance := ClosingCash - qryGeneral.FieldByName('OPENING_CASH').AsCurrency;
      qryGeneral.Close;
      
      qryGeneral.SQL.Clear;
      qryGeneral.SQL.Add('UPDATE SHIFTS SET END_TIME = CURRENT_TIMESTAMP, CLOSING_CASH = :closing_cash, ');
      qryGeneral.SQL.Add('VARIANCE = :variance, IS_CLOSED = ''Y'' WHERE ID = :id');
      qryGeneral.ParamByName('closing_cash').AsCurrency := ClosingCash;
      qryGeneral.ParamByName('variance').AsCurrency := Variance;
      qryGeneral.ParamByName('id').AsInteger := ShiftID;
      qryGeneral.ExecSQL;
      transMain.Commit;
      
      Result := True;
    end;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('EndShift error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCurrentShift(const ShiftType: string): Integer;
begin
  Result := 0;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT ID FROM SHIFTS WHERE SHIFT_TYPE = :shift_type AND IS_CLOSED = ''N'' ');
    qryGeneral.SQL.Add('AND SHIFT_DATE = CURRENT_DATE ORDER BY START_TIME DESC');
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('ID').AsInteger;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCurrentShift error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetShiftSummary(ShiftID: Integer): Boolean;
begin
  Result := False;
  
  try
    qryShifts.SQL.Clear;
    qryShifts.SQL.Add('SELECT s.*, u.FULL_NAME as USER_NAME FROM SHIFTS s ');
    qryShifts.SQL.Add('LEFT JOIN USERS u ON s.USER_ID = u.ID WHERE s.ID = :shift_id');
    qryShifts.ParamByName('shift_id').AsInteger := ShiftID;
    qryShifts.Open;
    
    Result := not qryShifts.EOF;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetShiftSummary error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetShiftReport(ShiftID: Integer): Boolean;
begin
  Result := False;
  try
    qryReports.Close;
    qryReports.SQL.Clear;
    qryReports.SQL.Add('SELECT s.*, u.FULL_NAME as USER_NAME,');
    qryReports.SQL.Add('  (SELECT COUNT(*) FROM SALES WHERE CASHIER_ID = s.USER_ID AND SHIFT_TYPE = s.SHIFT_TYPE AND CAST(SALE_DATE AS DATE) = s.SHIFT_DATE) as TRANSACTION_COUNT,');
    qryReports.SQL.Add('  (SELECT COALESCE(SUM(TOTAL_AMOUNT), 0) FROM SALES WHERE CASHIER_ID = s.USER_ID AND SHIFT_TYPE = s.SHIFT_TYPE AND CAST(SALE_DATE AS DATE) = s.SHIFT_DATE) as TOTAL_SALES');
    qryReports.SQL.Add('FROM SHIFTS s');
    qryReports.SQL.Add('LEFT JOIN USERS u ON s.USER_ID = u.ID');
    qryReports.SQL.Add('WHERE s.ID = :shift_id');
    qryReports.ParamByName('shift_id').AsInteger := ShiftID;
    qryReports.Open;
    
    Result := qryReports.Active and not qryReports.IsEmpty;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetShiftReport error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetAllShifts: Boolean;
begin
  Result := False;
  try
    qryShifts.Close;
    qryShifts.SQL.Clear;
    qryShifts.SQL.Add('SELECT s.*, u.FULL_NAME as USERNAME');
    qryShifts.SQL.Add('FROM SHIFTS s');
    qryShifts.SQL.Add('LEFT JOIN USERS u ON s.USER_ID = u.ID');
    qryShifts.SQL.Add('ORDER BY s.START_TIME DESC');
    qryShifts.SQL.Add('ROWS 50'); // Firebird syntax for LIMIT
    qryShifts.Open;
    
    Result := qryShifts.Active;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetAllShifts error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetShiftSales(ShiftID: Integer): Boolean;
begin
  Result := False;
  try
    // Get shift details first
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT SHIFT_DATE, SHIFT_TYPE, USER_ID FROM SHIFTS WHERE ID = :shift_id');
    qryGeneral.ParamByName('shift_id').AsInteger := ShiftID;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
    begin
      qrySales.Close;
      qrySales.SQL.Clear;
      qrySales.SQL.Add('SELECT RECEIPT_NO, SALE_DATE, TOTAL_AMOUNT, PAYMENT_METHOD');
      qrySales.SQL.Add('FROM SALES');
      qrySales.SQL.Add('WHERE CASHIER_ID = :user_id AND SHIFT_TYPE = :shift_type');
      qrySales.SQL.Add('AND CAST(SALE_DATE AS DATE) = :shift_date');
      qrySales.SQL.Add('ORDER BY SALE_DATE');
      qrySales.ParamByName('user_id').AsInteger := qryGeneral.FieldByName('USER_ID').AsInteger;
      qrySales.ParamByName('shift_type').AsString := qryGeneral.FieldByName('SHIFT_TYPE').AsString;
      qrySales.ParamByName('shift_date').AsDate := qryGeneral.FieldByName('SHIFT_DATE').AsDateTime;
      qrySales.Open;
      
      Result := qrySales.Active;
    end;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetShiftSales error: ' + E.Message);
    end;
  end;
end;

// Cash reporting methods
function TdmMain.GetCashSalesTotal(ADate: TDateTime; const ShiftType: string): Currency;
begin
  Result := 0;
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT COALESCE(SUM(TOTAL_AMOUNT), 0) as CASH_TOTAL');
    qryGeneral.SQL.Add('FROM SALES');
    qryGeneral.SQL.Add('WHERE CAST(SALE_DATE AS DATE) = :sale_date');
    qryGeneral.SQL.Add('AND PAYMENT_METHOD = ''CASH''');
    if ShiftType <> '' then
    begin
      qryGeneral.SQL.Add('AND SHIFT_TYPE = :shift_type');
      qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    end;
    qryGeneral.ParamByName('sale_date').AsDate := ADate;
    qryGeneral.Open;
    
    if not qryGeneral.IsEmpty then
      Result := qryGeneral.FieldByName('CASH_TOTAL').AsCurrency;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashSalesTotal error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCashInTotal(ADate: TDateTime; const ShiftType: string): Currency;
begin
  Result := 0;
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT COALESCE(SUM(AMOUNT), 0) as CASH_IN_TOTAL');
    qryGeneral.SQL.Add('FROM CASH_TRANSACTIONS');
    qryGeneral.SQL.Add('WHERE CAST(TRANSACTION_DATE AS DATE) = :trans_date');
    qryGeneral.SQL.Add('AND TRANSACTION_TYPE = ''CASH_IN''');
    if ShiftType <> '' then
    begin
      qryGeneral.SQL.Add('AND SHIFT_TYPE = :shift_type');
      qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    end;
    qryGeneral.ParamByName('trans_date').AsDate := ADate;
    qryGeneral.Open;
    
    if not qryGeneral.IsEmpty then
      Result := qryGeneral.FieldByName('CASH_IN_TOTAL').AsCurrency;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashInTotal error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCashOutTotal(ADate: TDateTime; const ShiftType: string): Currency;
begin
  Result := 0;
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT COALESCE(SUM(AMOUNT), 0) as CASH_OUT_TOTAL');
    qryGeneral.SQL.Add('FROM CASH_TRANSACTIONS');
    qryGeneral.SQL.Add('WHERE CAST(TRANSACTION_DATE AS DATE) = :trans_date');
    qryGeneral.SQL.Add('AND TRANSACTION_TYPE = ''CASH_OUT''');
    if ShiftType <> '' then
    begin
      qryGeneral.SQL.Add('AND SHIFT_TYPE = :shift_type');
      qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    end;
    qryGeneral.ParamByName('trans_date').AsDate := ADate;
    qryGeneral.Open;
    
    if not qryGeneral.IsEmpty then
      Result := qryGeneral.FieldByName('CASH_OUT_TOTAL').AsCurrency;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashOutTotal error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetSalesData(ADate: TDateTime): Boolean;
begin
  Result := False;
  try
    qrySales.Close;
    qrySales.SQL.Clear;
    qrySales.SQL.Add('SELECT RECEIPT_NO, SALE_DATE, TOTAL_AMOUNT, PAYMENT_METHOD');
    qrySales.SQL.Add('FROM SALES');
    qrySales.SQL.Add('WHERE CAST(SALE_DATE AS DATE) = :sale_date');
    qrySales.SQL.Add('ORDER BY SALE_DATE');
    qrySales.ParamByName('sale_date').AsDate := ADate;
    qrySales.Open;
    
    Result := qrySales.Active;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetSalesData error: ' + E.Message);
    end;
  end;
end;

// Reports functions
// Continuing from where the code was cut off...

function TdmMain.GetDailySalesReport(DateFrom, DateTo: TDate; const UserFilter, ShiftFilter: string): Boolean;
var
  SQL: string;
begin
  Result := False;
  try
    SQL := 'SELECT s.SALE_DATE, s.RECEIPT_NO, u.FULL_NAME as USER_NAME, ' +
           'COUNT(si.ID) as ITEM_COUNT, s.TOTAL_AMOUNT, s.PAYMENT_METHOD ' +
           'FROM SALES s ' +
           'INNER JOIN USERS u ON s.CASHIER_ID = u.ID ' +
           'LEFT JOIN SALE_ITEMS si ON s.ID = si.SALE_ID ' +
           'WHERE s.SALE_DATE BETWEEN :date_from AND :date_to ';
    
    if UserFilter <> '' then
      SQL := SQL + 'AND u.FULL_NAME = :user_filter ';
      
    if ShiftFilter <> '' then
      SQL := SQL + 'AND s.SHIFT_TYPE = :shift_filter ';
      
    SQL := SQL + 'GROUP BY s.ID, s.SALE_DATE, s.RECEIPT_NO, u.FULL_NAME, s.TOTAL_AMOUNT, s.PAYMENT_METHOD ' +
                 'ORDER BY s.SALE_DATE DESC';
    
    qryReports.Close;
    qryReports.SQL.Text := SQL;
    qryReports.ParamByName('date_from').AsDate := DateFrom;
    qryReports.ParamByName('date_to').AsDate := DateTo;
    
    if UserFilter <> '' then
      qryReports.ParamByName('user_filter').AsString := UserFilter;
      
    if ShiftFilter <> '' then
      qryReports.ParamByName('shift_filter').AsString := ShiftFilter;
      
    qryReports.Open;
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetDailySalesReport successful, Records: ' + IntToStr(qryReports.RecordCount));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetDailySalesReport error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetItemSalesReport(DateFrom, DateTo: TDate; const UserFilter, ShiftFilter: string): Boolean;
var
  SQL: string;
begin
  Result := False;
  try
    SQL := 'SELECT i.ITEM_CODE, i.ITEM_NAME, c.CATEGORY_NAME, ' +
           'SUM(si.QUANTITY) as TOTAL_QTY, SUM(si.TOTAL_PRICE) as TOTAL_SALES, ' +
           'AVG(si.UNIT_PRICE) as AVG_PRICE ' +
           'FROM SALE_ITEMS si ' +
           'INNER JOIN ITEMS i ON si.ITEM_ID = i.ID ' +
           'INNER JOIN SALES s ON si.SALE_ID = s.ID ' +
           'INNER JOIN USERS u ON s.CASHIER_ID = u.ID ' +
           'LEFT JOIN CATEGORIES c ON i.CATEGORY_ID = c.ID ' +
           'WHERE s.SALE_DATE BETWEEN :date_from AND :date_to ';
    
    if UserFilter <> '' then
      SQL := SQL + 'AND u.FULL_NAME = :user_filter ';
      
    if ShiftFilter <> '' then
      SQL := SQL + 'AND s.SHIFT_TYPE = :shift_filter ';
      
    SQL := SQL + 'GROUP BY i.ID, i.ITEM_CODE, i.ITEM_NAME, c.CATEGORY_NAME ' +
                 'ORDER BY TOTAL_SALES DESC';
    
    qryReports.Close;
    qryReports.SQL.Text := SQL;
    qryReports.ParamByName('date_from').AsDate := DateFrom;
    qryReports.ParamByName('date_to').AsDate := DateTo;
    
    if UserFilter <> '' then
      qryReports.ParamByName('user_filter').AsString := UserFilter;
      
    if ShiftFilter <> '' then
      qryReports.ParamByName('shift_filter').AsString := ShiftFilter;
      
    qryReports.Open;
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetItemSalesReport successful, Records: ' + IntToStr(qryReports.RecordCount));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetItemSalesReport error: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TdmMain.GetUserSalesReport(DateFrom, DateTo: TDate; const ShiftFilter: string): Boolean;
var
  SQL: string;
begin
  Result := False;
  try
    SQL := 'SELECT u.FULL_NAME as USER_NAME, u.USERNAME, ' +
           'COUNT(s.ID) as TRANSACTION_COUNT, SUM(s.TOTAL_AMOUNT) as TOTAL_SALES, ' +
           'AVG(s.TOTAL_AMOUNT) as AVG_SALE ' +
           'FROM SALES s ' +
           'INNER JOIN USERS u ON s.CASHIER_ID = u.ID ' +
           'WHERE s.SALE_DATE BETWEEN :date_from AND :date_to ';
    
    if ShiftFilter <> '' then
      SQL := SQL + 'AND s.SHIFT_TYPE = :shift_filter ';
      
    SQL := SQL + 'GROUP BY u.ID, u.FULL_NAME, u.USERNAME ' +
                 'ORDER BY TOTAL_SALES DESC';
    
    qryReports.Close;
    qryReports.SQL.Text := SQL;
    qryReports.ParamByName('date_from').AsDate := DateFrom;
    qryReports.ParamByName('date_to').AsDate := DateTo;
    
    if ShiftFilter <> '' then
      qryReports.ParamByName('shift_filter').AsString := ShiftFilter;
      
    qryReports.Open;
    Result := True;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetUserSalesReport successful, Records: ' + IntToStr(qryReports.RecordCount));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetUserSalesReport error: ' + E.Message);
      Result := False;
    end;
  end;
end;

// Add these methods to the implementation section of DataModule.pas, just before the final 'end.'

{ POS Methods Implementation }

function TdmMain.GetPOSCategories: TSQLQuery;
begin
  try
    qryCategories.Close;
    // Use optimized view for better performance
    qryCategories.SQL.Text := 'SELECT * FROM v_active_categories';
    qryCategories.Open;
    Result := qryCategories;

    if Assigned(Logger) then
      Logger.LogInfo('Retrieved ' + IntToStr(qryCategories.RecordCount) + ' active categories');

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in GetPOSCategories: ' + E.Message);
      raise;
    end;
  end;
end;

function TdmMain.GetPOSItemsByCategory(CategoryID: Integer): TSQLQuery;
begin
  try
    qryItems.Close;
    // Use optimized view with prepared statement
    qryItems.SQL.Text := 'SELECT id, item_code, item_name, category_id, category_name, unit_price, picture, barcode, stock_qty ' +
                         'FROM v_active_items WHERE category_id = :category_id ORDER BY item_name';
    qryItems.Prepare;
    qryItems.ParamByName('category_id').AsInteger := CategoryID;
    qryItems.Open;
    Result := qryItems;

    if Assigned(Logger) then
      Logger.LogInfo('Retrieved ' + IntToStr(qryItems.RecordCount) + ' items for category ' + IntToStr(CategoryID));

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in GetPOSItemsByCategory: ' + E.Message);
      raise;
    end;
  end;
end;

function TdmMain.GetPOSItemDetails(ItemID: Integer): Boolean;
begin
  Result := False;
  try
    qryItems.Close;
    qryItems.SQL.Text := 'SELECT i.*, c.category_name ' +
                        'FROM items i ' +
                        'LEFT JOIN categories c ON i.category_id = c.id ' +
                        'WHERE i.id = :item_id';
    qryItems.ParamByName('item_id').AsInteger := ItemID;
    qryItems.Open;
    Result := not qryItems.IsEmpty;
  except
    on E: Exception do
    begin
      Logger.LogError('Error in GetPOSItemDetails: ' + E.Message);
      raise;
    end;
  end;
end;

function TdmMain.FastSearchItems(const SearchTerm: string): TSQLQuery;
begin
  try
    qryItems.Close;
    // Use stored procedure for optimized search
    qryItems.SQL.Text := 'SELECT * FROM sp_search_items(:search_term)';
    qryItems.Prepare;
    qryItems.ParamByName('search_term').AsString := SearchTerm;
    qryItems.Open;
    Result := qryItems;

    if Assigned(Logger) then
      Logger.LogInfo('Fast search for "' + SearchTerm + '" returned ' + IntToStr(qryItems.RecordCount) + ' items');

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in FastSearchItems: ' + E.Message);
      raise;
    end;
  end;
end;

end.
