unit DatabaseSetup;

{$mode objfpc}{$H+}

interface

uses
  Classes,Dialogs, SysUtils, DB, sqldb, ConfigUtils, Logging;

procedure SetupDatabase;
function CheckDatabaseExists: <PERSON>olean;
function CreateDatabase: Boolean;
function CheckTablesExist: <PERSON>olean;
function CreateTables: Boolean;
function InsertDefaultData: Boolean;

implementation

procedure SetupDatabase;
begin
  try
    if Assigned(Logger) then
      Logger.LogInfo('Starting database setup...');
      
    if not CheckDatabaseExists then
    begin
      ShowMessage('Missing Database');
    end;
    
    if not CheckTablesExist then
    begin
      if not CreateTables then
        raise Exception.Create('Failed to create database tables');
        
      if not InsertDefaultData then
        raise Exception.Create('Failed to insert default data');
    end;
    
    if Assigned(Logger) then
      Logger.LogInfo('Database setup completed successfully');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Database setup failed: ' + E.Message);
      raise;
    end;
  end;
end;

function CheckDatabaseExists: <PERSON>olean;
var
  Connection: TSQLConnector;
begin
  Result := False;
  Connection := TSQLConnector.Create(nil);
  try
    try
      Connection.DatabaseName := Config.GetValue('Database', 'Path', '');
      Connection.UserName := Config.GetValue('Database', 'User', '');
      Connection.Password := Config.GetValue('Database', 'Password', '');
      Connection.CharSet := Config.GetValue('Database', 'Charset', 'UTF8');
      Connection.ConnectorType:='Firebird';
      Connection.Connected := True;
      Result := Connection.Connected;
      Connection.Connected := False;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogInfo('Database does not exist or cannot connect: ' + E.Message);
        Result := False;
      end;
    end;
  finally
    Connection.Free;
  end;
end;

function CreateDatabase: Boolean;
var
  Connection: TSQLConnector;
  CreateSQL: string;
begin
  Result := False;
  Connection := TSQLConnector.Create(nil);
  try
    try
      CreateSQL := Format(
        'CREATE DATABASE ''%s'' USER ''%s'' PASSWORD ''%s'' DEFAULT CHARACTER SET %s',
        [Config.GetValue('Database', 'Path', ''),
         Config.GetValue('Database', 'User', ''),
         Config.GetValue('Database', 'Password', ''),
         Config.GetValue('Database', 'Charset', 'UTF8')]
      );

      Connection.DatabaseName := Config.GetValue('Database', 'Path', '');
      Connection.UserName := Config.GetValue('Database', 'User', '');
      Connection.Password := Config.GetValue('Database', 'Password', '');
      Connection.CharSet := Config.GetValue('Database', 'Charset', 'UTF8');
      Connection.ConnectorType:='Firebird';

      if Assigned(Logger) then
        Logger.LogInfo('Database created successfully: ' + Config.GetValue('Database', 'Path', ''));
      Result := True;

    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Failed to create database: ' + E.Message);
        Result := False;
      end;
    end;
  finally
    Connection.Free;
  end;
end;

function CheckTablesExist: Boolean;
var
  Connection: TSQLConnector;
  Transaction: TSQLTransaction;
  Query: TSQLQuery;
  TableCount: Integer;
begin
  Result := False;
  Connection := TSQLConnector.Create(nil);
  Transaction := TSQLTransaction.Create(nil);
  Query := TSQLQuery.Create(nil);
  try
    try
      Connection.DatabaseName := Config.GetValue('Database', 'Path', '');
      Connection.UserName := Config.GetValue('Database', 'User', '');
      Connection.Password := Config.GetValue('Database', 'Password', '');
      Connection.CharSet := Config.GetValue('Database', 'Charset', 'UTF8');
      Connection.ConnectorType:='Firebird';
      Transaction.DataBase := Connection;
      Query.DataBase := Connection;
      Query.Transaction := Transaction;

      Connection.Connected := True;
      Transaction.Active := True;

      Query.SQL.Text := 'SELECT COUNT(*) FROM RDB$RELATIONS WHERE RDB$RELATION_NAME IN (''USERS'', ''CATEGORIES'', ''ITEMS'', ''SALES'', ''SALE_ITEMS'', ''SHIFTS'', ''CASH_TRANSACTIONS'') AND RDB$SYSTEM_FLAG = 0';
      Query.Open;

      TableCount := Query.Fields[0].AsInteger;
      Result := (TableCount = 7); // Updated to check for 7 tables instead of 6

      Query.Close;
      Transaction.Commit;
      Connection.Connected := False;

      if Assigned(Logger) then
        Logger.LogInfo(Format('Found %d main tables in database', [TableCount]));

    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error checking tables: ' + E.Message);
        Result := False;
      end;
    end;
  finally
    Query.Free;
    Transaction.Free;
    Connection.Free;
  end;
end;

function CreateTables: Boolean;
var
  Connection: TSQLConnector;
  Transaction: TSQLTransaction;
  Query: TSQLQuery;
  SQLStatements: TStringList;
  i: Integer;
begin
  Result := False;
  Connection := TSQLConnector.Create(nil);
  Transaction := TSQLTransaction.Create(nil);
  Query := TSQLQuery.Create(nil);
  SQLStatements := TStringList.Create;
  try
    try
      Connection.DatabaseName := Config.GetValue('Database', 'Path', '');
      Connection.UserName := Config.GetValue('Database', 'User', '');
      Connection.Password := Config.GetValue('Database', 'Password', '');
      Connection.CharSet := Config.GetValue('Database', 'Charset', 'UTF8');
      Connection.ConnectorType:='Firebird';
      Transaction.DataBase := Connection;
      Query.DataBase := Connection;
      Query.Transaction := Transaction;

      Connection.Connected := True;
      
      // Users table - conforming to create_database.sql schema
      SQLStatements.Add('CREATE TABLE users (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'username VARCHAR(50) NOT NULL UNIQUE, ' +
        'password_hash VARCHAR(255) NOT NULL, ' +
        'full_name VARCHAR(100) NOT NULL, ' +
        'is_active INTEGER DEFAULT 1, ' +
        'created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ' +
        'created_by INTEGER, ' +
        'CONSTRAINT pk_users PRIMARY KEY (id))');

      // Categories table - new table with picture field
      SQLStatements.Add('CREATE TABLE categories (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'category_name VARCHAR(50) NOT NULL UNIQUE, ' +
        'description VARCHAR(255), ' +
        'picture BLOB SUB_TYPE BINARY, ' +
        'is_active INTEGER DEFAULT 1, ' +
        'created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ' +
        'created_by INTEGER, ' +
        'CONSTRAINT pk_categories PRIMARY KEY (id))');
        
      // Items table - category_id as foreign key, with picture field
      SQLStatements.Add('CREATE TABLE items (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'item_code VARCHAR(20) NOT NULL UNIQUE, ' +
        'item_name VARCHAR(100) NOT NULL, ' +
        'category_id INTEGER NOT NULL, ' +
        'unit_price DECIMAL(10,2) NOT NULL, ' +
        'picture BLOB SUB_TYPE BINARY, ' +
        'is_active INTEGER DEFAULT 1, ' +
        'created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ' +
        'created_by INTEGER, ' +
        'CONSTRAINT pk_items PRIMARY KEY (id))');
        
      // Sales table - conforming to create_database.sql schema
      SQLStatements.Add('CREATE TABLE sales (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'receipt_no VARCHAR(20) NOT NULL UNIQUE, ' +
        'sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ' +
        'cashier_id INTEGER NOT NULL, ' +
        'subtotal DECIMAL(10,2) NOT NULL, ' +
        'discount_amount DECIMAL(10,2) DEFAULT 0, ' +
        'total_amount DECIMAL(10,2) NOT NULL, ' +
        'payment_method VARCHAR(10) NOT NULL CHECK (payment_method IN (''CASH'', ''CARD'', ''QR'')), ' +
        'card_number VARCHAR(20), ' +
        'auth_code VARCHAR(50), ' +
        'qr_code VARCHAR(100), ' +
        'qr_reference VARCHAR(50), ' +
        'shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN (''DAY'', ''NIGHT'')), ' +
        'CONSTRAINT pk_sales PRIMARY KEY (id))');
        
      // Sale items table - conforming to create_database.sql schema
      SQLStatements.Add('CREATE TABLE sale_items (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'sale_id INTEGER NOT NULL, ' +
        'item_id INTEGER NOT NULL, ' +
        'quantity DECIMAL(10,2) NOT NULL, ' +
        'unit_price DECIMAL(10,2) NOT NULL, ' +
        'total_price DECIMAL(10,2) NOT NULL, ' +
        'CONSTRAINT pk_sale_items PRIMARY KEY (id))');
        
      // Shifts table - conforming to create_database.sql schema
      SQLStatements.Add('CREATE TABLE shifts (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'shift_date DATE NOT NULL, ' +
        'shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN (''DAY'', ''NIGHT'')), ' +
        'user_id INTEGER NOT NULL, ' +
        'start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ' +
        'end_time TIMESTAMP, ' +
        'opening_cash DECIMAL(10,2) NOT NULL, ' +
        'closing_cash DECIMAL(10,2), ' +
        'variance DECIMAL(10,2), ' +
        'is_closed CHAR(1) DEFAULT ''N'' CHECK (is_closed IN (''Y'', ''N'')), ' +
        'CONSTRAINT pk_shifts PRIMARY KEY (id), ' +
        'CONSTRAINT uk_shifts_date_type UNIQUE (shift_date, shift_type))');
        
      // Cash transactions table - conforming to create_database.sql schema
      SQLStatements.Add('CREATE TABLE cash_transactions (' +
        'id INTEGER GENERATED BY DEFAULT AS IDENTITY, ' +
        'transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ' +
        'user_id INTEGER NOT NULL, ' +
        'transaction_type VARCHAR(10) NOT NULL CHECK (transaction_type IN (''CASH_IN'', ''CASH_OUT'', ''SALE'')), ' +
        'amount DECIMAL(10,2) NOT NULL, ' +
        'reason VARCHAR(255), ' +
        'sale_id INTEGER, ' +
        'shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN (''DAY'', ''NIGHT'')), ' +
        'CONSTRAINT pk_cash_transactions PRIMARY KEY (id))');
      
      // Add foreign key constraints
      SQLStatements.Add('ALTER TABLE items ADD CONSTRAINT fk_items_category FOREIGN KEY (category_id) REFERENCES categories(id)');
      SQLStatements.Add('ALTER TABLE sales ADD CONSTRAINT fk_sales_cashier FOREIGN KEY (cashier_id) REFERENCES users(id)');
      SQLStatements.Add('ALTER TABLE sale_items ADD CONSTRAINT fk_sale_items_sale FOREIGN KEY (sale_id) REFERENCES sales(id)');
      SQLStatements.Add('ALTER TABLE sale_items ADD CONSTRAINT fk_sale_items_item FOREIGN KEY (item_id) REFERENCES items(id)');
      SQLStatements.Add('ALTER TABLE shifts ADD CONSTRAINT fk_shifts_user FOREIGN KEY (user_id) REFERENCES users(id)');
      SQLStatements.Add('ALTER TABLE cash_transactions ADD CONSTRAINT fk_cash_trans_user FOREIGN KEY (user_id) REFERENCES users(id)');
      SQLStatements.Add('ALTER TABLE cash_transactions ADD CONSTRAINT fk_cash_trans_sale FOREIGN KEY (sale_id) REFERENCES sales(id)');
      
      // Add indexes for better performance - conforming to create_database.sql
      SQLStatements.Add('CREATE INDEX idx_sales_date ON sales(sale_date)');
      SQLStatements.Add('CREATE INDEX idx_sales_cashier ON sales(cashier_id)');
      SQLStatements.Add('CREATE INDEX idx_sales_shift ON sales(shift_type)');
      SQLStatements.Add('CREATE INDEX idx_sale_items_sale ON sale_items(sale_id)');
      SQLStatements.Add('CREATE INDEX idx_sale_items_item ON sale_items(item_id)');
      SQLStatements.Add('CREATE INDEX idx_shifts_date ON shifts(shift_date)');
      SQLStatements.Add('CREATE INDEX idx_cash_trans_date ON cash_transactions(transaction_date)');
      SQLStatements.Add('CREATE INDEX idx_cash_trans_user ON cash_transactions(user_id)');
      SQLStatements.Add('CREATE INDEX idx_cash_trans_shift ON cash_transactions(shift_type)');
      SQLStatements.Add('CREATE INDEX idx_categories_name ON categories(category_name)');
      SQLStatements.Add('CREATE INDEX idx_categories_active ON categories(is_active)');
      SQLStatements.Add('CREATE INDEX idx_items_code ON items(item_code)');
      SQLStatements.Add('CREATE INDEX idx_items_category ON items(category_id)');
      SQLStatements.Add('CREATE INDEX idx_items_active ON items(is_active)');
      
      // Execute all statements
      for i := 0 to SQLStatements.Count - 1 do
      begin
        Transaction.Active := True;
        Query.SQL.Text := SQLStatements[i];
        Query.ExecSQL;
        Transaction.Commit;
        
        if Assigned(Logger) then
          Logger.LogInfo('Executed SQL: ' + Copy(SQLStatements[i], 1, 50) + '...');
      end;
      
      Connection.Connected := False;
      Result := True;
      
      if Assigned(Logger) then
        Logger.LogInfo('All database tables created successfully');
        
    except
      on E: Exception do
      begin
        if Transaction.Active then
          Transaction.Rollback;
        if Assigned(Logger) then
          Logger.LogError('Error creating tables: ' + E.Message);
        Result := False;
      end;
    end;
  finally
    SQLStatements.Free;
    Query.Free;
    Transaction.Free;
    Connection.Free;
  end;
end;

function InsertDefaultData: Boolean;
var
  Connection: TSQLConnector;
  Transaction: TSQLTransaction;
  Query: TSQLQuery;
  SQLStatements: TStringList;
  i: Integer;
begin
  Result := False;
  Connection := TSQLConnector.Create(nil);
  Transaction := TSQLTransaction.Create(nil);
  Query := TSQLQuery.Create(nil);
  SQLStatements := TStringList.Create;
  try
    try
      Connection.DatabaseName := Config.GetValue('Database', 'Path', '');
      Connection.UserName := Config.GetValue('Database', 'User', '');
      Connection.Password := Config.GetValue('Database', 'Password', '');
      Connection.CharSet := Config.GetValue('Database', 'Charset', 'UTF8');
      Connection.ConnectorType:='Firebird';
      Transaction.DataBase := Connection;
      Query.DataBase := Connection;
      Query.Transaction := Transaction;

      Connection.Connected := True;

      // Insert default admin user (password: admin123 - conforming to create_database.sql)
      SQLStatements.Add('INSERT INTO users (username, password_hash, full_name, is_active) ' +
        'VALUES (''admin'', ''8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918'', ''System Administrator'', 1)');
      
      // Insert default categories
      SQLStatements.Add('INSERT INTO categories (category_name, description, is_active) VALUES ' +
        '(''Pool'', ''Pool table services'', 1)');
      SQLStatements.Add('INSERT INTO categories (category_name, description, is_active) VALUES ' +
        '(''Snooker'', ''Snooker table services'', 1)');
      SQLStatements.Add('INSERT INTO categories (category_name, description, is_active) VALUES ' +
        '(''Beverages'', ''Drinks and beverages'', 1)');
      SQLStatements.Add('INSERT INTO categories (category_name, description, is_active) VALUES ' +
        '(''Snacks'', ''Snacks and light food'', 1)');
      SQLStatements.Add('INSERT INTO categories (category_name, description, is_active) VALUES ' +
        '(''Other'', ''Other services and items'', 1)');
      
      // Insert default items - using category IDs instead of names
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''POOL1H'', ''Pool Table 1 Hour'', 1, 15.00)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''POOL30M'', ''Pool Table 30 Minutes'', 1, 8.00)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''SNOOK1H'', ''Snooker Table 1 Hour'', 2, 20.00)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''SNOOK30M'', ''Snooker Table 30 Minutes'', 2, 12.00)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''COKE'', ''Coca Cola'', 3, 2.50)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''WATER'', ''Mineral Water'', 3, 1.50)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''COFFEE'', ''Coffee'', 3, 3.00)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''TEA'', ''Tea'', 3, 2.00)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''CHIPS'', ''Potato Chips'', 4, 3.50)');
      SQLStatements.Add('INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES ' +
        '(''NUTS'', ''Mixed Nuts'', 4, 4.00)');
      
      // Execute all statements
      for i := 0 to SQLStatements.Count - 1 do
      begin
        Transaction.Active := True;
        Query.SQL.Text := SQLStatements[i];
        Query.ExecSQL;
        Transaction.Commit;
        
        if Assigned(Logger) then
          Logger.LogInfo('Inserted default data: ' + Copy(SQLStatements[i], 1, 50) + '...');
      end;
      
      Connection.Connected := False;
      Result := True;
      
      if Assigned(Logger) then
        Logger.LogInfo('Default data inserted successfully');
        
    except
      on E: Exception do
      begin
        if Transaction.Active then
          Transaction.Rollback;
        if Assigned(Logger) then
          Logger.LogError('Error inserting default data: ' + E.Message);
        Result := False;
      end;
    end;
  finally
    SQLStatements.Free;
    Query.Free;
    Transaction.Free;
    Connection.Free;
  end;
end;

end.
