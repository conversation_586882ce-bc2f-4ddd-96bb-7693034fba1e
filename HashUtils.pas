unit HashUtils;

{$mode objfpc}{$H+}

interface

uses
  Classes, Dialogs, SysUtils, DCPsha256, Logging;

function HashPassword(const Password: string): string;
function VerifyPassword(const Password, Hash: string): Boolean;
function GenerateReceiptNumber: string;
function GetCurrentShiftType: string;
function GenerateSalt: string;
function HashPasswordWithSalt(const Password, Salt: string): string;

implementation

uses
  DateUtils;

function GenerateSalt: string;
var
  i: Integer;
  SaltBytes: array[0..15] of Byte;
begin
  // Generate random salt
  Randomize;
  for i := 0 to 15 do
    SaltBytes[i] := Random(256);
  
  Result := '';
  for i := 0 to 15 do
    Result := Result + IntToHex(SaltBytes[i], 2);
  Result := LowerCase(Result);
end;

function HashPasswordWithSalt(const Password, Salt: string): string;
var
  SHA256: TDCP_sha256;
  Digest: array[0..31] of Byte;
  Source: string;
  i: Integer;
begin
  SHA256 := TDCP_sha256.Create(nil);
  try
    Source := Password + Salt;
    SHA256.Init;
    SHA256.UpdateStr(Source);
    SHA256.Final(Digest);
    
    Result := '';
    for i := 0 to 31 do
      Result := Result + IntToHex(Digest[i], 2);
    Result := LowerCase(Result);
  finally
    SHA256.Free;
  end;
end;

function HashPassword(const Password: string): string;
var
  SHA256: TDCP_sha256;
  Digest: array[0..31] of Byte;
  i: Integer;
begin
  SHA256 := TDCP_sha256.Create(nil);
  try
    SHA256.Init;
    SHA256.UpdateStr(Password);
    SHA256.Final(Digest);
    
    Result := '';
    for i := 0 to 31 do
      Result := Result + IntToHex(Digest[i], 2);
    Result := LowerCase(Result);
  finally
    SHA256.Free;
  end;
end;

function VerifyPassword(const Password, Hash: string): Boolean;
begin
  //if Length(Hash) <> 64 then
  //  ShowMessage('Invalid hash length. Expected 64 characters for SHA-256 hash.');
  //Logger.LogInfo(Format('Hash: %s, Password: %s Hashed: %s', [Hash, Password, HashPassword(Password)]));
  Result := HashPassword(Password) = LowerCase(Hash);
end;

function GenerateReceiptNumber: string;
var
  Year, Month, Day, Hour, Min, Sec, MSec: Word;
begin
  DecodeDateTime(Now, Year, Month, Day, Hour, Min, Sec, MSec);
  Result := Format('R%04d%02d%02d%02d%02d%02d', [Year, Month, Day, Hour, Min, Sec]);
end;

function GetCurrentShiftType: string;
var
  CurrentHour: Integer;
begin
  CurrentHour := HourOf(Now);
  if (CurrentHour >= 6) and (CurrentHour < 18) then
    Result := 'MORNING'
  else
    Result := 'EVENING';
end;

end.
