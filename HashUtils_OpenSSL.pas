unit HashUtils_OpenSSL;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils;

function HashPassword(const Password: string): string;
function VerifyPassword(const Password, Hash: string): Boolean;
function GenerateReceiptNumber: string;
function GetCurrentShiftType: string;
function GenerateSalt: string;
function HashPasswordWithSalt(const Password, Salt: string): string;

implementation

uses
  DateUtils;

{$IFDEF WINDOWS}
  {$LINKLIB libeay32}
{$ENDIF}

function GenerateSalt: string;
var
  i: Integer;
  SaltBytes: array[0..15] of Byte;
begin
  // Generate random salt
  Randomize;
  for i := 0 to 15 do
    SaltBytes[i] := Random(256);
  
  Result := '';
  for i := 0 to 15 do
    Result := Result + IntToHex(SaltBytes[i], 2);
  Result := LowerCase(Result);
end;

// Simple hash function using built-in CRC32 and string manipulation
function SimpleHash(const Input: string): string;
var
  i, Hash: Cardinal;
  c: Char;
begin
  Hash := $FFFFFFFF;
  
  for c in Input do
  begin
    Hash := Hash xor Ord(c);
    for i := 0 to 7 do
    begin
      if (Hash and 1) <> 0 then
        Hash := (Hash shr 1) xor $EDB88320
      else
        Hash := Hash shr 1;
    end;
  end;
  
  Hash := Hash xor $FFFFFFFF;
  
  // Make it longer by combining with string length and character sum
  Result := IntToHex(Hash, 8);
  Hash := Length(Input);
  for c in Input do
    Hash := Hash + Ord(c);
  
  Result := Result + IntToHex(Hash, 8);
  Result := LowerCase(Result);
end;

function HashPasswordWithSalt(const Password, Salt: string): string;
begin
  Result := SimpleHash(Password + Salt + 'POS_SYSTEM_SALT');
end;

function HashPassword(const Password: string): string;
begin
  Result := SimpleHash(Password + 'POS_SYSTEM_DEFAULT_SALT');
end;

function VerifyPassword(const Password, Hash: string): Boolean;
begin
  Result := HashPassword(Password) = LowerCase(Hash);
end;

function GenerateReceiptNumber: string;
var
  Year, Month, Day, Hour, Min, Sec, MSec: Word;
begin
  DecodeDateTime(Now, Year, Month, Day, Hour, Min, Sec, MSec);
  Result := Format('R%04d%02d%02d%02d%02d%02d', [Year, Month, Day, Hour, Min, Sec]);
end;

function GetCurrentShiftType: string;
var
  CurrentHour: Integer;
begin
  CurrentHour := HourOf(Now);
  if (CurrentHour >= 6) and (CurrentHour < 18) then
    Result := 'MORNING'
  else
    Result := 'EVENING';
end;

end.
