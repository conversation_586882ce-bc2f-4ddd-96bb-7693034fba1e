unit HashUtils_Salted;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, sha256, sha1;

function HashPassword(const Password: string): string;
function VerifyPassword(const Password, Hash: string): Boolean;
function GenerateReceiptNumber: string;
function GetCurrentShiftType: string;
function GenerateSalt: string;
function HashPasswordWithSalt(const Password, Salt: string): string;

implementation

uses
  DateUtils;

function GenerateSalt: string;
var
  i: Integer;
  SaltBytes: array[0..15] of Byte;
begin
  // Generate random salt
  Randomize;
  for i := 0 to 15 do
    SaltBytes[i] := Random(256);
  
  Result := '';
  for i := 0 to 15 do
    Result := Result + IntToHex(SaltBytes[i], 2);
  Result := LowerCase(Result);
end;

function HashPasswordWithSalt(const Password, Salt: string): string;
var
  Source: string;
  Hash: TSHA256Digest;
  i: Integer;
begin
  Source := Password + Salt;
  Hash := SHA256String(Source);
  
  Result := '';
  for i := 0 to Length(Hash) - 1 do
    Result := Result + IntToHex(Hash[i], 2);
  Result := LowerCase(Result);
end;

function HashPassword(const Password: string): string;
var
  Hash: TSHA256Digest;
  i: Integer;
begin
  Hash := SHA256String(Password);
  
  Result := '';
  for i := 0 to Length(Hash) - 1 do
    Result := Result + IntToHex(Hash[i], 2);
  Result := LowerCase(Result);
end;

function VerifyPassword(const Password, Hash: string): Boolean;
begin
  Result := HashPassword(Password) = LowerCase(Hash);
end;

function GenerateReceiptNumber: string;
var
  Year, Month, Day, Hour, Min, Sec, MSec: Word;
begin
  DecodeDateTime(Now, Year, Month, Day, Hour, Min, Sec, MSec);
  Result := Format('R%04d%02d%02d%02d%02d%02d', [Year, Month, Day, Hour, Min, Sec]);
end;

function GetCurrentShiftType: string;
var
  CurrentHour: Integer;
begin
  CurrentHour := HourOf(Now);
  if (CurrentHour >= 6) and (CurrentHour < 18) then
    Result := 'MORNING'
  else
    Result := 'EVENING';
end;

end.
