object frmItemSelect: TfrmItemSelect
  Left = 372
  Height = 400
  Top = 216
  Width = 500
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'Select Item'
  ClientHeight = 400
  ClientWidth = 500
  Color = clBtnFace
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  OnCreate = FormCreate
  OnShow = FormShow
  Position = poScreenCenter
  LCLVersion = '2.2.6.0'
  object pnlMain: TPanel
    Left = 0
    Height = 400
    Top = 0
    Width = 500
    Align = alClient
    BevelOuter = bvNone
    ClientHeight = 400
    ClientWidth = 500
    TabOrder = 0
    object pnlTop: TPanel
      Left = 0
      Height = 60
      Top = 0
      Width = 500
      Align = alTop
      BevelOuter = bvNone
      ClientHeight = 60
      ClientWidth = 500
      TabOrder = 0
      object lblSearch: TLabel
        Left = 20
        Height = 13
        Top = 15
        Width = 38
        Caption = 'Search:'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
      end
      object edtSearch: TEdit
        Left = 70
        Height = 21
        Top = 12
        Width = 200
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
        TabOrder = 0
        OnChange = edtSearchChange
      end
      object lblCategory: TLabel
        Left = 290
        Height = 13
        Top = 15
        Width = 51
        Caption = 'Category:'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
      end
      object cbCategory: TComboBox
        Left = 350
        Height = 21
        Top = 12
        Width = 120
        ItemHeight = 13
        Items.Strings = (
          'All Categories'
          'Pool Tables'
          'Snooker Tables'
          'Food'
          'Beverages'
          'Snacks'
          'Other'
        )
        Style = csDropDownList
        TabOrder = 1
        Text = 'All Categories'
        OnChange = cbCategoryChange
      end
    end
    object pnlCenter: TPanel
      Left = 0
      Height = 280
      Top = 60
      Width = 500
      Align = alClient
      BevelOuter = bvNone
      ClientHeight = 280
      ClientWidth = 500
      TabOrder = 1
      object sgItems: TStringGrid
        Left = 20
        Height = 240
        Top = 20
        Width = 460
        ColCount = 4
        DefaultColWidth = 110
        DefaultRowHeight = 25
        FixedCols = 0
        RowCount = 1
        ScrollBars = ssAutoVertical
        TabOrder = 0
        OnDblClick = sgItemsDblClick
        ColWidths = (
          200
          100
          80
          70
        )
      end
    end
    object pnlBottom: TPanel
      Left = 0
      Height = 60
      Top = 340
      Width = 500
      Align = alBottom
      BevelOuter = bvNone
      ClientHeight = 60
      ClientWidth = 500
      TabOrder = 2
      object lblQuantity: TLabel
        Left = 20
        Height = 13
        Top = 15
        Width = 48
        Caption = 'Quantity:'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
      end
      object edtQuantity: TEdit
        Left = 80
        Height = 21
        Top = 12
        Width = 60
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
        TabOrder = 0
        Text = '1'
      end
      object btnOK: TButton
        Left = 250
        Height = 35
        Top = 10
        Width = 80
        Caption = '&OK'
        Default = True
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        TabOrder = 1
        OnClick = btnOKClick
      end
      object btnCancel: TButton
        Left = 340
        Height = 35
        Top = 10
        Width = 80
        Cancel = True
        Caption = '&Cancel'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
        TabOrder = 2
        OnClick = btnCancelClick
      end
    end
  end
end