unit Logging;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms;

type
  TLogLevel = (llDebug, llInfo, llWarning, llError, llCritical);
  
  TLogger = class
  private
    FLogFile: TextFile;
    FLogPath: string;
    FInitialized: Boolean;
    procedure InitializeLog;
    function LogLevelToString(ALevel: TLogLevel): string;
  public
    constructor Create;
    destructor Destroy; override;
    
    procedure Log(const AMessage: string; ALevel: TLogLevel = llInfo);
    procedure LogDebug(const AMessage: string);
    procedure LogInfo(const AMessage: string);
    procedure LogWarning(const AMessage: string);
    procedure LogError(const AMessage: string);
    procedure LogCritical(const AMessage: string);
  end;

function Logger: TLogger;

implementation
uses
  Windows, ShlObj;

var
  _Logger: TLogger = nil;

function GetLocalAppDataPath: string;
var
  Path: array[0..MAX_PATH] of Char;
begin
  if SHGetFolderPath(0, CSIDL_LOCAL_APPDATA, 0, 0, @Path) = S_OK then
    Result := IncludeTrailingPathDelimiter(Path) + 'SNP\logs\'
  else
    Result := '';
end;


constructor TLogger.Create;
begin
  inherited Create;
  //FLogPath := GetLocalAppDataPath;
  FLogPath := IncludeTrailingPathDelimiter(ExtractFilePath(Application.ExeName) + 'logs') ;
  FInitialized := False;
  InitializeLog;
end;

destructor TLogger.Destroy;
begin
  if FInitialized then
    CloseFile(FLogFile);
  inherited Destroy;
end;


procedure TLogger.InitializeLog;
var
  LogFileName: string;
begin
  try
    if not DirectoryExists(FLogPath) then
      ForceDirectories(FLogPath);

    LogFileName := FLogPath + 'snpfnb_' + FormatDateTime('yyyymmdd', Now) + '.log';

    if FileExists(LogFileName) and (FileSetAttr(LogFileName, faArchive) = 0) then
      AssignFile(FLogFile, LogFileName)
    else
      AssignFile(FLogFile, LogFileName);

    if FileExists(LogFileName) then
      Append(FLogFile)
    else
      Rewrite(FLogFile);

    FInitialized := True;
    Log('Application started', llInfo);
  except
    on E: Exception do
      FInitialized := False;
  end;
end;

function TLogger.LogLevelToString(ALevel: TLogLevel): string;
begin
  case ALevel of
    llDebug: Result := 'DEBUG';
    llInfo: Result := 'INFO';
    llWarning: Result := 'WARNING';
    llError: Result := 'ERROR';
    llCritical: Result := 'CRITICAL';
  end;
end;

procedure TLogger.Log(const AMessage: string; ALevel: TLogLevel);
var
  LogEntry: string;
begin
  if not FInitialized then
    Exit;
    
  try
    LogEntry := Format('[%s] [%s] %s', [
      FormatDateTime('yyyy-mm-dd hh:nn:ss', Now),
      LogLevelToString(ALevel),
      AMessage
    ]);
    
    WriteLn(FLogFile, LogEntry);
    Flush(FLogFile);
  except
    // Silent fail for logging
  end;
end;

procedure TLogger.LogDebug(const AMessage: string);
begin
  Log(AMessage, llDebug);
end;

procedure TLogger.LogInfo(const AMessage: string);
begin
  Log(AMessage, llInfo);
end;

procedure TLogger.LogWarning(const AMessage: string);
begin
  Log(AMessage, llWarning);
end;

procedure TLogger.LogError(const AMessage: string);
begin
  Log(AMessage, llError);
end;

procedure TLogger.LogCritical(const AMessage: string);
begin
  Log(AMessage, llCritical);
end;

function Logger: TLogger;
begin
  if _Logger = nil then
    _Logger := TLogger.Create;
  Result := _Logger;
end;

initialization

finalization
  if _Logger <> nil then
    _Logger.Free;

end.
