unit MigrationUtils;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Logging, ConfigUtils, DB, SQLDB, SQLite3Conn,
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  FileUtil, IniFiles;

procedure PerformMigrationIfNeeded;

implementation

const
  OLD_DB_NAME = 'pos_system.db';
  MIGRATION_FLAG_FILE = 'migration.done';

procedure MigrateSettings;
var
  OldDbConnection: TSQLite3Connection;
  OldQuery: TSQLQuery;
  OldDbPath: string;
  SettingName, SettingValue: string;
begin
  OldDbPath := ExtractFilePath(ParamStr(0)) + OLD_DB_NAME;

  if not FileExists(OldDbPath) then
  begin
    if Assigned(Logger) then
      Logger.LogInfo('Old database not found. No migration needed.');
    Exit;
  end;

  if Assigned(Logger) then
    Logger.LogInfo('Starting settings migration from ' + OLD_DB_NAME);

  OldDbConnection := TSQLite3Connection.Create(nil);
  OldQuery := TSQLQuery.Create(nil);
  try
    OldDbConnection.DatabaseName := OldDbPath;
    OldQuery.Database := OldDbConnection;
    OldDbConnection.Connected := True;

    OldQuery.SQL.Text := 'SELECT setting_name, setting_value FROM system_settings';
    OldQuery.Open;

    while not OldQuery.EOF do
    begin
      SettingName := OldQuery.FieldByName('setting_name').AsString;
      SettingValue := OldQuery.FieldByName('setting_value').AsString;

      OldQuery.Next;
    end;

    Config.SaveConfig;

    if Assigned(Logger) then
      Logger.LogInfo('Settings migration completed successfully.');

  finally
    OldQuery.Free;
    OldDbConnection.Free;
  end;
end;

procedure MigrateIniSettings;
var
  Ini: TIniFile;
  IniPath: string;
  Sections, Keys: TStringList;
  i, j: Integer;
begin
  IniPath := ExtractFilePath(ParamStr(0)) + 'config.ini';
  if not FileExists(IniPath) then
  begin
    if Assigned(Logger) then
      Logger.LogInfo('config.ini not found. No INI migration needed.');
    Exit;
  end;

  if Assigned(Logger) then
    Logger.LogInfo('Starting INI settings migration from config.ini');

  Ini := TIniFile.Create(IniPath);
  Sections := TStringList.Create;
  Keys := TStringList.Create;
  try
    Ini.ReadSections(Sections);
    for i := 0 to Sections.Count - 1 do
    begin
      Ini.ReadSection(Sections[i], Keys);
      for j := 0 to Keys.Count - 1 do
      begin
        Config.SetValue(Sections[i], Keys[j], Ini.ReadString(Sections[i], Keys[j], ''));
      end;
    end;
    Config.SaveConfig;

    if Assigned(Logger) then
      Logger.LogInfo('INI settings migration completed successfully.');
  finally
    Keys.Free;
    Sections.Free;
    Ini.Free;
  end;
end;

procedure PerformMigrationIfNeeded;
var
  FlagFilePath: string;
  FileHandle: THandle;
begin
  FlagFilePath := ExtractFilePath(ParamStr(0)) + MIGRATION_FLAG_FILE;
  if FileExists(FlagFilePath) then
    Exit; // Migration already done

  try
    //MigrateSettings;
    MigrateIniSettings; // <-- Add this line

    // Create the flag file to prevent running the migration again
    FileHandle := FileCreate(FlagFilePath);
    if FileHandle <> INVALID_HANDLE_VALUE then
      FileClose(FileHandle);

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Failed to perform settings migration: ' + E.Message);
      // We don't re-raise, as the application might still be able to run with default settings.
    end;
  end;
end;

end.
