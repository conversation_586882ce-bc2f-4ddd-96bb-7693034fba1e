unit POS_Methods;

interface

uses
  Classes, SysUtils, DB, SQLDB;

implementation

uses
  DataModule, Logging;

{ TdmMain POS Methods }

function TdmMain.GetCategories: TSQLQuery;
begin
  try
    qryCategories.Close;
    qryCategories.SQL.Text := 'SELECT id, category_name, description, picture ' +
                             'FROM categories ' +
                             'WHERE is_active = 1 ' +
                             'ORDER BY category_name';
    qryCategories.Open;
    Result := qryCategories;
  except
    on E: Exception do
    begin
      Logger.LogError('Error in GetCategories: ' + E.Message);
      raise;
    end;
  end;
end;

function TdmMain.GetItemsByCategory(CategoryID: Integer): TSQLQuery;
begin
  try
    qryItems.Close;
    qryItems.SQL.Text := 'SELECT id, item_code, item_name, unit_price, picture, barcode ' +
                        'FROM items ' +
                        'WHERE category_id = :category_id AND is_active = 1 ' +
                        'ORDER BY item_name';
    qryItems.ParamByName('category_id').AsInteger := CategoryID;
    qryItems.Open;
    Result := qryItems;
  except
    on E: Exception do
    begin
      Logger.LogError('Error in GetItemsByCategory: ' + E.Message);
      raise;
    end;
  end;
end;

function TdmMain.GetItemDetails(ItemID: Integer): Boolean;
begin
  Result := False;
  try
    qryItems.Close;
    qryItems.SQL.Text := 'SELECT i.*, c.category_name ' +
                        'FROM items i ' +
                        'LEFT JOIN categories c ON i.category_id = c.id ' +
                        'WHERE i.id = :item_id';
    qryItems.ParamByName('item_id').AsInteger := ItemID;
    qryItems.Open;
    Result := not qryItems.IsEmpty;
  except
    on E: Exception do
    begin
      Logger.LogError('Error in GetItemDetails: ' + E.Message);
      raise;
    end;
  end;
end;

end.
