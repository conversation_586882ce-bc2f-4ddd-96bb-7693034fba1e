unit PrinterUtils;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Printers, Printer4Lazarus, Logging;

procedure PrintReceipt(const Lines: TStringList; const PrinterName: string);
function GetAvailablePrinters: TStringList;
function SetDefaultPrinter(const PrinterName: string): Boolean;
function IsPrinterSystemAvailable: Boolean;

implementation

function IsPrinterSystemAvailable: Boolean;
begin
  Result := False;
  try
    // Try to access printer count - this will fail if printer system has issues
    Result := Printer.Printers.Count > 0;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Printer system not available: ' + E.Message);
      Result := False;
    end;
  end;
end;

procedure PrintReceipt(const Lines: TStringList; const PrinterName: string);
var
  i: Integer;
  OldPrinterIndex: Integer;
  PrinterIndex: Integer;
begin
  if not Assigned(Lines) or (Lines.Count = 0) then
    Exit;
    
  try
    // Check if printer system is working
    if not IsPrinterSystemAvailable then
    begin
      raise Exception.Create('No printers available or printer system not working');
    end;
    
    OldPrinterIndex := -1;
    PrinterIndex := 0;
    
    // Safely get current printer index
    try
      OldPrinterIndex := Printer.PrinterIndex;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogWarning('Could not get current printer index: ' + E.Message);
        OldPrinterIndex := -1;
      end;
    end;
    
    // Find the specified printer
    if PrinterName <> '' then
    begin
      for i := 0 to Printer.Printers.Count - 1 do
      begin
        try
          if Pos(UpperCase(PrinterName), UpperCase(Printer.Printers[i])) > 0 then
          begin
            PrinterIndex := i;
            Break;
          end;
        except
          on E: Exception do
          begin
            if Assigned(Logger) then
              Logger.LogWarning('Error checking printer ' + IntToStr(i) + ': ' + E.Message);
            Continue;
          end;
        end;
      end;
    end;
    
    // Safely set printer index
    try
      if (PrinterIndex >= 0) and (PrinterIndex < Printer.Printers.Count) then
        Printer.PrinterIndex := PrinterIndex;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Could not set printer index to ' + IntToStr(PrinterIndex) + ': ' + E.Message);
        raise Exception.Create('Could not select printer: ' + E.Message);
      end;
    end;
    
    try
      Printer.BeginDoc;
      try
        // Print each line with better formatting
        for i := 0 to Lines.Count - 1 do
        begin
          try
            // Use better spacing for receipt printing
            Printer.Canvas.TextOut(10, 10 + (i * Printer.Canvas.TextHeight('A') + 2), Lines[i]);
          except
            on E: Exception do
            begin
              if Assigned(Logger) then
                Logger.LogError('Error printing line ' + IntToStr(i) + ': ' + E.Message);
              // Continue printing other lines
            end;
          end;
        end;
        
      finally
        try
          Printer.EndDoc;
        except
          on E: Exception do
          begin
            if Assigned(Logger) then
              Logger.LogError('Error ending print document: ' + E.Message);
            // Try to abort the print job
            try
              Printer.Abort;
            except
              // Ignore abort errors
            end;
          end;
        end;
      end;
      
      if Assigned(Logger) then
        Logger.LogInfo('Receipt printed successfully');
        
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Print error: ' + E.Message);
        raise;
      end;
    end;
    
  finally
    // Safely restore original printer
    if (OldPrinterIndex >= 0) and (OldPrinterIndex < Printer.Printers.Count) then
    begin
      try
        Printer.PrinterIndex := OldPrinterIndex;
      except
        on E: Exception do
        begin
          if Assigned(Logger) then
            Logger.LogWarning('Could not restore original printer index: ' + E.Message);
        end;
      end;
    end;
  end;
end;

function GetAvailablePrinters: TStringList;
var
  i: Integer;
begin
  Result := TStringList.Create;
  
  try
    // Check if printer system is working
    if not IsPrinterSystemAvailable then
    begin
      if Assigned(Logger) then
        Logger.LogInfo('No printers available');
      Exit;
    end;
    
    // Get printer list
    for i := 0 to Printer.Printers.Count - 1 do
    begin
      try
        Result.Add(Printer.Printers[i]);
      except
        on E: Exception do
        begin
          if Assigned(Logger) then
            Logger.LogWarning('Error getting printer ' + IntToStr(i) + ': ' + E.Message);
          Continue;
        end;
      end;
    end;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error getting printers: ' + E.Message);
    end;
  end;
end;

function SetDefaultPrinter(const PrinterName: string): Boolean;
var
  i: Integer;
begin
  Result := False;
  
  try
    // Check if printer system is working
    if not IsPrinterSystemAvailable then
      Exit;
    
    for i := 0 to Printer.Printers.Count - 1 do
    begin
      try
        if Pos(UpperCase(PrinterName), UpperCase(Printer.Printers[i])) > 0 then
        begin
          Printer.PrinterIndex := i;
          Result := True;
          if Assigned(Logger) then
            Logger.LogInfo('Default printer set to: ' + Printer.Printers[i]);
          Break;
        end;
      except
        on E: Exception do
        begin
          if Assigned(Logger) then
            Logger.LogWarning('Error checking/setting printer ' + IntToStr(i) + ': ' + E.Message);
          Continue;
        end;
      end;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error setting default printer: ' + E.Message);
    end;
  end;
end;

end.
