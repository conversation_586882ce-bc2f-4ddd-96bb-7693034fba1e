object frmReceiptPreview: TfrmReceiptPreview
  Left = 192
  Height = 600
  Top = 108
  Width = 400
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'Receipt Preview'
  ClientHeight = 600
  ClientWidth = 400
  Color = clBtnFace
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  OnCreate = FormCreate
  Position = poScreenCenter
  LCLVersion = '*******'
  object pnlMain: TPanel
    Left = 0
    Height = 600
    Top = 0
    Width = 400
    Align = alClient
    BevelOuter = bvNone
    ClientHeight = 600
    ClientWidth = 400
    TabOrder = 0
    object pnlReceipt: TPanel
      Left = 0
      Height = 540
      Top = 0
      Width = 400
      Align = alClient
      BevelOuter = bvLowered
      ClientHeight = 538
      ClientWidth = 398
      Color = clWhite
      ParentColor = False
      TabOrder = 0
      object memoReceipt: TMemo
        Left = 10
        Height = 518
        Top = 10
        Width = 378
        Font.Height = -11
        Font.Name = 'Courier New'
        Lines.Strings = (
          'Receipt Preview'
        )
        ParentFont = False
        ReadOnly = True
        ScrollBars = ssAutoVertical
        TabOrder = 0
      end
    end
    object pnlButtons: TPanel
      Left = 0
      Height = 60
      Top = 540
      Width = 400
      Align = alBottom
      BevelOuter = bvNone
      ClientHeight = 60
      ClientWidth = 400
      TabOrder = 1
      object btnPrint: TButton
        Left = 50
        Height = 35
        Top = 12
        Width = 100
        Caption = '&Print'
        Default = True
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        TabOrder = 0
        OnClick = btnPrintClick
      end
      object btnSave: TButton
        Left = 160
        Height = 35
        Top = 12
        Width = 100
        Caption = '&Save'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
        TabOrder = 1
        OnClick = btnSaveClick
      end
      object btnClose: TButton
        Left = 270
        Height = 35
        Top = 12
        Width = 100
        Cancel = True
        Caption = '&Close'
        Font.Height = -11
        Font.Name = 'Tahoma'
        ParentFont = False
        TabOrder = 2
        OnClick = btnCloseClick
      end
    end
  end
  object SaveDialog1: TSaveDialog
    DefaultExt = '.txt'
    Filter = 'Text Files|*.txt|All Files|*.*'
    Left = 320
    Top = 40
  end
end