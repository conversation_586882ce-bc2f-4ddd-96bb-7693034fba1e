unit StringUtils;

{$mode objfpc}{$H+}

interface

function PadRight(const Text: string; Width: Integer): string;
function PadLeft(const Text: string; Width: Integer): string;
function CenterText(const Text: string; Width: Integer): string;
function IfThen(Condition: Boolean; const TrueValue, FalseValue: string): string;

implementation

function PadRight(const Text: string; Width: Integer): string;
begin
  Result := Text + StringOfChar(' ', Width - Length(Text));
  if Length(Result) > Width then
    Result := Copy(Result, 1, Width);
end;

function PadLeft(const Text: string; Width: Integer): string;
begin
  Result := StringOfChar(' ', Width - Length(Text)) + Text;
  if Length(Result) > Width then
    Result := Copy(Result, Length(Result) - Width + 1, Width);
end;

function CenterText(const Text: string; Width: Integer): string;
var
  Spaces: Integer;
begin
  if Length(Text) >= Width then
    Result := Copy(Text, 1, Width)
  else
  begin
    Spaces := (Width - Length(Text)) div 2;
    Result := StringOfChar(' ', Spaces) + Text;
  end;
end;

function IfThen(Condition: Boolean; const TrueValue, FalseValue: string): string;
begin
  if Condition then
    Result := TrueValue
  else
    Result := FalseValue;
end;

end.
