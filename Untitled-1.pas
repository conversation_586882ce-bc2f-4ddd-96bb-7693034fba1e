function TdmMain.StartShift(UserID: Integer; const ShiftType: string; OpeningCash: Currency): Integer;
begin
  Result := 0;
  
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    // Fix: Use the correct schema - SHIFT_DATE is a separate DATE column
    qryGeneral.SQL.Add('INSERT INTO SHIFTS (SHIFT_DATE, SHIFT_TYPE, USER_ID, OPENING_CASH) VALUES (');
    qryGeneral.SQL.Add('CURRENT_DATE, :shift_type, :user_id, :opening_cash)');
    qryGeneral.SQL.Add('RETURNING ID');
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.ParamByName('user_id').AsInteger := UserID;
    qryGeneral.ParamByName('opening_cash').AsCurrency := OpeningCash;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('ID').AsInteger;
    
    qryGeneral.Close;
    
    if Result > 0 then
    begin
      transMain.Commit;
      if Assigned(Logger) then
        Logger.LogInfo('StartShift successful: ID=' + IntToStr(Result));
    end
    else
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('StartShift failed - no ID returned');
    end;
    
  except
    on E: Exception do
    begin
      transMain.Rollback;
      if Assigned(Logger) then
        Logger.LogError('StartShift error: ' + E.Message);
      Result := 0;
    end;
  end;
end;

function TdmMain.GetCurrentShift(const ShiftType: string): Integer;
begin
  Result := 0;
  
  try
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    // Fix: Use SHIFT_DATE column as defined in schema
    qryGeneral.SQL.Add('SELECT ID FROM SHIFTS WHERE SHIFT_TYPE = :shift_type AND IS_CLOSED = ''N'' ');
    qryGeneral.SQL.Add('AND SHIFT_DATE = CURRENT_DATE ORDER BY START_TIME DESC');
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('ID').AsInteger;
    
    qryGeneral.Close;
    
    if Assigned(Logger) then
      Logger.LogInfo('GetCurrentShift for ' + ShiftType + ': ' + IntToStr(Result));
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCurrentShift error: ' + E.Message);
      Result := 0;
    end;
  end;
end;

function TdmMain.GetShiftSales(ShiftID: Integer): Boolean;
begin
  Result := False;
  try
    // Get shift details first
    qryGeneral.Close;
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT SHIFT_DATE, SHIFT_TYPE, USER_ID FROM SHIFTS WHERE ID = :shift_id');
    qryGeneral.ParamByName('shift_id').AsInteger := ShiftID;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
    begin
      qrySales.Close;
      qrySales.SQL.Clear;
      qrySales.SQL.Add('SELECT RECEIPT_NO, SALE_DATE, TOTAL_AMOUNT, PAYMENT_METHOD');
      qrySales.SQL.Add('FROM SALES');
      qrySales.SQL.Add('WHERE CASHIER_ID = :user_id AND SHIFT_TYPE = :shift_type');
      qrySales.SQL.Add('AND CAST(SALE_DATE AS DATE) = :shift_date');
      qrySales.SQL.Add('ORDER BY SALE_DATE');
      qrySales.ParamByName('user_id').AsInteger := qryGeneral.FieldByName('USER_ID').AsInteger;
      qrySales.ParamByName('shift_type').AsString := qryGeneral.FieldByName('SHIFT_TYPE').AsString;
      qrySales.ParamByName('shift_date').AsDate := qryGeneral.FieldByName('SHIFT_DATE').AsDateTime;
      qrySales.Open;
      
      Result := qrySales.Active;
    end;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetShiftSales error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCashBalance(const ShiftType: string; TransactionDate: TDate): Currency;
begin
  Result := 0;
  
  try
    qryGeneral.SQL.Clear;
    qryGeneral.SQL.Add('SELECT ');
    qryGeneral.SQL.Add('  SUM(CASE WHEN TRANSACTION_TYPE IN (''CASH_IN'', ''SALE'') THEN AMOUNT ELSE 0 END) - ');
    qryGeneral.SQL.Add('  SUM(CASE WHEN TRANSACTION_TYPE = ''CASH_OUT'' THEN AMOUNT ELSE 0 END) as BALANCE ');
    qryGeneral.SQL.Add('FROM CASH_TRANSACTIONS ');
    qryGeneral.SQL.Add('WHERE SHIFT_TYPE = :shift_type AND CAST(TRANSACTION_DATE AS DATE) = :transaction_date');
    qryGeneral.ParamByName('shift_type').AsString := ShiftType;
    qryGeneral.ParamByName('transaction_date').AsDate := TransactionDate;
    qryGeneral.Open;
    
    if not qryGeneral.EOF then
      Result := qryGeneral.FieldByName('BALANCE').AsCurrency;
    
    qryGeneral.Close;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashBalance error: ' + E.Message);
    end;
  end;
end;

function TdmMain.GetCashTransactions(const ShiftType: string; TransactionDate: TDate): Boolean;
begin
  Result := False;
  
  try
    qryCashTransactions.SQL.Clear;
    qryCashTransactions.SQL.Add('SELECT ct.*, u.FULL_NAME as USER_NAME FROM CASH_TRANSACTIONS ct ');
    qryCashTransactions.SQL.Add('LEFT JOIN USERS u ON ct.USER_ID = u.ID ');
    qryCashTransactions.SQL.Add('WHERE ct.SHIFT_TYPE = :shift_type AND CAST(ct.TRANSACTION_DATE AS DATE) = :transaction_date ');
    qryCashTransactions.SQL.Add('ORDER BY ct.TRANSACTION_DATE DESC');
    qryCashTransactions.ParamByName('shift_type').AsString := ShiftType;
    qryCashTransactions.ParamByName('transaction_date').AsDate := TransactionDate;
    qryCashTransactions.Open;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('GetCashTransactions error: ' + E.Message);
    end;
  end;
end;
