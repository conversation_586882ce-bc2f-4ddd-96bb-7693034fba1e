unit authunit;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, SyncObjs, Forms, LoginForm, DataModule, Logging, DCPsha256;

type
  TAuth = class
  private
    class var FInstance: TAuth;
    class var FCriticalSection: TCriticalSection;
    FIsAuthenticated: Boolean;
    FUserRole: string;
    FUserId: Integer;
    FUsername: string;
    
    function GenerateSalt: string;
    function HashPassword(const Password, Salt: string): string;
    function InternalAuthenticate(const AUsername, APassword: string): Boolean;
  public
    constructor Create;
    class function GetInstance: TAuth;
    class procedure ReleaseInstance;
    
    function Authenticate: Boolean;
    procedure Logout;
    property IsAuthenticated: Boolean read FIsAuthenticated;
    property UserRole: string read FUserRole;
    property Username: string read FUsername;
  end;

implementation

{ TAuth }

constructor TAuth.Create;
begin
  inherited Create;
  FIsAuthenticated := False;
  FUserRole := '';
  FUserId := 0;
  FUsername := '';
end;

class function TAuth.GetInstance: TAuth;
begin
  if FInstance = nil then
  begin
    FCriticalSection.Enter;
    try
      if FInstance = nil then
        FInstance := TAuth.Create;
    finally
      FCriticalSection.Leave;
    end;
  end;
  Result := FInstance;
end;

class procedure TAuth.ReleaseInstance;
begin
  FCriticalSection.Enter;
  try
    if Assigned(FInstance) then
    begin
      if FInstance.IsAuthenticated then
        FInstance.Logout;
      FreeAndNil(FInstance);
    end;
  finally
    FCriticalSection.Leave;
  end;
end;

function TAuth.Authenticate: Boolean;
var
  LoginForm: TLoginForm;
begin
  Result := False;
  LoginForm := TLoginForm.Create(nil);
  try
    if LoginForm.ShowModal = mrOk then
    begin
      FCriticalSection.Enter;
      try
        Result := InternalAuthenticate(LoginForm.GetUsername, LoginForm.GetPassword);
      finally
        FCriticalSection.Leave;
      end;
    end;
  finally
    LoginForm.Free;
  end;
end;

function TAuth.InternalAuthenticate(const AUsername, APassword: string): Boolean;
var
  Salt, CombinedHash, StoredHash: string;
begin
  Result := False;
  with DataModule1 do
  begin
    SQLTransaction1.StartTransaction;
    try
      qUsers.Close;
      qUsers.SQL.Text := 'SELECT ID, PASSWORD_HASH, IS_ADMIN FROM USERS WHERE USERNAME = :user';
      qUsers.ParamByName('user').AsString := AUsername;
      qUsers.Open;

      if not qUsers.IsEmpty then
      begin
        CombinedHash := qUsers.FieldByName('PASSWORD_HASH').AsString;
        Salt := Copy(CombinedHash, 1, 16);
        StoredHash := Copy(CombinedHash, 17, Length(CombinedHash));
        
        if HashPassword(APassword, Salt) = StoredHash then
        begin
          FIsAuthenticated := True;
          FUsername := AUsername;
          FUserRole := qUsers.FieldByName('IS_ADMIN').AsBoolean ? 'Admin' : 'User';
          SQLTransaction1.Commit;
          Logger.Log('User authenticated: ' + FUsername);
          Exit(True);
        end;
      end;
      SQLTransaction1.Rollback;
    except
      SQLTransaction1.Rollback;
      Logger.Log('Auth error for ' + AUsername, llError);
    end;
  end;
end;

procedure TAuth.Logout;
begin
  FCriticalSection.Enter;
  try
    FIsAuthenticated := False;
    FUserRole := '';
    FUsername := '';
    Logger.Log('User logged out');
  finally
    FCriticalSection.Leave;
  end;
end;

function TAuth.GenerateSalt: string;
var
  i: Integer;
  Bytes: array[0..7] of Byte;
begin
  Randomize;
  for i := 0 to 7 do
    Bytes[i] := Random(256);
  Result := LowerCase(HexStr(@Bytes[0], 8));
end;

function TAuth.HashPassword(const Password, Salt: string): string;
var
  Hash: TDCP_sha256;
  Digest: array[0..31] of Byte;
begin
  Hash := TDCP_sha256.Create(nil);
  try
    Hash.Init;
    Hash.UpdateStr(Password + Salt);
    Hash.Final(Digest);
    Result := LowerCase(HexStr(@Digest[0], 32));
  finally
    Hash.Free;
  end;
end;

initialization
  TAuth.FCriticalSection := TCriticalSection.Create;

finalization
  TAuth.ReleaseInstance;
end.
