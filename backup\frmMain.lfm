object frmMain: TfrmMain
  Left = 633
  Height = 618
  Top = 0
  Width = 638
  Caption = 'Pool & Snooker Center - POS System'
  ClientHeight = 618
  ClientWidth = 638
  Color = clBtnFace
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Menu = MainMenu1
  OnClose = FormClose
  OnCreate = FormCreate
  OnShow = FormShow
  Position = poScreenCenter
  LCLVersion = '3.8.0.0'
  WindowState = wsMaximized
  object StatusBar1: TStatusBar
    Left = 0
    Height = 23
    Top = 595
    Width = 638
    Panels = <    
      item
        Width = 200
      end    
      item
        Width = 150
      end    
      item
        Width = 200
      end    
      item
        Width = 150
      end>
    SimplePanel = False
  end
  object pnlMain: TPanel
    Left = 0
    Height = 595
    Top = 0
    Width = 638
    Align = alClient
    BevelOuter = bvNone
    ClientHeight = 595
    ClientWidth = 638
    Color = clWhite
    ParentBackground = False
    ParentColor = False
    TabOrder = 1
    object pnlLeft: TPanel
      Left = 0
      Height = 595
      Top = 0
      Width = 600
      Align = alLeft
      ClientHeight = 595
      ClientWidth = 600
      ParentBackground = False
      TabOrder = 0
      object pnlCategories: TPanel
        Left = 1
        Height = 200
        Top = 1
        Width = 598
        Align = alTop
        ClientHeight = 200
        ClientWidth = 598
        ParentBackground = False
        TabOrder = 0
        object lblCategories: TLabel
          Left = 8
          Height = 15
          Top = 8
          Width = 59
          Caption = 'Categories'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object lbCategories: TListBox
          Left = 8
          Height = 160
          Top = 32
          Width = 582
          Anchors = [akTop, akLeft, akRight, akBottom]
          ItemHeight = 0
          TabOrder = 0
          OnClick = lbCategoriesClick
        end
      end
      object pnlItems: TPanel
        Left = 1
        Height = 393
        Top = 201
        Width = 598
        Align = alClient
        ClientHeight = 393
        ClientWidth = 598
        ParentBackground = False
        TabOrder = 1
        object lblItems: TLabel
          Left = 8
          Height = 15
          Top = 8
          Width = 32
          Caption = 'Items'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object sgItems: TStringGrid
          Left = 8
          Height = 317
          Top = 32
          Width = 582
          Anchors = [akTop, akLeft, akRight, akBottom]
          ColCount = 4
          FixedCols = 0
          RowCount = 1
          TabOrder = 0
          OnDblClick = sgItemsDblClick
          ColWidths = (
            80
            300
            80
            80
          )
        end
        object btnItemSearch: TBitBtn
          Left = 8
          Height = 25
          Top = 357
          Width = 100
          Anchors = [akLeft, akBottom]
          Caption = 'Search Items'
          OnClick = btnItemSearchClick
          TabOrder = 1
        end
        object btnBarcode: TBitBtn
          Left = 120
          Height = 25
          Top = 357
          Width = 100
          Anchors = [akLeft, akBottom]
          Caption = 'Barcode'
          OnClick = btnBarcodeClick
          TabOrder = 2
        end
      end
    end
    object Splitter1: TSplitter
      Left = 600
      Height = 595
      Top = 0
      Width = 5
    end
    object pnlRight: TPanel
      Left = 605
      Height = 595
      Top = 0
      Width = 33
      Align = alClient
      ClientHeight = 595
      ClientWidth = 33
      ParentBackground = False
      TabOrder = 2
      object pnlSale: TPanel
        Left = 1
        Height = 400
        Top = 1
        Width = 31
        Align = alTop
        ClientHeight = 400
        ClientWidth = 31
        ParentBackground = False
        TabOrder = 0
        object lblSaleItems: TLabel
          Left = 8
          Height = 15
          Top = 8
          Width = 58
          Caption = 'Sale Items'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object sgSale: TStringGrid
          Left = 8
          Height = 320
          Top = 32
          Width = 15
          Anchors = [akTop, akLeft, akRight, akBottom]
          FixedCols = 0
          RowCount = 1
          TabOrder = 0
          OnDblClick = sgSaleDblClick
          ColWidths = (
            80
            250
            50
            80
            80
          )
        end
        object btnRemoveItem: TBitBtn
          Left = 8
          Height = 30
          Top = 360
          Width = 100
          Caption = 'Remove Item'
          Enabled = False
          OnClick = btnRemoveItemClick
          TabOrder = 1
        end
        object btnClearSale: TBitBtn
          Left = 120
          Height = 30
          Top = 360
          Width = 100
          Caption = 'Clear Sale'
          OnClick = btnClearSaleClick
          TabOrder = 2
        end
        object btnQuantity: TBitBtn
          Left = 240
          Height = 30
          Top = 360
          Width = 100
          Caption = 'Change Qty'
          OnClick = btnQuantityClick
          TabOrder = 3
        end
      end
      object pnlTotals: TPanel
        Left = 1
        Height = 120
        Top = 521
        Width = 31
        Align = alTop
        ClientHeight = 120
        ClientWidth = 31
        ParentBackground = False
        TabOrder = 1
        object lblSubtotal: TLabel
          Left = 16
          Height = 13
          Top = 16
          Width = 44
          Caption = 'Subtotal:'
        end
        object edtSubtotal: TEdit
          Left = 100
          Height = 21
          Top = 13
          Width = 100
          ReadOnly = True
          TabOrder = 0
          Text = '0.00'
        end
        object lblTax: TLabel
          Left = 220
          Height = 13
          Top = 16
          Width = 22
          Caption = 'Tax:'
        end
        object edtTax: TEdit
          Left = 260
          Height = 21
          Top = 13
          Width = 100
          ReadOnly = True
          TabOrder = 1
          Text = '0.00'
        end
        object lblDiscount: TLabel
          Left = 16
          Height = 13
          Top = 48
          Width = 45
          Caption = 'Discount:'
        end
        object edtDiscount: TEdit
          Left = 100
          Height = 21
          Top = 45
          Width = 100
          TabOrder = 2
          Text = '0.00'
          OnChange = edtDiscountChange
        end
        object lblTotal: TLabel
          Left = 16
          Height = 15
          Top = 80
          Width = 30
          Caption = 'Total:'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object edtTotal: TEdit
          Left = 100
          Height = 23
          Top = 77
          Width = 100
          Font.Style = [fsBold]
          ParentFont = False
          ReadOnly = True
          TabOrder = 3
          Text = '0.00'
        end
      end
      object pnlPayment: TPanel
        Left = 1
        Height = 120
        Top = 401
        Width = 31
        Align = alTop
        ClientHeight = 120
        ClientWidth = 31
        ParentBackground = False
        TabOrder = 2
        object lblPaymentMethod: TLabel
          Left = 16
          Height = 15
          Top = 16
          Width = 99
          Caption = 'Payment Method:'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object rbCash: TRadioButton
          Left = 16
          Height = 17
          Top = 40
          Width = 42
          Caption = 'Cash'
          Checked = True
          TabOrder = 0
          TabStop = True
        end
        object rbCard: TRadioButton
          Left = 80
          Height = 17
          Top = 40
          Width = 41
          Caption = 'Card'
          TabOrder = 1
        end
        object rbQR: TRadioButton
          Left = 140
          Height = 17
          Top = 40
          Width = 33
          Caption = 'QR'
          TabOrder = 2
        end
        object btnProcessSale: TBitBtn
          Left = 300
          Height = 40
          Top = 32
          Width = 120
          Caption = 'Process Sale'
          Enabled = False
          Font.Style = [fsBold]
          Kind = bkOK
          OnClick = btnProcessSaleClick
          ParentFont = False
          TabOrder = 3
        end
      end
      object pnlQuickButtons: TPanel
        Left = 1
        Height = 0
        Top = 594
        Width = 31
        Align = alClient
        ClientHeight = 0
        ClientWidth = 31
        ParentBackground = False
        TabOrder = 3
        object btnCashIn: TBitBtn
          Left = 16
          Height = 30
          Top = 16
          Width = 100
          Caption = 'Cash In'
          OnClick = btnCashInClick
          TabOrder = 0
        end
        object btnCashOut: TBitBtn
          Left = 130
          Height = 30
          Top = 16
          Width = 100
          Caption = 'Cash Out'
          OnClick = btnCashOutClick
          TabOrder = 1
        end
        object btnEndShift: TBitBtn
          Left = 244
          Height = 30
          Top = 16
          Width = 100
          Caption = 'End Shift'
          OnClick = btnEndShiftClick
          TabOrder = 2
        end
        object btnReports: TBitBtn
          Left = 358
          Height = 30
          Top = 16
          Width = 100
          Caption = 'Reports'
          OnClick = btnReportsClick
          TabOrder = 3
        end
      end
    end
  end
  object MainMenu1: TMainMenu
    Left = 40
    Top = 96
    object mnuFile: TMenuItem
      Caption = '&File'
      object mnuNewSale: TMenuItem
        Caption = '&New Sale'
        ShortCut = 16462
      end
      object mnuSeparator1: TMenuItem
        Caption = '-'
      end
      object mnuCashIn: TMenuItem
        Caption = 'Cash &In'
        ShortCut = 16457
      end
      object mnuCashOut: TMenuItem
        Caption = 'Cash &Out'
        ShortCut = 16463
      end
      object mnuSeparator2: TMenuItem
        Caption = '-'
      end
      object mnuEndShift: TMenuItem
        Caption = '&End Shift'
        ShortCut = 16453
      end
      object mnuSeparator3: TMenuItem
        Caption = '-'
      end
      object mnuLogout: TMenuItem
        Caption = '&Logout'
        ShortCut = 16460
      end
      object mnuExit: TMenuItem
        Caption = 'E&xit'
        ShortCut = 32883
        OnClick = actExitExecute
      end
    end
    object mnuReports: TMenuItem
      Caption = '&Reports'
      object mnuDailySales: TMenuItem
        Caption = '&Daily Sales'
        ShortCut = 16452
        OnClick = mnuDailySalesClick
      end
      object mnuItemSales: TMenuItem
        Caption = '&Item Sales'
        ShortCut = 16456
        OnClick = mnuItemSalesClick
      end
      object mnuUserSales: TMenuItem
        Caption = '&User Sales'
        ShortCut = 16469
        OnClick = mnuUserSalesClick
      end
    end
    object mnuTools: TMenuItem
      Caption = '&Tools'
      object mnuSettings: TMenuItem
        Caption = '&Settings'
        ShortCut = 16467
      end
    end
    object mnuHelp: TMenuItem
      Caption = '&Help'
      object mnuAbout: TMenuItem
        Caption = '&About'
      end
    end
  end
  object ActionList1: TActionList
    Left = 120
    Top = 96
    object actNewSale: TAction
      Caption = 'New Sale'
      OnExecute = actNewSaleExecute
      ShortCut = 16462
    end
    object actCashIn: TAction
      Caption = 'Cash In'
      OnExecute = actCashInExecute
      ShortCut = 16457
    end
    object actCashOut: TAction
      Caption = 'Cash Out'
      OnExecute = actCashOutExecute
      ShortCut = 16463
    end
    object actEndShift: TAction
      Caption = 'End Shift'
      OnExecute = actEndShiftExecute
      ShortCut = 16453
    end
    object actReports: TAction
      Caption = 'Reports'
      OnExecute = actReportsExecute
      ShortCut = 16466
    end
    object actSettings: TAction
      Caption = 'Settings'
      OnExecute = actSettingsExecute
      ShortCut = 16467
    end
    object actLogout: TAction
      Caption = 'Logout'
      OnExecute = actLogoutExecute
      ShortCut = 16460
    end
    object actExit: TAction
      Caption = 'Exit'
      OnExecute = actExitExecute
      ShortCut = 32883
    end
  end
end
