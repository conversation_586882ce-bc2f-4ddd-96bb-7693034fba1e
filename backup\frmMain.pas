unit frmMain;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, Buttons, StdCtrls, ExtCtrls,
  Grids, ComCtrls, Menus, ActnList, DataModule, Logging, ConfigUtils;

type
  TSaleItem = record
    ItemID: Integer;
    ItemCode: string;
    ItemName: string;
    Quantity: Integer;
    UnitPrice: Currency;
    LineTotal: Currency;
  end;

  TfrmMain = class(TForm)
    // Actions
    actNewSale: TAction;
    actCashIn: TAction;
    actCashOut: TAction;
    actEndShift: TAction;
    actReports: TAction;
    actSettings: TAction;
    actLogout: TAction;
    actExit: TAction;
    ActionList1: TActionList;
    
    // Main menu
    MainMenu1: TMainMenu;
    mnuFile: TMenuItem;
    mnuNewSale: TMenuItem;
    mnuSeparator1: TMenuItem;
    mnuCashIn: TMenuItem;
    mnuCashOut: TMenuItem;
    mnuSeparator2: TMenuItem;
    mnuEndShift: TMenuItem;
    mnuSeparator3: TMenuItem;
    mnuLogout: TMenuItem;
    mnuExit: TMenuItem;
    mnuReports: TMenuItem;
    mnuDailySales: TMenuItem;
    mnuItemSales: TMenuItem;
    mnuUserSales: TMenuItem;
    mnuTools: TMenuItem;
    mnuSettings: TMenuItem;
    
    // Status bar
    StatusBar1: TStatusBar;
    
    // Main panels
    pnlMain: TPanel;
    pnlLeft: TPanel;
    pnlRight: TPanel;
    Splitter1: TSplitter;
    
    // Categories panel
    pnlCategories: TPanel;
    lblCategories: TLabel;
    lbCategories: TListBox;
    
    // Items panel
    pnlItems: TPanel;
    lblItems: TLabel;
    sgItems: TStringGrid;
    
    // Sale panel
    pnlSale: TPanel;
    lblSaleItems: TLabel;
    sgSale: TStringGrid;
    
    // Totals panel
    pnlTotals: TPanel;
    lblSubtotal: TLabel;
    lblTax: TLabel;
    lblDiscount: TLabel;
    lblTotal: TLabel;
    edtSubtotal: TEdit;
    edtTax: TEdit;
    edtDiscount: TEdit;
    edtTotal: TEdit;
    
    // Payment panel
    pnlPayment: TPanel;
    lblPaymentMethod: TLabel;
    rbCash: TRadioButton;
    rbCard: TRadioButton;
    rbQR: TRadioButton;
    
    // Quick buttons panel
    pnlQuickButtons: TPanel;
    btnProcessSale: TBitBtn;
    btnClearSale: TBitBtn;
    btnRemoveItem: TBitBtn;
    btnCashIn: TBitBtn;
    btnCashOut: TBitBtn;
    btnEndShift: TBitBtn;
    btnReports: TBitBtn;
    btnItemSearch: TBitBtn;
    btnBarcode: TBitBtn;
    btnQuantity: TBitBtn;

    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var CloseAction: TCloseAction);
    procedure FormShow(Sender: TObject);
    procedure lbCategoriesClick(Sender: TObject);
    procedure sgItemsDblClick(Sender: TObject);
    procedure sgSaleDblClick(Sender: TObject);
    procedure btnProcessSaleClick(Sender: TObject);
    procedure btnClearSaleClick(Sender: TObject);
    procedure btnRemoveItemClick(Sender: TObject);
    procedure btnCashInClick(Sender: TObject);
    procedure btnCashOutClick(Sender: TObject);
    procedure btnEndShiftClick(Sender: TObject);
    procedure btnReportsClick(Sender: TObject);
    procedure actNewSaleExecute(Sender: TObject);
    procedure actCashInExecute(Sender: TObject);
    procedure actCashOutExecute(Sender: TObject);
    procedure actEndShiftExecute(Sender: TObject);
    procedure actReportsExecute(Sender: TObject);
    procedure actSettingsExecute(Sender: TObject);
    procedure actLogoutExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure edtDiscountChange(Sender: TObject);
    procedure mnuDailySalesClick(Sender: TObject);
    procedure mnuItemSalesClick(Sender: TObject);
    procedure mnuUserSalesClick(Sender: TObject);
    procedure btnItemSearchClick(Sender: TObject);
    procedure btnBarcodeClick(Sender: TObject);
    procedure btnQuantityClick(Sender: TObject);
  private
    FUserID: Integer;
    FUsername: string;
    FFullName: string;
    FUserRole: string;
    FCurrentShiftID: Integer;
    FShiftType: string;
    FSaleItems: array of TSaleItem;
    FSaleItemCount: Integer;
    FSubtotal: Currency;
    FTaxAmount: Currency;
    FDiscountAmount: Currency;
    FTotal: Currency;
    
    procedure InitializeForm;
    procedure LoadCategories;
    procedure LoadItems(const Category: string = '');
    procedure SetupGrids;
    procedure AddItemToSale(ItemID: Integer; const ItemCode, ItemName: string; UnitPrice: Currency; Quantity: Integer = 1);
    procedure RemoveItemFromSale(Index: Integer);
    procedure UpdateSaleGrid;
    procedure CalculateTotals;
    procedure ClearSale;
    procedure UpdateStatusBar;
    function StartShiftIfNeeded: Boolean;
    function ProcessCashPayment: Boolean;
    function ProcessCardPayment: Boolean;
    function ProcessQRPayment: Boolean;
    procedure PrintSaleReceipt(SaleID: Integer);
    procedure ShowItemSelection;
    function GetCurrentShiftType: string;
    
  public
    procedure SetUserInfo(UserID: Integer; const Username, FullName, UserRole: string);
  end;

var
  formMain: TfrmMain;

implementation

uses
  frmCashInOut, frmEndShift, frmReports, frmPayment,
  frmItemSelection, frmReceiptPreview, frmQuantityInput, frmSales, DateUtils;

{$R *.lfm}

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  try
    InitializeForm;
    
    if Assigned(Logger) then
      Logger.LogInfo('Main form created');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error creating main form: ' + E.Message);
      ShowMessage('Error initializing main form: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.FormShow(Sender: TObject);
begin
  try
    // Start shift if needed
    if not StartShiftIfNeeded then
    begin
      ShowMessage('Unable to start shift. Please contact administrator.');
      Close;
      Exit;
    end;
    
    // Load initial data
    LoadCategories;
    LoadItems;
    UpdateStatusBar;
    
    if Assigned(Logger) then
      Logger.LogInfo('Main form shown for user: ' + FUsername);
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error showing main form: ' + E.Message);
      ShowMessage('Error loading form data: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.FormClose(Sender: TObject; var CloseAction: TCloseAction);
begin
  try
    if FSaleItemCount > 0 then
    begin
      if MessageDlg('There are items in the current sale. Do you want to clear them?',
                    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
        ClearSale
      else
      begin
        CloseAction := caNone;
        Exit;
      end;
    end;
    
    if Assigned(Logger) then
      Logger.LogInfo('Main form closing for user: ' + FUsername);
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error closing main form: ' + E.Message);
    end;
  end;
  Application.Terminate;
end;

procedure TfrmMain.SetUserInfo(UserID: Integer; const Username, FullName, UserRole: string);
begin
  FUserID := UserID;
  FUsername := Username;
  FFullName := FullName;
  FUserRole := UserRole;
  
  Caption := Config.GetValue('Company','Name','') + ' - POS System - ' + FullName + ' (' + UserRole + ')';
  
  // Set permissions based on role
  if FUserRole <> 'ADMIN' then
  begin
    mnuSettings.Visible := False;
    actSettings.Visible := False;
    btnReports.Enabled := FUserRole = 'MANAGER';
    mnuReports.Enabled := FUserRole = 'MANAGER';
    actReports.Enabled := FUserRole = 'MANAGER';
  end;
end;

procedure TfrmMain.InitializeForm;
begin
  // Initialize arrays
  SetLength(FSaleItems, 100); // Initial capacity
  FSaleItemCount := 0;
  
  // Initialize totals
  FSubtotal := 0;
  FTaxAmount := 0;
  FDiscountAmount := 0;
  FTotal := 0;
  
  // Get current shift type
  FShiftType := GetCurrentShiftType;
  
  // Setup grids
  SetupGrids;
  
  // Set default payment method
  rbCash.Checked := True;
  
  // Initialize form state
  btnProcessSale.Enabled := False;
  btnRemoveItem.Enabled := False;
  edtDiscount.Text := '0.00';
  
  // Link actions to menu items and buttons
  mnuNewSale.Action := actNewSale;
  mnuCashIn.Action := actCashIn;
  mnuCashOut.Action := actCashOut;
  mnuEndShift.Action := actEndShift;
  mnuReports.Action := actReports;
  mnuSettings.Action := actSettings;
  mnuLogout.Action := actLogout;
  mnuExit.Action := actExit;
  
  btnCashIn.Action := actCashIn;
  btnCashOut.Action := actCashOut;
  btnEndShift.Action := actEndShift;
  btnReports.Action := actReports;
end;

procedure TfrmMain.SetupGrids;
begin
  // Setup items grid
  sgItems.ColCount := 4;
  sgItems.RowCount := 1;
  sgItems.FixedRows := 1;
  sgItems.Cells[0, 0] := 'Code';
  sgItems.Cells[1, 0] := 'Item Name';
  sgItems.Cells[2, 0] := 'Price';
  sgItems.Cells[3, 0] := 'Stock';
  sgItems.ColWidths[0] := 80;
  sgItems.ColWidths[1] := 200;
  sgItems.ColWidths[2] := 80;
  sgItems.ColWidths[3] := 60;
  
  // Setup sale grid
  sgSale.ColCount := 5;
  sgSale.RowCount := 1;
  sgSale.FixedRows := 1;
  sgSale.Cells[0, 0] := 'Code';
  sgSale.Cells[1, 0] := 'Item';
  sgSale.Cells[2, 0] := 'Qty';
  sgSale.Cells[3, 0] := 'Price';
  sgSale.Cells[4, 0] := 'Total';
  sgSale.ColWidths[0] := 80;
  sgSale.ColWidths[1] := 200;
  sgSale.ColWidths[2] := 50;
  sgSale.ColWidths[3] := 80;
  sgSale.ColWidths[4] := 80;
end;

procedure TfrmMain.LoadCategories;
var
  Categories: TStringList;
  i: Integer;
begin
  try
    // Fix: GetCategoryList returns TStringList, not Boolean
    Categories := dmMain.GetCategoryList;
    if Assigned(Categories) then
    try
      lbCategories.Items.Clear;
      lbCategories.Items.Add('All Items');
      
      for i := 0 to Categories.Count - 1 do
        lbCategories.Items.Add(Categories[i]);
      
      if lbCategories.Items.Count > 0 then
        lbCategories.ItemIndex := 0;
        
    finally
      Categories.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error loading categories: ' + E.Message);
      ShowMessage('Error loading categories: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.LoadItems(const Category: string = '');
var
  Row: Integer;
  CategoryID: Integer;
begin
  try
    // Fix: Convert category name to ID if not empty
    if Category = '' then
      CategoryID := 0  // All items
    else
      CategoryID := dmMain.GetCategoryByName(Category);
    
    if dmMain.GetItems(CategoryID) then
    begin
      sgItems.RowCount := dmMain.qryItems.RecordCount + 1;
      Row := 1;
      
      dmMain.qryItems.First;
      while not dmMain.qryItems.EOF do
      begin
        sgItems.Cells[0, Row] := dmMain.qryItems.FieldByName('item_code').AsString;
        sgItems.Cells[1, Row] := dmMain.qryItems.FieldByName('item_name').AsString;
        sgItems.Cells[2, Row] := FormatFloat('#,##0.00', dmMain.qryItems.FieldByName('unit_price').AsCurrency);
        sgItems.Cells[3, Row] := 'Available'; // You can add stock tracking later
        
        Inc(Row);
        dmMain.qryItems.Next;
      end;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error loading items: ' + E.Message);
      ShowMessage('Error loading items: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.lbCategoriesClick(Sender: TObject);
var
  Category: string;
begin
  if lbCategories.ItemIndex >= 0 then
  begin
    try
      if lbCategories.ItemIndex = 0 then
        Category := ''  // "All Items" selected
      else
        Category := lbCategories.Items[lbCategories.ItemIndex];
      
      if Assigned(Logger) then
        Logger.LogInfo('Category selected: "' + Category + '"');
      
      LoadItems(Category);
      
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error in category selection: ' + E.Message);
        ShowMessage('Error loading category items: ' + E.Message);
      end;
    end;
  end;
end;

procedure TfrmMain.sgItemsDblClick(Sender: TObject);
var
  Row: Integer;
  ItemID: Integer;
  ItemCode, ItemName: string;
  UnitPrice: Currency;
  frmQty: TfrmQuantityInput;
begin
  Row := sgItems.Row;
  if (Row > 0) and (Row < sgItems.RowCount) then
  begin
    try
      ItemCode := sgItems.Cells[0, Row];
      
      if dmMain.GetItemByCode(ItemCode) then
      begin
        ItemID := dmMain.qryItems.FieldByName('id').AsInteger;
        ItemName := dmMain.qryItems.FieldByName('item_name').AsString;
        UnitPrice := dmMain.qryItems.FieldByName('unit_price').AsCurrency;
        
        // Show quantity input dialog
        frmQty := TfrmQuantityInput.Create(Self);
        try
          frmQty.SetItemInfo(ItemName, UnitPrice, 1);
          if frmQty.ShowModal = mrOK then
          begin
            // Add item with specified quantity
            AddItemToSale(ItemID, ItemCode, ItemName, UnitPrice, frmQty.Quantity);
          end;
        finally
          frmQty.Free;
        end;
      end;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error adding item to sale: ' + E.Message);
        ShowMessage('Error adding item: ' + E.Message);
      end;
    end;
  end;
end;

procedure TfrmMain.AddItemToSale(ItemID: Integer; const ItemCode, ItemName: string; UnitPrice: Currency; Quantity: Integer = 1);
var
  i: Integer;
  Found: Boolean;
begin
  Found := False;
  
  // Check if item already exists in sale
  for i := 0 to FSaleItemCount - 1 do
  begin
    if FSaleItems[i].ItemID = ItemID then
    begin
      FSaleItems[i].Quantity := FSaleItems[i].Quantity + Quantity;
      FSaleItems[i].LineTotal := FSaleItems[i].Quantity * FSaleItems[i].UnitPrice;
      Found := True;
      Break;
    end;
  end;
  
  // If item not found, add new item
  if not Found then
  begin
    // Expand array if needed
    if FSaleItemCount >= Length(FSaleItems) then
      SetLength(FSaleItems, Length(FSaleItems) + 50);
    
    FSaleItems[FSaleItemCount].ItemID := ItemID;
    FSaleItems[FSaleItemCount].ItemCode := ItemCode;
    FSaleItems[FSaleItemCount].ItemName := ItemName;
    FSaleItems[FSaleItemCount].Quantity := Quantity;
    FSaleItems[FSaleItemCount].UnitPrice := UnitPrice;
    FSaleItems[FSaleItemCount].LineTotal := Quantity * UnitPrice;
    
    Inc(FSaleItemCount);
  end;
  
  UpdateSaleGrid;
  CalculateTotals;
  
  btnProcessSale.Enabled := FSaleItemCount > 0;
  btnRemoveItem.Enabled := FSaleItemCount > 0;
end;

procedure TfrmMain.RemoveItemFromSale(Index: Integer);
var
  i: Integer;
begin
  if (Index >= 0) and (Index < FSaleItemCount) then
  begin
    // Shift items down
    for i := Index to FSaleItemCount - 2 do
      FSaleItems[i] := FSaleItems[i + 1];
    
    Dec(FSaleItemCount);
    
    UpdateSaleGrid;
    CalculateTotals;
    
    btnProcessSale.Enabled := FSaleItemCount > 0;
    btnRemoveItem.Enabled := FSaleItemCount > 0;
  end;
end;

procedure TfrmMain.UpdateSaleGrid;
var
  i: Integer;
begin
  sgSale.RowCount := FSaleItemCount + 1;
  
  for i := 0 to FSaleItemCount - 1 do
  begin
    sgSale.Cells[0, i + 1] := FSaleItems[i].ItemCode;
    sgSale.Cells[1, i + 1] := FSaleItems[i].ItemName;
    sgSale.Cells[2, i + 1] := IntToStr(FSaleItems[i].Quantity);
    Logger.LogInfo('Quantity: ' + IntToStr(FSaleItems[i].Quantity));
    sgSale.Cells[3, i + 1] := FormatFloat('#,##0.00', FSaleItems[i].UnitPrice);
    Logger.LogInfo('UnitPrice: ' + FormatFloat('#,##0.00', FSaleItems[i].UnitPrice));
    sgSale.Cells[4, i + 1] := FormatFloat('#,##0.00', FSaleItems[i].LineTotal);
    Logger.LogInfo('LineTotal: ' + FormatFloat('#,##0.00', FSaleItems[i].LineTotal));
  end;
end;

procedure TfrmMain.CalculateTotals;
var
  i: Integer;
  TaxRate: Currency;
begin
  FSubtotal := 0;
  
  for i := 0 to FSaleItemCount - 1 do
    FSubtotal := FSubtotal + FSaleItems[i].LineTotal;
  
  // Get tax rate from settings (default 0%)
  TaxRate := Config.GetInt('Tax','Rate',0) / 100.0;
  FTaxAmount := FSubtotal * TaxRate;
  
  // Get discount amount
  FDiscountAmount := StrToCurrDef(edtDiscount.Text, 0);
  
  FTotal := FSubtotal + FTaxAmount - FDiscountAmount;
  if FTotal < 0 then
    FTotal := 0;
  
  // Update display
  edtSubtotal.Text := FormatFloat('#,##0.00', FSubtotal);
  edtTax.Text := FormatFloat('#,##0.00', FTaxAmount);
  edtTotal.Text := FormatFloat('#,##0.00', FTotal);
end;

procedure TfrmMain.ClearSale;
begin
  FSaleItemCount := 0;
  FSubtotal := 0;
  FTaxAmount := 0;
  FDiscountAmount := 0;
  FTotal := 0;
  
  UpdateSaleGrid;
  edtSubtotal.Text := '0.00';
  edtTax.Text := '0.00';
  edtDiscount.Text := '0.00';
  edtTotal.Text := '0.00';
  
  btnProcessSale.Enabled := False;
  btnRemoveItem.Enabled := False;
  
  rbCash.Checked := True;
end;

procedure TfrmMain.sgSaleDblClick(Sender: TObject);
var
  Row: Integer;
begin
  Row := sgSale.Row;
  if (Row > 0) and (Row <= FSaleItemCount) then
  begin
    if MessageDlg('Remove this item from sale?', mtConfirmation, [mbYes, mbNo], 0) = mrYes then
      RemoveItemFromSale(Row - 1);
  end;
end;

procedure TfrmMain.btnRemoveItemClick(Sender: TObject);
var
  Row: Integer;
begin
  Row := sgSale.Row;
  if (Row > 0) and (Row <= FSaleItemCount) then
    RemoveItemFromSale(Row - 1);
end;

procedure TfrmMain.btnClearSaleClick(Sender: TObject);
begin
  if FSaleItemCount > 0 then
  begin
    if MessageDlg('Clear all items from current sale?', mtConfirmation, [mbYes, mbNo], 0) = mrYes then
      ClearSale;
  end;
end;

procedure TfrmMain.btnProcessSaleClick(Sender: TObject);
var
  Success: Boolean;
begin
  if FSaleItemCount = 0 then
  begin
    ShowMessage('No items in sale.');
    Exit;
  end;
  
  Success := False;
  
  try
    if rbCash.Checked then
      Success := ProcessCashPayment
    else if rbCard.Checked then
      Success := ProcessCardPayment
    else if rbQR.Checked then
      Success := ProcessQRPayment;
    
    if Success then
    begin
      ShowMessage('Sale completed successfully!');
      ClearSale;
      UpdateStatusBar;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error processing sale: ' + E.Message);
      ShowMessage('Error processing sale: ' + E.Message);
    end;
  end;
end;

function TfrmMain.ProcessCashPayment: Boolean;
var
  ReceiptNo: string;
  SaleID: Integer;
  i: Integer;
  AmountReceived: Currency;
  ChangeAmount: Currency;
  AmountStr: string;
begin
  Result := False;
  
  try
    // Get receipt number
    ReceiptNo := dmMain.GetNextReceiptNumber;
    
    // Get amount received from user
    AmountStr := FormatFloat('#,##0.00', FTotal);
    if InputQuery('Cash Payment', 'Amount Received:', AmountStr) then
    begin
      AmountReceived := StrToCurrDef(AmountStr, 0);
      
      if AmountReceived < FTotal then
      begin
        ShowMessage('Insufficient amount received.');
        Exit;
      end;
      
      ChangeAmount := AmountReceived - FTotal;
      
      // Create sale
      SaleID := dmMain.CreateSale(ReceiptNo, FUserID, FShiftType, FSubtotal, FDiscountAmount, FTotal);
      if SaleID > 0 then
      begin
        // Add sale items and update stock
        for i := 0 to FSaleItemCount - 1 do
        begin
          if dmMain.AddSaleItem(SaleID, FSaleItems[i].ItemID, FSaleItems[i].Quantity, FSaleItems[i].UnitPrice) then
          begin
            // Update stock (when implemented)
            dmMain.UpdateItemStock(FSaleItems[i].ItemID, FSaleItems[i].Quantity);
          end;
        end;
        
        // Update payment information
        if dmMain.UpdateSalePayment(SaleID, 'CASH', AmountReceived, ChangeAmount, '', '', '', '') then
        begin
          // Add cash transaction
          dmMain.AddCashTransaction(FUserID, 'SALE', FTotal, 'Sale: ' + ReceiptNo, SaleID, FShiftType);
          
          // Show change amount if any
          if ChangeAmount > 0 then
            ShowMessage('Change: ' + FormatFloat('#,##0.00', ChangeAmount));
          
          // Try to print receipt (don't fail the sale if printing fails)
          try
            PrintSaleReceipt(SaleID);
          except
            on E: Exception do
            begin
              if Assigned(Logger) then
                Logger.LogError('Receipt printing failed: ' + E.Message);
              // Don't show error to user - sale was successful
            end;
          end;
          
          Result := True;
          
          if Assigned(Logger) then
            Logger.LogInfo('Cash sale completed: ' + ReceiptNo + ', Amount: ' + CurrToStr(FTotal));
        end;
      end
      else
      begin
        ShowMessage('Failed to create sale record.');
      end;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('ProcessCashPayment error: ' + E.Message);
      ShowMessage('Error processing payment: ' + E.Message);
      raise;
    end;
  end;
end;

function TfrmMain.ProcessCardPayment: Boolean;
var
  frmPayment: TfrmPayment;
  ReceiptNo: string;
  SaleID: Integer;
  i: Integer;
begin
  Result := False;
  
  try
    frmPayment := TfrmPayment.Create(Self);
    try
      frmPayment.SetPaymentInfo('CARD', FTotal);
      
      if frmPayment.ShowModal = mrOK then
      begin
        // Get receipt number
        ReceiptNo := dmMain.GetNextReceiptNumber;
        
        // Create sale
        SaleID := dmMain.CreateSale(ReceiptNo, FUserID, FShiftType, FSubtotal, FDiscountAmount, FTotal);
        if SaleID > 0 then
        begin
          // Add sale items
          for i := 0 to FSaleItemCount - 1 do
          begin
            dmMain.AddSaleItem(SaleID, FSaleItems[i].ItemID, FSaleItems[i].Quantity, FSaleItems[i].UnitPrice);
          end;
          
          // Update payment information
          if dmMain.UpdateSalePayment(SaleID, 'CARD', FTotal, 0, 
                                     frmPayment.CardNumber, frmPayment.AuthCode, '', '') then
          begin
            // Print receipt
            PrintSaleReceipt(SaleID);
            
            Result := True;
            
            if Assigned(Logger) then
              Logger.LogInfo('Card sale completed: ' + ReceiptNo + ', Amount: ' + CurrToStr(FTotal));
          end;
        end;
      end;
      
    finally
      frmPayment.Free;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('ProcessCardPayment error: ' + E.Message);
      raise;
    end;
  end;
end;

function TfrmMain.ProcessQRPayment: Boolean;
var
  frmPayment: TfrmPayment;
  ReceiptNo: string;
  SaleID: Integer;
  i: Integer;
begin
  Result := False;
  
  try
    frmPayment := TfrmPayment.Create(Self);
    try
      frmPayment.SetPaymentInfo('QR', FTotal);
      
      if frmPayment.ShowModal = mrOK then
      begin
        // Get receipt number
        ReceiptNo := dmMain.GetNextReceiptNumber;
        
        // Create sale
        SaleID := dmMain.CreateSale(ReceiptNo, FUserID, FShiftType, FSubtotal, FDiscountAmount, FTotal);
        if SaleID > 0 then
        begin
          // Add sale items
          for i := 0 to FSaleItemCount - 1 do
          begin
            dmMain.AddSaleItem(SaleID, FSaleItems[i].ItemID, FSaleItems[i].Quantity, FSaleItems[i].UnitPrice);
          end;
          
          // Update payment information
          if dmMain.UpdateSalePayment(SaleID, 'QR', FTotal, 0, '', '', 
                                     frmPayment.QRCode, frmPayment.QRReference) then
          begin
            // Print receipt
            PrintSaleReceipt(SaleID);
            
            Result := True;
            
            if Assigned(Logger) then
              Logger.LogInfo('QR sale completed: ' + ReceiptNo + ', Amount: ' + CurrToStr(FTotal));
          end;
        end;
      end;
      
    finally
      frmPayment.Free;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('ProcessQRPayment error: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TfrmMain.PrintSaleReceipt(SaleID: Integer);
var
  frmPreview: TfrmReceiptPreview;
begin
  try
    if SaleID <= 0 then
    begin
      if Assigned(Logger) then
        Logger.LogError('Invalid SaleID for receipt: ' + IntToStr(SaleID));
      ShowMessage('Invalid sale ID for receipt printing.');
      Exit;
    end;
    
    if Assigned(Logger) then
      Logger.LogInfo('Creating receipt preview for SaleID: ' + IntToStr(SaleID));
    
    frmPreview := TfrmReceiptPreview.Create(Self);
    try
      if Assigned(frmPreview) then
      begin
        if Assigned(Logger) then
          Logger.LogInfo('Setting sale info for receipt preview');
          
        frmPreview.SetSaleInfo(SaleID);
        
        if Assigned(Logger) then
          Logger.LogInfo('Showing receipt preview modal');
          
        frmPreview.ShowModal;
      end
      else
      begin
        if Assigned(Logger) then
          Logger.LogError('Failed to create receipt preview form');
        ShowMessage('Error: Could not create receipt preview window.');
      end;
    finally
      if Assigned(frmPreview) then
      begin
        if Assigned(Logger) then
          Logger.LogInfo('Freeing receipt preview form');
        frmPreview.Free;
      end;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in PrintSaleReceipt: ' + E.Message);
      ShowMessage('Error showing receipt preview: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.btnCashInClick(Sender: TObject);
begin
  actCashInExecute(Sender);
end;

procedure TfrmMain.btnCashOutClick(Sender: TObject);
begin
  actCashOutExecute(Sender);
end;

procedure TfrmMain.btnEndShiftClick(Sender: TObject);
begin
  actEndShiftExecute(Sender);
end;

procedure TfrmMain.btnReportsClick(Sender: TObject);
begin
  actReportsExecute(Sender);
end;

procedure TfrmMain.actNewSaleExecute(Sender: TObject);
begin
  ClearSale;
  ActiveControl := sgItems;
end;

procedure TfrmMain.actCashInExecute(Sender: TObject);
var
  frmCashInOut: TfrmCashInOut;
begin
  try
    frmCashInOut := TfrmCashInOut.Create(Self);
    try
      frmCashInOut.SetTransactionInfo('CASH_IN', FUserID, FShiftType);
      if frmCashInOut.ShowModal = mrOK then
        UpdateStatusBar;
    finally
      frmCashInOut.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error opening cash in form: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.actCashOutExecute(Sender: TObject);
var
  frmCashInOut: TfrmCashInOut;
begin
  try
    frmCashInOut := TfrmCashInOut.Create(Self);
    try
      frmCashInOut.SetTransactionInfo('CASH_OUT', FUserID, FShiftType);
      if frmCashInOut.ShowModal = mrOK then
        UpdateStatusBar;
    finally
      frmCashInOut.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error opening cash out form: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.actEndShiftExecute(Sender: TObject);
var
  frmEndShift: TfrmEndShift;
begin
  try
    if FSaleItemCount > 0 then
    begin
      ShowMessage('Please complete or clear the current sale before ending shift.');
      Exit;
    end;
    
    frmEndShift := TfrmEndShift.Create(Self);
    try
      frmEndShift.SetShiftInfo(FCurrentShiftID, FUserID, FShiftType);
      if frmEndShift.ShowModal = mrOK then
      begin
        ShowMessage('Shift ended successfully. Please logout.');
        actLogoutExecute(nil);
      end;
    finally
      frmEndShift.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error ending shift: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.actReportsExecute(Sender: TObject);
var
  frmReports: TfrmReports;
begin
  try
    frmReports := TfrmReports.Create(Self);
    try
      frmReports.ShowModal;
    finally
      frmReports.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error opening reports: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.actSettingsExecute(Sender: TObject);
begin

end;

procedure TfrmMain.actLogoutExecute(Sender: TObject);
begin
  try
    if FSaleItemCount > 0 then
    begin
      if MessageDlg('There are items in the current sale. Clear them and logout?',
                    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
        ClearSale
      else
        Exit;
    end;
    
    if Assigned(Logger) then
      Logger.LogInfo('User logout: ' + FUsername);
    
    ModalResult := mrCancel;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error during logout: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Application.Terminate;
  Close;
end;

// Menu Click Events
procedure TfrmMain.mnuDailySalesClick(Sender: TObject);
var
  frmSalesReports: TfrmSales;
begin
  try
    frmSalesReports := TfrmSales.Create(Self);
    try
      frmSalesReports.SetReportType('DAILY_SALES');
      frmSalesReports.ShowModal;
    finally
      frmSalesReports.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error opening daily sales report: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.mnuItemSalesClick(Sender: TObject);
var
  frmSalesReports: TfrmSales;
begin
  try
    frmSalesReports := TfrmSales.Create(Self);
    try
      frmSalesReports.SetReportType('ITEM_SALES');
      frmSalesReports.ShowModal;
    finally
      frmSalesReports.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error opening item sales report: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.mnuUserSalesClick(Sender: TObject);
var
  frmSalesReports: TfrmSales;
begin
  try
    frmSalesReports := TfrmSales.Create(Self);
    try
      frmSalesReports.SetReportType('USER_SALES');
      frmSalesReports.ShowModal;
    finally
      frmSalesReports.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error opening user sales report: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

// Other Event Handlers
procedure TfrmMain.edtDiscountChange(Sender: TObject);
begin
  CalculateTotals;
end;

procedure TfrmMain.UpdateStatusBar;
var
  CashBalance: Currency;
begin
  try
    CashBalance := dmMain.GetCashBalance(FShiftType, Date);
    
    StatusBar1.Panels[0].Text := 'User: ' + FFullName;
    StatusBar1.Panels[1].Text := 'Shift: ' + FShiftType;
    StatusBar1.Panels[2].Text := 'Cash Balance: ' + FormatFloat('#,##0.00', CashBalance);
    StatusBar1.Panels[3].Text := FormatDateTime('dd/mm/yyyy hh:nn:ss', Now);
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error updating status bar: ' + E.Message);
    end;
  end;
end;

function TfrmMain.StartShiftIfNeeded: Boolean;
var
  OpeningCash: Currency;
  InputStr: string;
begin
  Result := False;
  
  try
    // Check if shift is already open
    FCurrentShiftID := dmMain.GetCurrentShift(FShiftType);
    
    if FCurrentShiftID = 0 then
    begin
      // Need to start new shift
      OpeningCash := 0;
      InputStr := FormatFloat('#,##0.00', OpeningCash);
      if InputQuery('Start Shift', 'Opening Cash Amount:', InputStr) then
      begin
        OpeningCash := StrToCurrDef(InputStr, 0);
        FCurrentShiftID := dmMain.StartShift(FUserID, FShiftType, OpeningCash);
        if FCurrentShiftID > 0 then
        begin
          // Add opening cash transaction
          dmMain.AddCashTransaction(FUserID, 'CASH_IN', OpeningCash, 'Opening Cash', 0, FShiftType);
          Result := True;
          
          if Assigned(Logger) then
            Logger.LogInfo('New shift started: ' + FShiftType + ', Opening: ' + CurrToStr(OpeningCash));
        end
        else
        begin
          ShowMessage('Failed to start shift.');
        end;
      end;
    end
    else
    begin
      Result := True;
      if Assigned(Logger) then
        Logger.LogInfo('Continuing existing shift: ' + IntToStr(FCurrentShiftID));
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('StartShiftIfNeeded error: ' + E.Message);
      ShowMessage('Error starting shift: ' + E.Message);
    end;
  end;
end;

function TfrmMain.GetCurrentShiftType: string;
var
  CurrentHour: Integer;
begin
  CurrentHour := HourOf(Now);
  
  if (CurrentHour >= 6) and (CurrentHour < 18) then
    Result := 'DAY'
  else
    Result := 'NIGHT';
end;

procedure TfrmMain.ShowItemSelection;
var
  frmSelect: TfrmItemSelection;
  ItemID: Integer;
  ItemCode, ItemName: string;
  UnitPrice: Currency;
begin
  try
    frmSelect := TfrmItemSelection.Create(Self);
    try
      if Assigned(frmSelect) then
      begin
        if frmSelect.ShowModal = mrOK then
        begin
          ItemID := frmSelect.SelectedItemID;
          ItemCode := frmSelect.SelectedItemCode;
          ItemName := frmSelect.SelectedItemName;
          UnitPrice := frmSelect.SelectedUnitPrice;
          
          if ItemID > 0 then
            Self.AddItemToSale(ItemID, ItemCode, ItemName, UnitPrice, 1)
          else
          begin
            if Assigned(Logger) then
              Logger.LogError('Invalid item selected from item selection dialog');
          end;
        end;
      end;
    finally
      if Assigned(frmSelect) then
        frmSelect.Free;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in ShowItemSelection: ' + E.Message);
      ShowMessage('Error opening item selection: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.btnItemSearchClick(Sender: TObject);
begin
  try
    ShowItemSelection;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in btnItemSearchClick: ' + E.Message);
      ShowMessage('Error opening item search: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.btnBarcodeClick(Sender: TObject);
var
  Barcode: string;
  ItemID: Integer;
  ItemCode, ItemName: string;
  UnitPrice: Currency;
begin
  Barcode := '';
  if InputQuery('Barcode Scanner', 'Scan or enter barcode:', Barcode) then
  begin
    try
      if dmMain.GetItemByBarcode(Barcode) then
      begin
        ItemID := dmMain.qryItems.FieldByName('id').AsInteger;
        ItemCode := dmMain.qryItems.FieldByName('item_code').AsString;
        ItemName := dmMain.qryItems.FieldByName('item_name').AsString;
        UnitPrice := dmMain.qryItems.FieldByName('unit_price').AsCurrency;
        
        Self.AddItemToSale(ItemID, ItemCode, ItemName, UnitPrice, 1);
      end
      else
        ShowMessage('Item not found with barcode: ' + Barcode);
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error processing barcode: ' + E.Message);
        ShowMessage('Error processing barcode: ' + E.Message);
      end;
    end;
  end;
end;

procedure TfrmMain.btnQuantityClick(Sender: TObject);
var
  Row: Integer;
  frmQty: TfrmQuantityInput;
begin
  Row := Self.sgSale.Row;
  if (Row > 0) and (Row <= Self.FSaleItemCount) then
  begin
    try
      frmQty := TfrmQuantityInput.Create(Self);
      try
        frmQty.SetItemInfo(Self.FSaleItems[Row-1].ItemName,
                           Self.FSaleItems[Row-1].UnitPrice,
                           Self.FSaleItems[Row-1].Quantity);
        if frmQty.ShowModal = mrOK then
        begin
          Self.FSaleItems[Row-1].Quantity := frmQty.Quantity;
          Self.FSaleItems[Row-1].LineTotal := Self.FSaleItems[Row-1].Quantity * Self.FSaleItems[Row-1].UnitPrice;
          Self.UpdateSaleGrid;
          Self.CalculateTotals;
        end;
      finally
        frmQty.Free;
      end;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error updating quantity: ' + E.Message);
        ShowMessage('Error updating quantity: ' + E.Message);
      end;
    end;
  end
  else
    ShowMessage('Please select an item first.');
end;

end.

