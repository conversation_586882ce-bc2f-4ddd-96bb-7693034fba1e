-- Snooker POS Database Creation Script for Firebird 3.0
-- Run this script to create the database structure

-- Create database (run this command separately)
-- CREATE DATABASE 'snpfnb.fdb' USER 'SYSDBA' PASSWORD 'masterkey';

-- Users table
-- Users table (updated with role field)
CREATE TABLE users (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    username VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'CASHIER' CHECK (role IN ('ADMIN', 'MANAGER', 'CASHIER')),
    is_active INTEGER DEFAULT 1,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    CONSTRAINT pk_users PRIMARY KEY (id)
);


-- Categories table (new table with picture field)
CREATE TABLE categories (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    category_name VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    picture BLOB SUB_TYPE BINARY,
    is_active INTEGER DEFAULT 1,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    CONSTRAINT pk_categories PRIMARY KEY (id)
);

-- Items table (category_id as foreign key, added picture field)
CREATE TABLE items (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    item_code VARCHAR(20) NOT NULL UNIQUE,
    item_name VARCHAR(100) NOT NULL,
    category_id INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    picture BLOB SUB_TYPE BINARY,
    BARCODE VARCHAR(50),
    STOCK_QTY INTEGER DEFAULT 0,
    is_active INTEGER DEFAULT 1,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    CONSTRAINT pk_items PRIMARY KEY (id),
    CONSTRAINT fk_items_category FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Sales table
-- Update the sales table to allow NULL payment_method initially
CREATE TABLE sales (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    receipt_no VARCHAR(20) NOT NULL UNIQUE,
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cashier_id INTEGER NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    amount_received DECIMAL(10,2),
    change_amount DECIMAL(10,2),
    payment_method VARCHAR(10) CHECK (payment_method IN ('CASH', 'CARD', 'QR') OR payment_method IS NULL),
    card_number VARCHAR(20),
    auth_code VARCHAR(50),
    qr_code VARCHAR(100),
    qr_reference VARCHAR(50),
    shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN ('DAY', 'NIGHT')),
    CONSTRAINT pk_sales PRIMARY KEY (id),
    CONSTRAINT fk_sales_cashier FOREIGN KEY (cashier_id) REFERENCES users(id)
);


-- Sale items table
CREATE TABLE sale_items (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    sale_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    CONSTRAINT pk_sale_items PRIMARY KEY (id),
    CONSTRAINT fk_sale_items_sale FOREIGN KEY (sale_id) REFERENCES sales(id),
    CONSTRAINT fk_sale_items_item FOREIGN KEY (item_id) REFERENCES items(id)
);

-- Shifts table
CREATE TABLE shifts (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    shift_date DATE NOT NULL,
    shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN ('DAY', 'NIGHT')),
    user_id INTEGER NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    opening_cash DECIMAL(10,2) NOT NULL,
    closing_cash DECIMAL(10,2),
    variance DECIMAL(10,2),
    is_closed CHAR(1) DEFAULT 'N' CHECK (is_closed IN ('Y', 'N')),
    CONSTRAINT pk_shifts PRIMARY KEY (id),
    CONSTRAINT fk_shifts_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT uk_shifts_date_type UNIQUE (shift_date, shift_type)
);

-- Cash transactions table
CREATE TABLE cash_transactions (
    id INTEGER GENERATED BY DEFAULT AS IDENTITY,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER NOT NULL,
    transaction_type VARCHAR(10) NOT NULL CHECK (transaction_type IN ('CASH_IN', 'CASH_OUT', 'SALE')),
    amount DECIMAL(10,2) NOT NULL,
    reason VARCHAR(255),
    sale_id INTEGER,
    shift_type VARCHAR(10) NOT NULL CHECK (shift_type IN ('DAY', 'NIGHT')),
    CONSTRAINT pk_cash_transactions PRIMARY KEY (id),
    CONSTRAINT fk_cash_trans_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_cash_trans_sale FOREIGN KEY (sale_id) REFERENCES sales(id)
);

-- Create indexes for better performance
-- Primary indexes for fast lookups
CREATE INDEX idx_sales_date ON sales(sale_date);
CREATE INDEX idx_sales_cashier ON sales(cashier_id);
CREATE INDEX idx_sales_shift ON sales(shift_type);
CREATE INDEX idx_sale_items_sale ON sale_items(sale_id);
CREATE INDEX idx_sale_items_item ON sale_items(item_id);
CREATE INDEX idx_shifts_date ON shifts(shift_date);
CREATE INDEX idx_cash_trans_date ON cash_transactions(transaction_date);
CREATE INDEX idx_cash_trans_user ON cash_transactions(user_id);
CREATE INDEX idx_cash_trans_shift ON cash_transactions(shift_type);
CREATE INDEX idx_categories_name ON categories(category_name);
CREATE INDEX idx_categories_active ON categories(is_active);
CREATE INDEX idx_items_code ON items(item_code);
CREATE INDEX idx_items_category ON items(category_id);
CREATE INDEX idx_items_active ON items(is_active);

-- Composite indexes for POS performance optimization
CREATE INDEX idx_items_category_active ON items(category_id, is_active);
CREATE INDEX idx_items_active_name ON items(is_active, item_name);
CREATE INDEX idx_sales_date_shift ON sales(sale_date, shift_type);
CREATE INDEX idx_sales_cashier_date ON sales(cashier_id, sale_date);
CREATE INDEX idx_sale_items_sale_qty ON sale_items(sale_id, quantity);
CREATE INDEX idx_categories_active_name ON categories(is_active, category_name);

-- Indexes for reporting queries
CREATE INDEX idx_sales_date_total ON sales(sale_date, total_amount);
CREATE INDEX idx_cash_trans_date_type ON cash_transactions(transaction_date, transaction_type);
CREATE INDEX idx_shifts_date_type ON shifts(shift_date, shift_type);

-- Barcode and search optimization
CREATE INDEX idx_items_barcode ON items(barcode);
CREATE INDEX idx_items_name_upper ON items(UPPER(item_name));

-- User authentication optimization
CREATE INDEX idx_users_username_active ON users(username, is_active);

-- Insert default admin user (password: admin123)
-- Insert default admin user (password: admin123) with role
INSERT INTO users (username, password_hash, full_name, role, is_active)
VALUES ('admin', '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a918', 'System Administrator', 'ADMIN', 1);

-- Insert default categories
INSERT INTO categories (category_name, description, is_active) VALUES
('Pool', 'Pool table services', 1);
INSERT INTO categories (category_name, description, is_active) VALUES
('Snooker', 'Snooker table services', 1);
INSERT INTO categories (category_name, description, is_active) VALUES
('Beverages', 'Drinks and beverages', 1);
INSERT INTO categories (category_name, description, is_active) VALUES
('Snacks', 'Snacks and light food', 1);
INSERT INTO categories (category_name, description, is_active) VALUES
('Other', 'Other services and items', 1);

-- Insert sample items (using category IDs)
INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES
('COKE', 'Coca Cola', 3, 2.50);
INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES
('WATER', 'Mineral Water', 3, 1.50);
INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES
('COFFEE', 'Coffee', 3, 3.00);
INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES
('TEA', 'Tea', 3, 2.00);
INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES    
('CHIPS', 'Potato Chips', 4, 3.50);
INSERT INTO items (item_code, item_name, category_id, unit_price) VALUES
('NUTS', 'Mixed Nuts', 4, 4.00);

-- Create optimized views for POS operations
CREATE VIEW v_active_categories AS
SELECT id, category_name, description, picture
FROM categories
WHERE is_active = 1
ORDER BY category_name;

CREATE VIEW v_active_items AS
SELECT i.id, i.item_code, i.item_name, i.category_id, c.category_name,
       i.unit_price, i.picture, i.barcode, i.stock_qty
FROM items i
INNER JOIN categories c ON i.category_id = c.id
WHERE i.is_active = 1 AND c.is_active = 1
ORDER BY i.item_name;

CREATE VIEW v_items_by_category AS
SELECT i.id, i.item_code, i.item_name, i.category_id, c.category_name,
       i.unit_price, i.picture, i.barcode, i.stock_qty
FROM items i
INNER JOIN categories c ON i.category_id = c.id
WHERE i.is_active = 1 AND c.is_active = 1
ORDER BY c.category_name, i.item_name;

-- Create view for sales summary (for reports)
CREATE VIEW v_sales_summary AS
SELECT s.id, s.receipt_no, s.sale_date, s.cashier_id, u.full_name as cashier_name,
       s.subtotal, s.discount_amount, s.total_amount, s.payment_method,
       s.shift_type, COUNT(si.id) as item_count
FROM sales s
INNER JOIN users u ON s.cashier_id = u.id
LEFT JOIN sale_items si ON s.id = si.sale_id
GROUP BY s.id, s.receipt_no, s.sale_date, s.cashier_id, u.full_name,
         s.subtotal, s.discount_amount, s.total_amount, s.payment_method, s.shift_type;

-- Create stored procedure for fast item search
SET TERM ^ ;

CREATE PROCEDURE sp_search_items(search_term VARCHAR(100))
RETURNS (
    item_id INTEGER,
    item_code VARCHAR(20),
    item_name VARCHAR(100),
    category_name VARCHAR(50),
    unit_price DECIMAL(10,2),
    stock_qty INTEGER
)
AS
BEGIN
    FOR SELECT i.id, i.item_code, i.item_name, c.category_name, i.unit_price, i.stock_qty
        FROM items i
        INNER JOIN categories c ON i.category_id = c.id
        WHERE i.is_active = 1 AND c.is_active = 1
        AND (UPPER(i.item_name) CONTAINING UPPER(:search_term)
             OR UPPER(i.item_code) CONTAINING UPPER(:search_term)
             OR UPPER(c.category_name) CONTAINING UPPER(:search_term)
             OR i.barcode = :search_term)
        ORDER BY i.item_name
        INTO :item_id, :item_code, :item_name, :category_name, :unit_price, :stock_qty
    DO
        SUSPEND;
END^

SET TERM ; ^

COMMIT;