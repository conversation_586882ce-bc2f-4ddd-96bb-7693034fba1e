unit frmCashInOut;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, DataModule, Logging;

type
  TfrmCashInOut = class(TForm)
    btnOK: TBitBtn;
    btnCancel: TBitBtn;
    edtAmount: TEdit;
    edtReason: TEdit;
    lblAmount: TLabel;
    lblReason: TLabel;
    lblTransactionType: TLabel;
    pnlMain: TPanel;
    pnlButtons: TPanel;
    
    procedure FormCreate(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure edtAmountKeyPress(Sender: TObject; var Key: char);
    
  private
    FTransactionType: string;
    FUserID: Integer;
    FShiftType: string;
    
    function ValidateInput: Boolean;
    
  public
    procedure SetTransactionInfo(const TransactionType: string; UserID: Integer; const ShiftType: string);
  end;

var
  CashInOutForm: TfrmCashInOut;

implementation

{$R *.lfm}

procedure TfrmCashInOut.FormCreate(Sender: TObject);
begin
  try
    Position := poOwnerFormCenter;
    BorderStyle := bsDialog;
    BorderIcons := [biSystemMenu];
    
    // Initialize form
    edtAmount.Text := '0.00';
    edtReason.Text := '';
    
    if Assigned(Logger) then
      Logger.LogInfo('Cash In/Out form created');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error creating cash in/out form: ' + E.Message);
      ShowMessage('Error initializing form: ' + E.Message);
    end;
  end;
end;

procedure TfrmCashInOut.SetTransactionInfo(const TransactionType: string; UserID: Integer; const ShiftType: string);
begin
  try
    FTransactionType := TransactionType;
    FUserID := UserID;
    FShiftType := ShiftType;
    
    if TransactionType = 'CASH_IN' then
    begin
      Caption := 'Cash In';
      lblTransactionType.Caption := 'Cash In Transaction';
      lblReason.Caption := 'Reason for Cash In:';
      edtReason.Text := 'Cash In - ';
    end
    else
    begin
      Caption := 'Cash Out';
      lblTransactionType.Caption := 'Cash Out Transaction';
      lblReason.Caption := 'Reason for Cash Out:';
      edtReason.Text := 'Cash Out - ';
    end;
    
    ActiveControl := edtAmount;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('SetTransactionInfo error: ' + E.Message);
      ShowMessage('Error setting transaction info: ' + E.Message);
    end;
  end;
end;

function TfrmCashInOut.ValidateInput: Boolean;
var
  Amount: Currency;
begin
  Result := False;
  
  try
    // Validate amount
    if Trim(edtAmount.Text) = '' then
    begin
      ShowMessage('Please enter an amount.');
      edtAmount.SetFocus;
      Exit;
    end;
    
    try
      Amount := StrToCurr(edtAmount.Text);
    except
      ShowMessage('Please enter a valid amount.');
      edtAmount.SetFocus;
      Exit;
    end;
    
    if Amount <= 0 then
    begin
      ShowMessage('Amount must be greater than zero.');
      edtAmount.SetFocus;
      Exit;
    end;
    
    // Validate reason
    if Trim(edtReason.Text) = '' then
    begin
      ShowMessage('Please enter a reason for this transaction.');
      edtReason.SetFocus;
      Exit;
    end;
    
    if Length(Trim(edtReason.Text)) < 5 then
    begin
      ShowMessage('Please provide a more detailed reason (at least 5 characters).');
      edtReason.SetFocus;
      Exit;
    end;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('ValidateInput error: ' + E.Message);
      ShowMessage('Validation error: ' + E.Message);
    end;
  end;
end;

procedure TfrmCashInOut.btnOKClick(Sender: TObject);
var
  Amount: Currency;
  TransactionID: Integer;
  Reason: string;
begin
  try
    if not ValidateInput then
      Exit;
    
    Amount := StrToCurr(edtAmount.Text);
    Reason := Trim(edtReason.Text);
    
    // Confirm the transaction
    if MessageDlg(
      Format('Confirm %s transaction:%s%sAmount: %s%sReason: %s%s%sProceed?', [
        FTransactionType,
        sLineBreak, sLineBreak,
        FormatFloat('#,##0.00', Amount),
        sLineBreak,
        Reason,
        sLineBreak, sLineBreak
      ]),
      mtConfirmation, [mbYes, mbNo], 0) <> mrYes then
      Exit;
    
    // Add cash transaction to database
    if dmMain.AddCashTransaction(FUserID, FTransactionType, Amount, Reason, 0, FShiftType) then
    begin
      ShowMessage(Format('%s transaction completed successfully!%sAmount: %s', [
        FTransactionType,
        sLineBreak,
        FormatFloat('#,##0.00', Amount)
      ]));
      
      if Assigned(Logger) then
        Logger.LogInfo(Format('%s transaction: User=%d, Amount=%s, Reason=%s', [
          FTransactionType, FUserID, CurrToStr(Amount), Reason
        ]));
      
      ModalResult := mrOK;
    end
    else
    begin
      ShowMessage('Failed to process transaction. Please try again.');
      if Assigned(Logger) then
        Logger.LogError(Format('Failed to add %s transaction: User=%d, Amount=%s', [
          FTransactionType, FUserID, CurrToStr(Amount)
        ]));
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnOKClick error: ' + E.Message);
      ShowMessage('Error processing transaction: ' + E.Message);
    end;
  end;
end;

procedure TfrmCashInOut.btnCancelClick(Sender: TObject);
begin
  try
    if (Trim(edtAmount.Text) <> '0.00') or (Trim(edtReason.Text) <> '') then
    begin
      if MessageDlg('Cancel transaction? Any entered data will be lost.', 
                    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
        ModalResult := mrCancel;
    end
    else
      ModalResult := mrCancel;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnCancelClick error: ' + E.Message);
      ModalResult := mrCancel;
    end;
  end;
end;

procedure TfrmCashInOut.edtAmountKeyPress(Sender: TObject; var Key: char);
begin
  try
    // Allow only numbers, decimal point, and control keys
    if not (Key in ['0'..'9', '.', #8, #13]) then
    begin
      Key := #0;
      Exit;
    end;
    
    // Handle Enter key
    if Key = #13 then
    begin
      Key := #0;
      edtReason.SetFocus;
      Exit;
    end;
    
    // Allow only one decimal point
    if (Key = '.') and (Pos('.', edtAmount.Text) > 0) then
    begin
      Key := #0;
      Exit;
    end;
    
    // Limit decimal places to 2
    if (Key in ['0'..'9']) and (Pos('.', edtAmount.Text) > 0) then
    begin
      if (Length(edtAmount.Text) - Pos('.', edtAmount.Text)) >= 2 then
      begin
        if edtAmount.SelLength = 0 then
          Key := #0;
      end;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('edtAmountKeyPress error: ' + E.Message);
    end;
  end;
end;

end.
