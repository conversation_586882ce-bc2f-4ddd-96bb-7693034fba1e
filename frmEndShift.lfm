object frmEndShift: TfrmEndShift
  Left = 320
  Height = 600
  Top = 200
  Width = 800
  BorderIcons = [biSystemMenu]
  BorderStyle = bsDialog
  Caption = 'End Shift'
  ClientHeight = 600
  ClientWidth = 800
  Position = poOwnerFormCenter
  LCLVersion = '2.2.6.0'
  object pnlMain: TPanel
    Left = 0
    Height = 600
    Top = 0
    Width = 800
    Align = alClient
    ClientHeight = 600
    ClientWidth = 800
    TabOrder = 0
    object pnlHeader: TPanel
      Left = 1
      Height = 80
      Top = 1
      Width = 798
      Align = alTop
      ClientHeight = 80
      ClientWidth = 798
      TabOrder = 0
      object lblTitle: TLabel
        Left = 16
        Height = 20
        Top = 16
        Width = 80
        Caption = 'End Shift'
        Font.Height = -16
        Font.Style = [fsBold]
        ParentFont = False
      end
      object lblShiftInfo: TLabel
        Left = 16
        Height = 15
        Top = 48
        Width = 60
        Caption = 'Shift Info'
      end
    end
    object pnlSummary: TPanel
      Left = 1
      Height = 440
      Top = 81
      Width = 798
      Align = alClient
      ClientHeight = 440
      ClientWidth = 798
      TabOrder = 1
      object lblOpeningCash: TLabel
        Left = 16
        Height = 15
        Top = 16
        Width = 80
        Caption = 'Opening Cash:'
      end
      object edtOpeningCash: TEdit
        Left = 120
        Height = 23
        Top = 13
        Width = 120
        ReadOnly = True
        TabOrder = 0
      end
      object lblCashSales: TLabel
        Left = 16
        Height = 15
        Top = 48
        Width = 65
        Caption = 'Cash Sales:'
      end
      object edtCashSales: TEdit
        Left = 120
        Height = 23
        Top = 45
        Width = 120
        ReadOnly = True
        TabOrder = 1
      end
      object lblCashIn: TLabel
        Left = 16
        Height = 15
        Top = 80
        Width = 45
        Caption = 'Cash In:'
      end
      object edtCashIn: TEdit
        Left = 120
        Height = 23
        Top = 77
        Width = 120
        ReadOnly = True
        TabOrder = 2
      end
      object lblCashOut: TLabel
        Left = 16
        Height = 15
        Top = 112
        Width = 55
        Caption = 'Cash Out:'
      end
      object edtCashOut: TEdit
        Left = 120
        Height = 23
        Top = 109
        Width = 120
        ReadOnly = True
        TabOrder = 3
      end
      object lblExpectedCash: TLabel
        Left = 16
        Height = 15
        Top = 144
        Width = 85
        Caption = 'Expected Cash:'
      end
      object edtExpectedCash: TEdit
        Left = 120
        Height = 23
        Top = 141
        Width = 120
        ReadOnly = True
        TabOrder = 4
      end
      object lblActualCash: TLabel
        Left = 16
        Height = 15
        Top = 176
        Width = 70
        Caption = 'Actual Cash:'
      end
      object edtActualCash: TEdit
        Left = 120
        Height = 23
        Top = 173
        Width = 120
        TabOrder = 5
        OnChange = edtActualCashChange
        OnKeyPress = edtActualCashKeyPress
      end
      object lblClosingCash: TLabel
        Left = 16
        Height = 15
        Top = 208
        Width = 75
        Caption = 'Closing Cash:'
      end
      object edtClosingCash: TEdit
        Left = 120
        Height = 23
        Top = 205
        Width = 120
        TabOrder = 6
        OnChange = edtClosingCashChange
      end
      object lblDifference: TLabel
        Left = 16
        Height = 15
        Top = 240
        Width = 60
        Caption = 'Difference:'
      end
      object edtDifference: TEdit
        Left = 120
        Height = 23
        Top = 237
        Width = 120
        ReadOnly = True
        TabOrder = 7
      end
      object lblNotes: TLabel
        Left = 16
        Height = 15
        Top = 280
        Width = 35
        Caption = 'Notes:'
      end
      object edtNotes: TMemo
        Left = 120
        Height = 80
        Top = 277
        Width = 300
        TabOrder = 8
      end
      object sgSales: TStringGrid
        Left = 450
        Height = 320
        Top = 16
        Width = 330
        ColCount = 4
        FixedCols = 0
        FixedRows = 1
        RowCount = 1
        TabOrder = 9
      end
    end
    object pnlButtons: TPanel
      Left = 1
      Height = 78
      Top = 521
      Width = 798
      Align = alBottom
      ClientHeight = 78
      ClientWidth = 798
      TabOrder = 2
      object btnEndShift: TBitBtn
        Left = 520
        Height = 30
        Top = 24
        Width = 80
        Caption = 'End Shift'
        Default = True
        Kind = bkOK
        ModalResult = 1
        TabOrder = 0
        OnClick = btnEndShiftClick
      end
      object btnCancel: TBitBtn
        Left = 616
        Height = 30
        Top = 24
        Width = 80
        Cancel = True
        Caption = 'Cancel'
        Kind = bkCancel
        ModalResult = 2
        TabOrder = 1
        OnClick = btnCancelClick
      end
      object btnPrintReport: TBitBtn
        Left = 400
        Height = 30
        Top = 24
        Width = 100
        Caption = 'Print Report'
        TabOrder = 2
        OnClick = btnPrintReportClick
      end
    end
  end
end