unit frmEndShift;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, Grids, DataModule, Logging;

type
  TfrmEndShift = class(TForm)
    btnEndShift: TBitBtn;
    btnCancel: TBitBtn;
    btnPrintReport: TBitBtn;
    edtOpeningCash: TEdit;
    edtCashSales: TEdit;
    edtCashIn: TEdit;
    edtCashOut: TEdit;
    edtExpectedCash: TEdit;
    edtActualCash: TEdit;
    edtClosingCash: TEdit;
    edtDifference: TEdit;
    edtNotes: TMemo;
    lblTitle: TLabel;
    lblShiftInfo: TLabel;
    lblOpeningCash: TLabel;
    lblCashSales: TLabel;
    lblCashIn: TLabel;
    lblCashOut: TLabel;
    lblExpectedCash: TLabel;
    lblActualCash: TLabel;
    lblClosingCash: TLabel;
    lblDifference: TLabel;
    lblNotes: TLabel;
    pnlMain: TPanel;
    pnlHeader: TPanel;
    pnlSummary: TPanel;
    pnlButtons: TPanel;
    sgSales: TStringGrid;
    
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure btnEndShiftClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure btnPrintReportClick(Sender: TObject);
    procedure edtActualCashChange(Sender: TObject);
    procedure edtActualCashKeyPress(Sender: TObject; var Key: char);
    procedure edtClosingCashChange(Sender: TObject);
    
  private
    FShiftID: Integer;
    FUserID: Integer;
    FShiftType: string;
    FOpeningCash: Currency;
    FCashSales: Currency;
    FCashIn: Currency;
    FCashOut: Currency;
    FExpectedCash: Currency;
    FActualCash: Currency;
    FClosingCash: Currency;
    FDifference: Currency;
    FVariance: Currency;
    
    procedure LoadShiftData;
    procedure LoadSalesData;
    procedure CalculateExpectedCash;
    procedure CalculateDifference;
    procedure CalculateVariance;
    function ValidateInput: Boolean;
    procedure PrintShiftReport;
    
  public
    procedure SetShiftInfo(ShiftID, UserID: Integer; const ShiftType: string);
  end;

var
  EndShiftForm: TfrmEndShift;

implementation

uses
  PrinterUtils;

{$R *.lfm}

// Utility functions
function PadRight(const Text: string; Width: Integer): string;
begin
  Result := Text + StringOfChar(' ', Width - Length(Text));
  if Length(Result) > Width then
    Result := Copy(Result, 1, Width);
end;

function PadLeft(const Text: string; Width: Integer): string;
begin
  Result := StringOfChar(' ', Width - Length(Text)) + Text;
  if Length(Result) > Width then
    Result := Copy(Result, Length(Result) - Width + 1, Width);
end;

function CenterText(const Text: string; Width: Integer): string;
var
  Spaces: Integer;
begin
  if Length(Text) >= Width then
    Result := Copy(Text, 1, Width)
  else
  begin
    Spaces := (Width - Length(Text)) div 2;
    Result := StringOfChar(' ', Spaces) + Text;
  end;
end;

// Simple config replacement
function GetCompanyName: string;
begin
  Result := 'Your Company Name';
end;

function GetReceiptPrinter: string;
begin
  Result := 'Default';
end;

procedure TfrmEndShift.FormCreate(Sender: TObject);
begin
  try
    Position := poOwnerFormCenter;
    BorderStyle := bsDialog;
    BorderIcons := [biSystemMenu];
    
    // Initialize values
    FShiftID := 0;
    FUserID := 0;
    FShiftType := '';
    FOpeningCash := 0;
    FCashSales := 0;
    FCashIn := 0;
    FCashOut := 0;
    FExpectedCash := 0;
    FActualCash := 0;
    FClosingCash := 0;
    FDifference := 0;
    FVariance := 0;
    
    // Setup sales grid
    sgSales.ColCount := 4;
    sgSales.RowCount := 1;
    sgSales.FixedRows := 1;
    sgSales.Cells[0, 0] := 'Receipt No';
    sgSales.Cells[1, 0] := 'Time';
    sgSales.Cells[2, 0] := 'Amount';
    sgSales.Cells[3, 0] := 'Payment';
    sgSales.ColWidths[0] := 120;
    sgSales.ColWidths[1] := 80;
    sgSales.ColWidths[2] := 80;
    sgSales.ColWidths[3] := 80;
    
    if Assigned(Logger) then
      Logger.LogInfo('End Shift form created');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error creating end shift form: ' + E.Message);
      ShowMessage('Error initializing form: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.FormShow(Sender: TObject);
begin
  try
    LoadShiftData;
    LoadSalesData;
    CalculateExpectedCash;
    
    edtActualCash.SetFocus;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error showing end shift form: ' + E.Message);
      ShowMessage('Error loading shift data: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.SetShiftInfo(ShiftID, UserID: Integer; const ShiftType: string);
begin
  try
    FShiftID := ShiftID;
    FUserID := UserID;
    FShiftType := ShiftType;
    
    Caption := 'End Shift - ' + ShiftType;
    lblTitle.Caption := 'End ' + ShiftType + ' Shift';
    lblShiftInfo.Caption := Format('Shift ID: %d | Date: %s | Type: %s', [
      ShiftID, FormatDateTime('dd/mm/yyyy', Date), ShiftType
    ]);
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('SetShiftInfo error: ' + E.Message);
      ShowMessage('Error setting shift info: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.LoadShiftData;
begin
  try
    // Get shift summary
    if dmMain.GetShiftSummary(FShiftID) then
    begin
      FOpeningCash := dmMain.qryShifts.FieldByName('opening_cash').AsCurrency;
      edtOpeningCash.Text := FormatFloat('#,##0.00', FOpeningCash);
    end;
    
    // Get cash sales for this shift - using existing method
    FCashSales := dmMain.GetCashSalesTotal(Date, FShiftType);
    edtCashSales.Text := FormatFloat('#,##0.00', FCashSales);
    
    // Get cash in transactions - using existing method
    FCashIn := dmMain.GetCashInTotal(Date, FShiftType);
    edtCashIn.Text := FormatFloat('#,##0.00', FCashIn);
    
    // Get cash out transactions - using existing method
    FCashOut := dmMain.GetCashOutTotal(Date, FShiftType);
    edtCashOut.Text := FormatFloat('#,##0.00', FCashOut);
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('LoadShiftData error: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TfrmEndShift.LoadSalesData;
var
  Row: Integer;
begin
  try
    if dmMain.GetSalesData(Date) then
    begin
      sgSales.RowCount := dmMain.qrySales.RecordCount + 1;
      Row := 1;
      
      dmMain.qrySales.First;
      while not dmMain.qrySales.EOF do
      begin
        sgSales.Cells[0, Row] := dmMain.qrySales.FieldByName('receipt_no').AsString;
        sgSales.Cells[1, Row] := FormatDateTime('hh:nn', dmMain.qrySales.FieldByName('sale_date').AsDateTime);
        sgSales.Cells[2, Row] := FormatFloat('#,##0.00', dmMain.qrySales.FieldByName('total_amount').AsCurrency);
        sgSales.Cells[3, Row] := dmMain.qrySales.FieldByName('payment_method').AsString;
        
        Inc(Row);
        dmMain.qrySales.Next;
      end;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('LoadSalesData error: ' + E.Message);
      raise;
    end;
  end;
end;

procedure TfrmEndShift.CalculateExpectedCash;
begin
  try
    FExpectedCash := FOpeningCash + FCashSales + FCashIn - FCashOut;
    edtExpectedCash.Text := FormatFloat('#,##0.00', FExpectedCash);
    
    CalculateDifference;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('CalculateExpectedCash error: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.CalculateDifference;
begin
  try
    FDifference := FActualCash - FExpectedCash;
    edtDifference.Text := FormatFloat('#,##0.00', FDifference);
    
    // Color code the difference
    if FDifference = 0 then
      edtDifference.Color := clWindow
    else if FDifference > 0 then
      edtDifference.Color := clMoneyGreen
    else
      edtDifference.Color := clRed;
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('CalculateDifference error: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.CalculateVariance;
begin
  try
    FVariance := FClosingCash - FExpectedCash;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('CalculateVariance error: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.edtActualCashChange(Sender: TObject);
begin
  try
    if Trim(edtActualCash.Text) <> '' then
    begin
      FActualCash := StrToCurrDef(edtActualCash.Text, 0);
      CalculateDifference;
    end;
  except
    // Ignore conversion errors during typing
  end;
end;

procedure TfrmEndShift.edtActualCashKeyPress(Sender: TObject; var Key: char);
begin
  // Allow only numeric input, decimal point, and control keys
  if not (Key in ['0'..'9', '.', #8, #13]) then
    Key := #0;
end;

procedure TfrmEndShift.edtClosingCashChange(Sender: TObject);
begin
  try
    if Trim(edtClosingCash.Text) <> '' then
    begin
      FClosingCash := StrToCurrDef(edtClosingCash.Text, 0);
      CalculateVariance;
    end;
  except
    // Ignore conversion errors during typing
  end;
end;

function TfrmEndShift.ValidateInput: Boolean;
begin
  Result := False;
  
  try
    // Validate actual cash amount
    if Trim(edtActualCash.Text) = '' then
    begin
      ShowMessage('Please enter the actual cash amount.');
      edtActualCash.SetFocus;
      Exit;
    end;
    
    // Validate closing cash amount
    if Trim(edtClosingCash.Text) = '' then
    begin
      ShowMessage('Please enter the closing cash amount.');
      edtClosingCash.SetFocus;
      Exit;
    end;
    
    try
      FActualCash := StrToCurr(edtActualCash.Text);
      FClosingCash := StrToCurr(edtClosingCash.Text);
    except
      ShowMessage('Invalid cash amount.');
      Exit;
    end;
    
    Result := True;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('ValidateInput error: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.btnEndShiftClick(Sender: TObject);
var
  Notes: string;
begin
  if not ValidateInput then
    Exit;
    
  try
    CalculateVariance;
    
    if Abs(FVariance) > 10 then // Allow variance up to 10 units
    begin
      if MessageDlg(Format('Cash variance is %s. Continue with shift end?', 
                          [FormatFloat('#,##0.00', FVariance)]),
                    mtConfirmation, [mbYes, mbNo], 0) <> mrYes then
        Exit;
    end;
    
    // Prepare notes including variance information
    Notes := edtNotes.Text;
    if FVariance <> 0 then
    begin
      if Notes <> '' then
        Notes := Notes + #13#10;
      Notes := Notes + Format('Cash Variance: %s', [FormatFloat('#,##0.00', FVariance)]);
    end;
    
    // End the shift - passing closing cash and notes
    if dmMain.EndShift(FShiftID, FClosingCash, Notes) then
    begin
      // Print shift report
      PrintShiftReport;
      
      ShowMessage('Shift ended successfully.');
      
      if Assigned(Logger) then
        Logger.LogInfo('Shift ended: ' + IntToStr(FShiftID) + ', Variance: ' + CurrToStr(FVariance));
      
      ModalResult := mrOK;
    end
    else
    begin
      ShowMessage('Failed to end shift.');
      if Assigned(Logger) then
        Logger.LogError('Failed to end shift: ' + IntToStr(FShiftID));
    end;
    
  except
    on E: Exception do
    begin
      ShowMessage('Error ending shift: ' + E.Message);
      if Assigned(Logger) then
        Logger.LogError('End shift error: ' + E.Message);
    end;
  end;
end;

procedure TfrmEndShift.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

procedure TfrmEndShift.btnPrintReportClick(Sender: TObject);
begin
  PrintShiftReport;
end;

procedure TfrmEndShift.PrintShiftReport;
var
  Report: TStringList;
  Row: Integer;
  TotalSales: Currency;
  TransactionCount: Integer;
begin
  try
    Report := TStringList.Create;
    try
      // Build shift report
      Report.Add(StringOfChar('=', 50));
      Report.Add(CenterText(GetCompanyName, 50));
      Report.Add(CenterText('SHIFT REPORT', 50));
      Report.Add(StringOfChar('=', 50));
      Report.Add('Shift: ' + FShiftType);
      Report.Add('Date: ' + FormatDateTime('dd/mm/yyyy', Date));
      
      // Get user name safely
      if dmMain.GetShiftSummary(FShiftID) then
        Report.Add('User: ' + dmMain.qryShifts.FieldByName('user_name').AsString)
      else
        Report.Add('User: Unknown');
        
      if dmMain.qryShifts.Active and not dmMain.qryShifts.IsEmpty then
      begin
        Report.Add('Start Time: ' + FormatDateTime('hh:nn', dmMain.qryShifts.FieldByName('start_time').AsDateTime));
      end;
      Report.Add('End Time: ' + FormatDateTime('hh:nn', Now));
      Report.Add(StringOfChar('-', 50));
      
      // Cash summary
      Report.Add('CASH SUMMARY:');
      Report.Add('Opening Cash: ' + FormatFloat('#,##0.00', FOpeningCash));
      Report.Add('Cash Sales: ' + FormatFloat('#,##0.00', FCashSales));
      Report.Add('Cash In: ' + FormatFloat('#,##0.00', FCashIn));
      Report.Add('Cash Out: ' + FormatFloat('#,##0.00', FCashOut));
      Report.Add('Expected Cash: ' + FormatFloat('#,##0.00', FExpectedCash));
      Report.Add('Actual Cash: ' + FormatFloat('#,##0.00', FActualCash));
      Report.Add('Closing Cash: ' + FormatFloat('#,##0.00', FClosingCash));
      Report.Add('Variance: ' + FormatFloat('#,##0.00', FVariance));
      Report.Add(StringOfChar('-', 50));
      
      // Sales summary
      TotalSales := 0;
      TransactionCount := 0;
      
      // Calculate totals from grid data
      for Row := 1 to sgSales.RowCount - 1 do
      begin
        if sgSales.Cells[0, Row] <> '' then
        begin
          try
            TotalSales := TotalSales + StrToCurrDef(sgSales.Cells[2, Row], 0);
            Inc(TransactionCount);
          except
            // Skip invalid entries
          end;
        end;
      end;
      
      Report.Add('SALES SUMMARY:');
      Report.Add('Total Transactions: ' + IntToStr(TransactionCount));
      Report.Add('Total Sales: ' + FormatFloat('#,##0.00', TotalSales));
      if TransactionCount > 0 then
        Report.Add('Average Sale: ' + FormatFloat('#,##0.00', TotalSales / TransactionCount));
      Report.Add(StringOfChar('-', 50));
      
      // Sales details
      Report.Add('SALES DETAILS:');
      Report.Add(Format('%s %s %s %s', [
        PadRight('Receipt No', 12),
        PadRight('Time', 8),
        PadRight('Amount', 10),
        PadLeft('Payment', 12)
      ]));
      Report.Add(StringOfChar('-', 50));
      
      for Row := 1 to sgSales.RowCount - 1 do
      begin
        if sgSales.Cells[0, Row] <> '' then
        begin
          Report.Add(Format('%s %s %s %s', [
            PadRight(sgSales.Cells[0, Row], 12),
            PadRight(sgSales.Cells[1, Row], 8),
            PadRight(sgSales.Cells[2, Row], 10),
            PadLeft(sgSales.Cells[3, Row], 12)
          ]));
        end;
      end;
      
      Report.Add(StringOfChar('=', 50));
      Report.Add('Report Generated: ' + FormatDateTime('dd/mm/yyyy hh:nn:ss', Now));
      Report.Add(StringOfChar('=', 50));
      
      // Print report
      PrintReceipt(Report, GetReceiptPrinter);
      
    finally
      Report.Free;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error printing shift report: ' + E.Message);
      ShowMessage('Error printing report: ' + E.Message);
    end;
  end;
end;

end.
