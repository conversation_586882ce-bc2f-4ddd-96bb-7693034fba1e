unit frmItemSelection;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, Grids, ComCtrls, DataModule, Logging;

type

  { TfrmItemSelection }

  TfrmItemSelection = class(TForm)
    pnlTop: TPanel;
    lblSearch: TLabel;
    edtSearch: TEdit;
    btnSearch: TBitBtn;
    btnBarcode: TBitBtn;
    
    pnlMain: TPanel;
    sgItems: TStringGrid;
    
    pnlQuantity: TPanel;
    lblQuantity: TLabel;
    edtQuantity: TEdit;
    udQuantity: TUpDown;
    
    pnlButtons: TPanel;
    btnOK: TBitBtn;
    btnCancel: TBitBtn;
    
    procedure edtSearchKeyPress(Sender: TObject; var Key: char);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure edtSearchChange(Sender: TObject);
    procedure btnSearchClick(Sender: TObject);
    procedure btnBarcodeClick(Sender: TObject);
    procedure sgItemsDblClick(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    
  private
    FSelectedItemID: Integer;
    FSelectedItemCode: string;
    FSelectedItemName: string;
    FSelectedUnitPrice: Currency;
    FSelectedQuantity: Integer;
    
    procedure SetupGrid;
    procedure LoadItems(const SearchTerm: string = '');
    procedure SelectCurrentItem;
    
  public
    property SelectedItemID: Integer read FSelectedItemID;
    property SelectedItemCode: string read FSelectedItemCode;
    property SelectedItemName: string read FSelectedItemName;
    property SelectedUnitPrice: Currency read FSelectedUnitPrice;
    property SelectedQuantity: Integer read FSelectedQuantity;
  end;

var
  formItemSelection: TfrmItemSelection;

implementation

{$R *.lfm}

procedure TfrmItemSelection.FormCreate(Sender: TObject);
begin
  FSelectedItemID := 0;
  FSelectedItemCode := '';
  FSelectedItemName := '';
  FSelectedUnitPrice := 0;
  FSelectedQuantity := 1;
  
  SetupGrid;
  
  edtQuantity.Text := '1';
  udQuantity.Position := 1;
  
  btnOK.Enabled := False;
end;

procedure TfrmItemSelection.edtSearchKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // Enter key
  begin
    Key := #0; // Consume the key
    btnSearchClick(nil); // Trigger search
  end;
end;

procedure TfrmItemSelection.FormShow(Sender: TObject);
begin
  try
    LoadItems;
    edtSearch.SetFocus;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error showing item selection form: ' + E.Message);
      ShowMessage('Error loading items: ' + E.Message);
    end;
  end;
end;

procedure TfrmItemSelection.SetupGrid;
begin
  sgItems.ColCount := 5;
  sgItems.RowCount := 1;
  sgItems.FixedRows := 1;
  sgItems.Cells[0, 0] := 'Code';
  sgItems.Cells[1, 0] := 'Item Name';
  sgItems.Cells[2, 0] := 'Category';
  sgItems.Cells[3, 0] := 'Price';
  sgItems.Cells[4, 0] := 'Barcode';
  
  sgItems.ColWidths[0] := 80;   // Code
  sgItems.ColWidths[1] := 200;  // Item Name
  sgItems.ColWidths[2] := 100;  // Category
  sgItems.ColWidths[3] := 80;   // Price
  sgItems.ColWidths[4] := 120;  // Barcode
end;

procedure TfrmItemSelection.LoadItems(const SearchTerm: string = '');
var
  Row: Integer;
begin
  try
    // Clear existing data first
    sgItems.RowCount := 1; // Keep header row
    
    // Ensure query is closed before reuse
    if dmMain.qryItems.Active then
      dmMain.qryItems.Close;
    
    if dmMain.SearchItems(SearchTerm) then
    begin
      if dmMain.qryItems.RecordCount > 0 then
      begin
        sgItems.RowCount := dmMain.qryItems.RecordCount + 1;
        Row := 1;
        
        dmMain.qryItems.First;
        while not dmMain.qryItems.EOF do
        begin
          sgItems.Cells[0, Row] := dmMain.qryItems.FieldByName('item_code').AsString;
          sgItems.Cells[1, Row] := dmMain.qryItems.FieldByName('item_name').AsString;
          sgItems.Cells[2, Row] := dmMain.qryItems.FieldByName('CATEGORY_NAME').AsString;
          sgItems.Cells[3, Row] := FormatFloat('#,##0.00', dmMain.qryItems.FieldByName('unit_price').AsCurrency);
         // sgItems.Cells[4, Row] := dmMain.qryItems.FieldByName('barcode').AsString;
          
          Inc(Row);
          dmMain.qryItems.Next;
        end;
        
        // Auto-select first item if only one result
        if dmMain.qryItems.RecordCount = 1 then
        begin
          sgItems.Row := 1;
          SelectCurrentItem;
        end;
        
        if Assigned(Logger) then
          Logger.LogInfo('LoadItems successful: ' + IntToStr(dmMain.qryItems.RecordCount) + ' items found');
      end
      else
      begin
        sgItems.RowCount := 1;
        if SearchTerm <> '' then
          ShowMessage('No items found matching: ' + SearchTerm)
        else
          ShowMessage('No items found.');
      end;
    end
    else
    begin
      sgItems.RowCount := 1;
      ShowMessage('Error searching items.');
      if Assigned(Logger) then
        Logger.LogError('SearchItems returned false for term: ' + SearchTerm);
    end;
    
    // Reset selection state
    btnOK.Enabled := False;
    
  except
    on E: Exception do
    begin
      sgItems.RowCount := 1;
      if Assigned(Logger) then
        Logger.LogError('Error loading items: ' + E.Message);
      ShowMessage('Error loading items: ' + E.Message);
    end;
  end;
end;

procedure TfrmItemSelection.edtSearchChange(Sender: TObject);
begin
  // Disable auto-search to avoid conflicts - let user use search button or Enter key
  // Auto-search can cause issues with rapid typing
  
  // Optional: Only search if user pauses typing (you'd need a timer for this)
  // For now, just clear the grid if search is empty
  if edtSearch.Text = '' then
    LoadItems; // Load all items when search is cleared
end;

procedure TfrmItemSelection.btnSearchClick(Sender: TObject);
begin
  try
    LoadItems(Trim(edtSearch.Text));
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Search button error: ' + E.Message);
      ShowMessage('Search error: ' + E.Message);
    end;
  end;
end;


procedure TfrmItemSelection.btnBarcodeClick(Sender: TObject);
var
  Barcode: string;
begin
  if InputQuery('Barcode Scanner', 'Scan or enter barcode:', Barcode) then
  begin
    try
      if dmMain.GetItemByBarcode(Barcode) then
      begin
        // Clear grid and show only the found item
        sgItems.RowCount := 2;
        sgItems.Cells[0, 1] := dmMain.qryItems.FieldByName('item_code').AsString;
        sgItems.Cells[1, 1] := dmMain.qryItems.FieldByName('item_name').AsString;
        sgItems.Cells[2, 1] := dmMain.qryItems.FieldByName('category_name').AsString;
        sgItems.Cells[3, 1] := FormatFloat('#,##0.00', dmMain.qryItems.FieldByName('unit_price').AsCurrency);
        sgItems.Cells[4, 1] := dmMain.qryItems.FieldByName('barcode').AsString;
        
        sgItems.Row := 1;
        SelectCurrentItem;
        
        edtSearch.Text := Barcode;
      end
      else
      begin
        ShowMessage('Item not found with barcode: ' + Barcode);
        edtSearch.Text := Barcode;
        LoadItems(Barcode);
      end;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error processing barcode: ' + E.Message);
        ShowMessage('Error processing barcode: ' + E.Message);
      end;
    end;
  end;
end;

procedure TfrmItemSelection.sgItemsDblClick(Sender: TObject);
begin
  if sgItems.Row > 0 then
  begin
    SelectCurrentItem;
    if btnOK.Enabled then
      ModalResult := mrOK;
  end;
end;

procedure TfrmItemSelection.SelectCurrentItem;
var
  Row: Integer;
  ItemCode: string;
begin
  Row := sgItems.Row;
  if (Row > 0) and (Row < sgItems.RowCount) then
  begin
    try
      ItemCode := sgItems.Cells[0, Row];
      
      if dmMain.GetItemByCode(ItemCode) then
      begin
        FSelectedItemID := dmMain.qryItems.FieldByName('id').AsInteger;
        FSelectedItemCode := dmMain.qryItems.FieldByName('item_code').AsString;
        FSelectedItemName := dmMain.qryItems.FieldByName('item_name').AsString;
        FSelectedUnitPrice := dmMain.qryItems.FieldByName('unit_price').AsCurrency;
        FSelectedQuantity := udQuantity.Position;
        
        btnOK.Enabled := True;
        
        // Highlight selected row
        sgItems.Options := sgItems.Options + [goRowSelect];
      end;
    except
      on E: Exception do
      begin
        if Assigned(Logger) then
          Logger.LogError('Error selecting item: ' + E.Message);
        ShowMessage('Error selecting item: ' + E.Message);
      end;
    end;
  end;
end;

procedure TfrmItemSelection.btnOKClick(Sender: TObject);
begin
  if FSelectedItemID > 0 then
  begin
    FSelectedQuantity := udQuantity.Position;
    ModalResult := mrOK;
  end
  else
  begin
    ShowMessage('Please select an item first.');
  end;
end;

procedure TfrmItemSelection.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

end.
