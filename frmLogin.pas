unit frmLogin;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, Buttons, StdCtrls, ExtCtrls,
  DataModule, Logging;

type

  { TfrmLogin }

  TfrmLogin = class(TForm)
    // UI Components
    pnlMain: TPanel;
    pnlHeader: TPanel;
    pnlLogin: TPanel;
    pnlButtons: TPanel;
    
    lblTitle: TLabel;
    lblUsername: TLabel;
    lblPassword: TLabel;
    
    edtUsername: TEdit;
    edtPassword: TEdit;
    
    btnLogin: TBitBtn;
    btnCancel: TBitBtn;
    
    imgLogo: TImage;
    
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure btnLoginClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure edtPasswordKeyPress(Sender: TObject; var Key: char);
    procedure edtUsernameKeyPress(Sender: TObject; var Key: char);
    
  private
    FAttempts: Integer;
    procedure ClearForm;
    function ValidateInput: Boolean;
    procedure ProcessLogin;
    
  public
    UserID: Integer;
    Username: string;
    FullName: string;
    UserRole: string;
  end;

var
  formLogin: TfrmLogin;

implementation


{$R *.lfm}

procedure TfrmLogin.FormCreate(Sender: TObject);
begin
  try
    FAttempts := 0;
    
    // Set form properties
    Position := poScreenCenter;
    BorderStyle := bsDialog;
    BorderIcons := [biSystemMenu];
    btnLogin.Default := False;
    // Initialize form
    ClearForm;
    
    if Assigned(Logger) then
      Logger.LogInfo('Login form created');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error creating login form: ' + E.Message);
      ShowMessage('Error initializing login form: ' + E.Message);
    end;
  end;
end;

procedure TfrmLogin.FormShow(Sender: TObject);
begin
  try
    ClearForm;
    edtUsername.SetFocus;
    
    if Assigned(Logger) then
      Logger.LogInfo('Login form shown');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error showing login form: ' + E.Message);
    end;
  end;
end;

procedure TfrmLogin.ClearForm;
begin
  edtUsername.Text := '';
  edtPassword.Text := '';
  //edtUsername.SetFocus;
end;

function TfrmLogin.ValidateInput: Boolean;
begin
  Result := False;
  
  if Trim(edtUsername.Text) = '' then
  begin
    ShowMessage('Please enter username.');
    edtUsername.SetFocus;
    Exit;
  end;
  
  if Trim(edtPassword.Text) = '' then
  begin
    ShowMessage('Please enter password.');
    edtPassword.SetFocus;
    Exit;
  end;
  
  Result := True;
end;

procedure TfrmLogin.ProcessLogin;
begin
  try
    if not ValidateInput then
      Exit;
    
    Inc(FAttempts);
    
    // Authenticate user
    if dmMain.AuthenticateUser(Trim(edtUsername.Text), Trim(edtPassword.Text)) then
    begin
      // Get user information
      if dmMain.GetUserInfo(Trim(edtUsername.Text)) then
      begin
        UserID := dmMain.qryUsers.FieldByName('id').AsInteger;
        Username := dmMain.qryUsers.FieldByName('username').AsString;
        FullName := dmMain.qryUsers.FieldByName('full_name').AsString;
        UserRole := dmMain.qryUsers.FieldByName('role').AsString;
        
        dmMain.qryUsers.Close;
        
        if Assigned(Logger) then
          Logger.LogInfo('User login successful: ' + Username + ' (' + UserRole + ')');
        
        ModalResult := mrOK;

      end
      else
      begin
        ShowMessage('Error retrieving user information.');
        if Assigned(Logger) then
          Logger.LogError('Error retrieving user info for: ' + Trim(edtUsername.Text));
      end;
    end
    else
    begin
      ShowMessage('Invalid username or password.');
      
      if Assigned(Logger) then
        Logger.LogWarning('Login failed for user: ' + Trim(edtUsername.Text) + ', Attempt: ' + IntToStr(FAttempts));
      
      if FAttempts >= 3 then
      begin
        ShowMessage('Maximum login attempts exceeded. Application will exit.');
        if Assigned(Logger) then
          Logger.LogError('Maximum login attempts exceeded for user: ' + Trim(edtUsername.Text));
        Application.Terminate;
      end
      else
      begin
        ClearForm;
      end;
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('ProcessLogin error: ' + E.Message);
      ShowMessage('Login error: ' + E.Message);
    end;
  end;
end;

procedure TfrmLogin.btnLoginClick(Sender: TObject);
begin
  ProcessLogin;
end;

procedure TfrmLogin.btnCancelClick(Sender: TObject);
begin
  try
    if Assigned(Logger) then
      Logger.LogInfo('User cancelled login');
    
    Application.Terminate;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnCancelClick error: ' + E.Message);
    end;
  end;
end;

procedure TfrmLogin.edtUsernameKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // Enter key
  begin
    Key := #0;
    edtPassword.SetFocus;
  end;
end;

procedure TfrmLogin.edtPasswordKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // Enter key
  begin
    Key := #0;
    ProcessLogin;
  end;
end;

end.
