object frmPOS: TfrmPOS
  Left = 278
  Height = 680
  Top = 16
  Width = 928
  BorderStyle = bsNone
  Caption = 'POS - Point of Sale'
  ClientHeight = 680
  ClientWidth = 928
  Color = clBtnFace
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  KeyPreview = True
  OnCreate = FormCreate
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  LCLVersion = '3.8.0.0'
  WindowState = wsMaximized
  object pnlMain: TPanel
    Left = 0
    Height = 680
    Top = 0
    Width = 928
    Align = alClient
    BevelOuter = bvNone
    ClientHeight = 680
    ClientWidth = 928
    Color = clGray
    ParentBackground = False
    ParentColor = False
    TabOrder = 0
    object pnlHeader: TPanel
      Left = 0
      Height = 60
      Top = 0
      Width = 928
      Align = alTop
      BevelOuter = bvNone
      ClientHeight = 60
      ClientWidth = 928
      Color = 3947580
      ParentBackground = False
      ParentColor = False
      TabOrder = 0
      object lblDateTime: TLabel
        Left = 704
        Height = 23
        Top = 20
        Width = 204
        Alignment = taRightJustify
        Anchors = [akTop, akRight]
        Caption = '2023-01-01 12:00:00'
        Font.Color = clWhite
        Font.Height = -19
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
      end
      object lblUser: TLabel
        Left = 20
        Height = 23
        Top = 10
        Width = 56
        Caption = 'User: '
        Font.Color = clWhite
        Font.Height = -19
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
      end
      object lblShift: TLabel
        Left = 20
        Height = 23
        Top = 35
        Width = 51
        Caption = 'Shift: '
        Font.Color = clWhite
        Font.Height = -19
        Font.Name = 'Tahoma'
        ParentColor = False
        ParentFont = False
      end
    end
    object pnlCategories: TPanel
      Left = 0
      Height = 76
      Top = 60
      Width = 928
      Align = alTop
      Anchors = [akTop]
      BevelOuter = bvNone
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 1
    end
    object pnlItems: TPanel
      Left = 0
      Height = 444
      Top = 136
      Width = 648
      Align = alLeft
      BevelOuter = bvNone
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 2
    end
    object pnlItemDetails: TPanel
      Left = 0
      Height = 56
      Top = 136
      Width = 928
      Anchors = [akLeft, akRight]
      BevelOuter = bvNone
      ClientHeight = 56
      ClientWidth = 928
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 3
      Visible = False
      object imgItem: TImage
        Left = 0
        Height = 0
        Top = 0
        Width = 528
        Anchors = [akTop, akLeft, akRight, akBottom]
        Center = True
        Proportional = True
      end
      object lblItemName: TLabel
        Left = 0
        Height = 1
        Top = -480
        Width = 528
        Alignment = taCenter
        Anchors = [akLeft, akRight, akBottom]
        Font.Color = clWindowText
        Font.Height = -19
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
      end
      object lblItemPrice: TLabel
        Left = 0
        Height = 1
        Top = -450
        Width = 528
        Alignment = taCenter
        Anchors = [akLeft, akRight, akBottom]
        Font.Color = clGreen
        Font.Height = -19
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
      end
    end
    object pnlKeypad: TPanel
      Left = 554
      Height = 444
      Top = 136
      Width = 374
      Align = alRight
      Anchors = [akTop, akRight]
      BevelOuter = bvNone
      ClientHeight = 444
      ClientWidth = 374
      Color = clSilver
      ParentBackground = False
      ParentColor = False
      TabOrder = 4
      object pnlNumpad: TPanel
        Left = 16
        Height = 286
        Top = 0
        Width = 354
        BevelOuter = bvNone
        ClientHeight = 286
        ClientWidth = 354
        Color = clSilver
        ParentBackground = False
        ParentColor = False
        TabOrder = 0
        object btn7: TBCButton
          Left = 10
          Height = 60
          Top = 10
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '7'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn8: TBCButton
          Left = 120
          Height = 60
          Top = 10
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '8'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn9: TBCButton
          Left = 230
          Height = 60
          Top = 10
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '9'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn4: TBCButton
          Left = 10
          Height = 60
          Top = 80
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '4'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn5: TBCButton
          Left = 120
          Height = 60
          Top = 80
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '5'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn6: TBCButton
          Left = 230
          Height = 60
          Top = 80
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '6'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn1: TBCButton
          Left = 10
          Height = 60
          Top = 150
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '1'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn2: TBCButton
          Left = 120
          Height = 60
          Top = 150
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '2'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn3: TBCButton
          Left = 230
          Height = 60
          Top = 150
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '3'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btnDecimal: TBCButton
          Left = 10
          Height = 60
          Top = 220
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '.'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btn0: TBCButton
          Left = 120
          Height = 60
          Top = 220
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '0'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
        object btnBackspace: TBCButton
          Left = 230
          Height = 60
          Top = 220
          Width = 100
          StateClicked.Background.Gradient1.StartColor = 8404992
          StateClicked.Background.Gradient1.EndColor = 4194304
          StateClicked.Background.Gradient1.GradientType = gtRadial
          StateClicked.Background.Gradient1.Point1XPercent = 50
          StateClicked.Background.Gradient1.Point1YPercent = 100
          StateClicked.Background.Gradient1.Point2XPercent = 0
          StateClicked.Background.Gradient1.Point2YPercent = 0
          StateClicked.Background.Gradient2.StartColor = clWhite
          StateClicked.Background.Gradient2.EndColor = clBlack
          StateClicked.Background.Gradient2.GradientType = gtLinear
          StateClicked.Background.Gradient2.Point1XPercent = 0
          StateClicked.Background.Gradient2.Point1YPercent = 0
          StateClicked.Background.Gradient2.Point2XPercent = 0
          StateClicked.Background.Gradient2.Point2YPercent = 100
          StateClicked.Background.Gradient1EndPercent = 100
          StateClicked.Background.Style = bbsGradient
          StateClicked.Border.Style = bboNone
          StateClicked.FontEx.Color = 16770790
          StateClicked.FontEx.FontQuality = fqSystemClearType
          StateClicked.FontEx.Shadow = True
          StateClicked.FontEx.ShadowRadius = 2
          StateClicked.FontEx.ShadowOffsetX = 1
          StateClicked.FontEx.ShadowOffsetY = 1
          StateClicked.FontEx.Style = [fsBold]
          StateHover.Background.Gradient1.StartColor = 16744448
          StateHover.Background.Gradient1.EndColor = 8404992
          StateHover.Background.Gradient1.GradientType = gtRadial
          StateHover.Background.Gradient1.Point1XPercent = 50
          StateHover.Background.Gradient1.Point1YPercent = 100
          StateHover.Background.Gradient1.Point2XPercent = 0
          StateHover.Background.Gradient1.Point2YPercent = 0
          StateHover.Background.Gradient2.StartColor = clWhite
          StateHover.Background.Gradient2.EndColor = clBlack
          StateHover.Background.Gradient2.GradientType = gtLinear
          StateHover.Background.Gradient2.Point1XPercent = 0
          StateHover.Background.Gradient2.Point1YPercent = 0
          StateHover.Background.Gradient2.Point2XPercent = 0
          StateHover.Background.Gradient2.Point2YPercent = 100
          StateHover.Background.Gradient1EndPercent = 100
          StateHover.Background.Style = bbsGradient
          StateHover.Border.Style = bboNone
          StateHover.FontEx.Color = clWhite
          StateHover.FontEx.FontQuality = fqSystemClearType
          StateHover.FontEx.Shadow = True
          StateHover.FontEx.ShadowRadius = 2
          StateHover.FontEx.ShadowOffsetX = 1
          StateHover.FontEx.ShadowOffsetY = 1
          StateHover.FontEx.Style = [fsBold]
          StateNormal.Background.Gradient1.StartColor = 4194304
          StateNormal.Background.Gradient1.EndColor = 8405056
          StateNormal.Background.Gradient1.GradientType = gtLinear
          StateNormal.Background.Gradient1.Point1XPercent = 0
          StateNormal.Background.Gradient1.Point1YPercent = 0
          StateNormal.Background.Gradient1.Point2XPercent = 0
          StateNormal.Background.Gradient1.Point2YPercent = 100
          StateNormal.Background.Gradient2.StartColor = 8405056
          StateNormal.Background.Gradient2.EndColor = 4194304
          StateNormal.Background.Gradient2.GradientType = gtRadial
          StateNormal.Background.Gradient2.Point1XPercent = 50
          StateNormal.Background.Gradient2.Point1YPercent = 100
          StateNormal.Background.Gradient2.Point2XPercent = 0
          StateNormal.Background.Gradient2.Point2YPercent = 0
          StateNormal.Background.Gradient1EndPercent = 60
          StateNormal.Background.Style = bbsGradient
          StateNormal.Border.Style = bboNone
          StateNormal.FontEx.Color = 16770790
          StateNormal.FontEx.FontQuality = fqSystemClearType
          StateNormal.FontEx.Shadow = True
          StateNormal.FontEx.ShadowRadius = 2
          StateNormal.FontEx.ShadowOffsetX = 1
          StateNormal.FontEx.ShadowOffsetY = 1
          StateNormal.FontEx.Style = [fsBold]
          Caption = '⌫'
          Color = clNone
          DropDownWidth = 16
          DropDownArrowSize = 8
          GlobalOpacity = 255
          OnClick = btnNumberClick
          ParentColor = False
          Rounding.RoundX = 12
          Rounding.RoundY = 12
          RoundingDropDown.RoundX = 1
          RoundingDropDown.RoundY = 1
          TextApplyGlobalOpacity = False
          MemoryUsage = bmuHigh
        end
      end
      object btnPay: TBCButton
        Left = 10
        Height = 60
        Top = 312
        Width = 354
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'PAY'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnPayClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuLow
      end
      object btnClear: TBCButton
        Left = 10
        Height = 60
        Top = 376
        Width = 106
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'CLEAR'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnClearClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuHigh
      end
      object btnQuantity: TBCButton
        Left = 128
        Height = 60
        Top = 376
        Width = 110
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'QTY'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnQuantityClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuHigh
      end
      object btnDiscount: TBCButton
        Left = 112
        Height = 60
        Top = 150
        Width = 110
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'DISC'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnDiscountClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuHigh
      end
      object btnVoid: TBCButton
        Left = 250
        Height = 60
        Top = 150
        Width = 110
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'VOID'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnVoidClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuHigh
      end
      object btnPrint: TBCButton
        Left = 248
        Height = 60
        Top = 376
        Width = 112
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'PRINT'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnPrintClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuHigh
      end
      object btnBack: TBCButton
        Left = 190
        Height = 60
        Top = 220
        Width = 170
        StateClicked.Background.Gradient1.StartColor = 8404992
        StateClicked.Background.Gradient1.EndColor = 4194304
        StateClicked.Background.Gradient1.GradientType = gtRadial
        StateClicked.Background.Gradient1.Point1XPercent = 50
        StateClicked.Background.Gradient1.Point1YPercent = 100
        StateClicked.Background.Gradient1.Point2XPercent = 0
        StateClicked.Background.Gradient1.Point2YPercent = 0
        StateClicked.Background.Gradient2.StartColor = clWhite
        StateClicked.Background.Gradient2.EndColor = clBlack
        StateClicked.Background.Gradient2.GradientType = gtLinear
        StateClicked.Background.Gradient2.Point1XPercent = 0
        StateClicked.Background.Gradient2.Point1YPercent = 0
        StateClicked.Background.Gradient2.Point2XPercent = 0
        StateClicked.Background.Gradient2.Point2YPercent = 100
        StateClicked.Background.Gradient1EndPercent = 100
        StateClicked.Background.Style = bbsGradient
        StateClicked.Border.Style = bboNone
        StateClicked.FontEx.Color = 16770790
        StateClicked.FontEx.FontQuality = fqSystemClearType
        StateClicked.FontEx.Shadow = True
        StateClicked.FontEx.ShadowRadius = 2
        StateClicked.FontEx.ShadowOffsetX = 1
        StateClicked.FontEx.ShadowOffsetY = 1
        StateClicked.FontEx.Style = [fsBold]
        StateHover.Background.Gradient1.StartColor = 16744448
        StateHover.Background.Gradient1.EndColor = 8404992
        StateHover.Background.Gradient1.GradientType = gtRadial
        StateHover.Background.Gradient1.Point1XPercent = 50
        StateHover.Background.Gradient1.Point1YPercent = 100
        StateHover.Background.Gradient1.Point2XPercent = 0
        StateHover.Background.Gradient1.Point2YPercent = 0
        StateHover.Background.Gradient2.StartColor = clWhite
        StateHover.Background.Gradient2.EndColor = clBlack
        StateHover.Background.Gradient2.GradientType = gtLinear
        StateHover.Background.Gradient2.Point1XPercent = 0
        StateHover.Background.Gradient2.Point1YPercent = 0
        StateHover.Background.Gradient2.Point2XPercent = 0
        StateHover.Background.Gradient2.Point2YPercent = 100
        StateHover.Background.Gradient1EndPercent = 100
        StateHover.Background.Style = bbsGradient
        StateHover.Border.Style = bboNone
        StateHover.FontEx.Color = clWhite
        StateHover.FontEx.FontQuality = fqSystemClearType
        StateHover.FontEx.Shadow = True
        StateHover.FontEx.ShadowRadius = 2
        StateHover.FontEx.ShadowOffsetX = 1
        StateHover.FontEx.ShadowOffsetY = 1
        StateHover.FontEx.Style = [fsBold]
        StateNormal.Background.Gradient1.StartColor = 4194304
        StateNormal.Background.Gradient1.EndColor = 8405056
        StateNormal.Background.Gradient1.GradientType = gtLinear
        StateNormal.Background.Gradient1.Point1XPercent = 0
        StateNormal.Background.Gradient1.Point1YPercent = 0
        StateNormal.Background.Gradient1.Point2XPercent = 0
        StateNormal.Background.Gradient1.Point2YPercent = 100
        StateNormal.Background.Gradient2.StartColor = 8405056
        StateNormal.Background.Gradient2.EndColor = 4194304
        StateNormal.Background.Gradient2.GradientType = gtRadial
        StateNormal.Background.Gradient2.Point1XPercent = 50
        StateNormal.Background.Gradient2.Point1YPercent = 100
        StateNormal.Background.Gradient2.Point2XPercent = 0
        StateNormal.Background.Gradient2.Point2YPercent = 0
        StateNormal.Background.Gradient1EndPercent = 60
        StateNormal.Background.Style = bbsGradient
        StateNormal.Border.Style = bboNone
        StateNormal.FontEx.Color = 16770790
        StateNormal.FontEx.FontQuality = fqSystemClearType
        StateNormal.FontEx.Shadow = True
        StateNormal.FontEx.ShadowRadius = 2
        StateNormal.FontEx.ShadowOffsetX = 1
        StateNormal.FontEx.ShadowOffsetY = 1
        StateNormal.FontEx.Style = [fsBold]
        Caption = 'BACK'
        Color = clNone
        DropDownWidth = 16
        DropDownArrowSize = 8
        GlobalOpacity = 255
        OnClick = btnBackClick
        ParentColor = False
        Rounding.RoundX = 12
        Rounding.RoundY = 12
        RoundingDropDown.RoundX = 1
        RoundingDropDown.RoundY = 1
        TextApplyGlobalOpacity = False
        MemoryUsage = bmuHigh
      end
    end
    object pnlCart: TPanel
      Left = 0
      Height = 100
      Top = 580
      Width = 928
      Align = alBottom
      BevelOuter = bvNone
      ClientHeight = 100
      ClientWidth = 928
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 5
      object pnlCartHeader: TPanel
        Left = 0
        Height = 48
        Top = 0
        Width = 928
        Align = alTop
        BevelOuter = bvNone
        ClientHeight = 48
        ClientWidth = 928
        Color = 3947580
        ParentBackground = False
        ParentColor = False
        TabOrder = 0
        object lblCartHeader: TLabel
          Left = 10
          Height = 23
          Top = 10
          Width = 40
          Caption = 'Cart'
          Font.Color = clWhite
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
      end
      object pnlCartFooter: TPanel
        Left = 660
        Height = 52
        Top = 48
        Width = 268
        Align = alClient
        BevelOuter = bvNone
        ClientHeight = 52
        ClientWidth = 268
        Color = clWhite
        ParentBackground = False
        ParentColor = False
        TabOrder = 1
        object lblSubtotal: TLabel
          Left = 700
          Height = 23
          Top = 10
          Width = 88
          Alignment = taRightJustify
          Caption = 'Subtotal:'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
        object lblSubtotalValue: TLabel
          Left = 800
          Height = 23
          Top = 10
          Width = 54
          Caption = '$0.00'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
        object lblTax: TLabel
          Left = 710
          Height = 23
          Top = 35
          Width = 96
          Alignment = taRightJustify
          Caption = 'Tax (10%):'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          ParentColor = False
          ParentFont = False
        end
        object lblTaxValue: TLabel
          Left = 800
          Height = 23
          Top = 35
          Width = 46
          Caption = '$0.00'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          ParentColor = False
          ParentFont = False
        end
        object lblTotal: TLabel
          Left = 900
          Height = 23
          Top = 10
          Width = 56
          Alignment = taRightJustify
          Caption = 'Total:'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
        object lblTotalValue: TLabel
          Left = 970
          Height = 23
          Top = 10
          Width = 54
          Caption = '$0.00'
          Font.Color = clGreen
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
      end
      object sgCart: TStringGrid
        Left = 5
        Height = 52
        Top = 48
        Width = 650
        Align = alLeft
        BorderSpacing.Left = 5
        BorderSpacing.Right = 5
        ColCount = 4
        DefaultColWidth = 160
        DefaultRowHeight = 30
        FixedCols = 0
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect]
        RowCount = 1
        TabOrder = 2
        Visible = False
      end
    end
  end
  object tmrClock: TTimer
    OnTimer = tmrClockTimer
    Left = 144
    Top = 24
  end
end
