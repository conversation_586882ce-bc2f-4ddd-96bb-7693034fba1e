unit frmPOS;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, ExtCtrls, StdCtrls, frmQuantityInput, PrintersDlgs,
  Buttons, Grids, Menus, ComCtrls, SQLDB, DB, BCButton, BGRABitmap, BGRABitmapTypes, BGRAImageList, BCTypes, BCPanel, BCLabel, BGRAShape;

type
  TCategoryButton = class(TSpeedButton)
  private
    FCategoryID: Integer;
    FImage: TBGRABitmap;
    procedure SetImage(Value: TBGRABitmap);
  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
    property CategoryID: Integer read FCategoryID write FCategoryID;
    property Image: TBGRABitmap read FImage write SetImage;
  end;

  TItemButton = class(TSpeedButton)
  private
    FItemID: Integer;
    FPrice: Currency;
    FImage: TBGRABitmap;
    procedure SetImage(Value: TBGRABitmap);
  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
    property ItemID: Integer read FItemID write FItemID;
    property Price: Currency read FPrice write FPrice;
    property Image: TBGRABitmap read FImage write SetImage;
  end;

  TSaleItem = record
    ItemID: Integer;
    ItemCode: string;
    ItemName: string;
    Quantity: Integer;
    UnitPrice: Currency;
    LineTotal: Currency;
  end;

  { TfrmPOS }

  TfrmPOS = class(TForm)
    edtQuantity: TEdit;
    edtAmount: TEdit;
    pnlMain: TPanel;
    pnlHeader: TPanel;
    lblDateTime: TLabel;
    lblUser: TLabel;
    lblShift: TLabel;
    pnlCategories: TPanel;
    pnlItems: TPanel;
    pnlCart: TPanel;
    pnlCartHeader: TPanel;
    lblCartHeader: TLabel;
    sgCart: TStringGrid;
    pnlCartFooter: TPanel;
    lblSubtotal: TLabel;
    lblSubtotalValue: TLabel;
    lblTax: TLabel;
    lblTaxValue: TLabel;
    lblTotal: TLabel;
    lblTotalValue: TLabel;
    pnlKeypad: TPanel;
    btnPay: TBitBtn;
    btnClear: TBitBtn;
    btnQuantity: TBitBtn;
    btnDiscount: TBitBtn;
    btnVoid: TBitBtn;
    btnPrint: TBitBtn;
    btnDel: TBitBtn;
    pnlNumpad: TPanel;
    btn1: TBitBtn;
    btn2: TBitBtn;
    btn3: TBitBtn;
    btn4: TBitBtn;
    btn5: TBitBtn;
    btn6: TBitBtn;
    btn7: TBitBtn;
    btn8: TBitBtn;
    btn9: TBitBtn;
    btnDecimal: TBitBtn;
    btn0: TBitBtn;
    btnBackspace: TBitBtn;
    tmrClock: TTimer;
    pnlItemDetails: TPanel;
    imgItem: TImage;
    pnlItemInfo: TPanel;
    lblItemName: TLabel;
    lblItemPrice: TLabel;
    pnlCartContainer: TPanel;
    lblCart: TLabel;
    procedure FormClose(Sender: TObject; var CloseAction: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure pnlItemDetailsClick(Sender: TObject);
    procedure tmrClockTimer(Sender: TObject);
    procedure btnNumberClick(Sender: TObject);
    procedure btnPayClick(Sender: TObject);
    procedure btnClearClick(Sender: TObject);
    procedure btnQuantityClick(Sender: TObject);
    procedure btnDiscountClick(Sender: TObject);
    procedure btnVoidClick(Sender: TObject);
    procedure btnPrintClick(Sender: TObject);
    procedure btnDelClick(Sender: TObject);
    procedure btnBackspaceClick(Sender: TObject);
    procedure FormKeyPress(Sender: TObject; var Key: char);
  private
    FCurrentInput: string; // Stores the current numeric input
    FCurrentItem: TItemButton; // Reference to the currently selected item
    FQuantityEdit: TEdit; // Edit control for quantity input
    FAmountEdit: TEdit; // Edit control for payment amount input
    FSelectedCartRow: Integer; // Tracks the currently selected row in the cart
    FQuantityInput: string; // Stores the current quantity input
  FCurrentUser: string;
  FCurrentUserID: Integer;  // Add this line
  FCurrentShift: string;
  FCurrentShiftType: string;  // Change from FCurrentShift to FCurrentShiftType
  FSubtotal: Currency;
  FTax: Currency;
  FTotal: Currency;
  FCurrentCategoryID: Integer;
  FSelectedItemID: Integer;
  FSaleItems: array of TSaleItem;
  FSaleItemCount: Integer;
  FTaxRate: Double;
  FDiscountAmount: Currency;
  FPaymentMethod: string;
  FAmountTendered: Currency;
  FChangeDue: Currency;
  FCardLastFour: string;
  FCardAuthCode: string;
  FQRReference: string;

    procedure SetupButtonStyles;
    procedure UpdateClock;
    procedure InitializeCartGrid;
    procedure LoadCategories;
    procedure LoadItemsByCategory(CategoryID: Integer);
    procedure InitializeForm;
    procedure CategoryButtonClick(Sender: TObject);
    procedure ItemButtonClick(Sender: TObject);
    function LoadImageFromBlob(BlobField: TBlobField): TBGRABitmap;
    procedure ShowItemDetails(ItemID: Integer; const ItemName: string; Price: Currency; Image: TBGRABitmap);
    function SaveSale: Boolean;
    function ProcessPayment: Boolean;
    procedure ShowReceipt(SaleID: Integer);
    procedure AddItemToSale(ItemID: Integer; const ItemCode, ItemName: string;
        UnitPrice: Currency; Quantity: Integer = 1);
    procedure sgCartSelectCell(Sender: TObject; aCol, aRow: Integer; var CanSelect: Boolean);
  procedure sgCartKeyPress(Sender: TObject; var Key: char);
  procedure ProcessQuantityInput;
  procedure UpdateSaleGrid;
  procedure CalculateTotals;
  procedure ClearSale;
  procedure UpdateTotals;
  procedure UpdateQuantityDisplay(Quantity: Integer); // Updates the quantity display
  procedure ClearInput; // Clears the current input buffer
    function CategoryHasItems(CategoryID: Integer): Boolean; // Checks if a category has any items
    // procedure PrintSaleReceipt(SaleID: Integer);
    // procedure ShowItemSelection;
    // function GetCurrentShiftType: string;

  public
    property CurrentUser: string read FCurrentUser write FCurrentUser;
    property CurrentShift: string read FCurrentShift write FCurrentShift;
  end;

var
  formPOS: TfrmPOS;

implementation

uses
   DataModule, Logging, frmPayment ,frmReceiptPreview;
{$R *.lfm}

{ TCategoryButton }

constructor TCategoryButton.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  FImage := nil;
  Layout := blGlyphTop;
  Height := 120;
  Width := 120;
  Font.Height := -16;
  Font.Style := [fsBold];
  Margin := 8;
  Spacing := 8;
end;

destructor TCategoryButton.Destroy;
begin
  if Assigned(FImage) then
    FImage.Free;
  inherited Destroy;
end;

procedure TCategoryButton.SetImage(Value: TBGRABitmap);
begin
  if FImage <> Value then
  begin
    if Assigned(FImage) then
      FImage.Free;
    FImage := Value;
    if Assigned(FImage) then
    begin
      Glyph.Assign(FImage.Bitmap);
      Glyph.Transparent := True;
    end;
  end;
end;

{ TItemButton }

constructor TItemButton.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  FImage := nil;
  Layout := blGlyphTop;
  Height := 140;
  Width := 140;
  Font.Height := -14;
  Margin := 8;
  Spacing := 8;
  ShowCaption := True;
end;

destructor TItemButton.Destroy;
begin
  if Assigned(FImage) then
    FImage.Free;
  inherited Destroy;
end;

procedure TItemButton.SetImage(Value: TBGRABitmap);
begin
  if FImage <> Value then
  begin
    if Assigned(FImage) then
      FImage.Free;
    FImage := Value;
    if Assigned(FImage) then
    begin
      Glyph.Width := 80;
      Glyph.Height := 80;
      Glyph.Canvas.StretchDraw(Rect(0, 0, 80, 80), FImage.Bitmap);
      Glyph.Transparent := True;
    end;
  end;
end;

{ TfrmPOS }

procedure TfrmPOS.FormCreate(Sender: TObject);
begin
  // Initialize sale-related variables
  FSaleItemCount := 0;
  FTaxRate := 0.15; // 15% tax rate
  FDiscountAmount := 0;
  FPaymentMethod := 'CASH';
  FCurrentShiftType := 'DAY';
  FCurrentUserID := 1; // Set to current user's ID
  
  // Initialize UI
  InitializeForm;
  SetupButtonStyles;
  InitializeCartGrid;
  LoadCategories;
  UpdateClock;

end;

procedure TfrmPOS.FormClose(Sender: TObject; var CloseAction: TCloseAction);
begin
  Application.Terminate;
end;

procedure TfrmPOS.FormShow(Sender: TObject);
begin
  InitializeForm;
   //Load items for default category
  if dmMain.GetCategories then
  begin
    if not dmMain.qryCategories.IsEmpty then
    begin
      FCurrentCategoryID := dmMain.qryCategories.FieldByName('id').AsInteger;
     // LoadItemsByCategory(FCurrentCategoryID);
    end;
  end;

  if (FSaleItemCount = 0) and (FCurrentCategoryID > 0) then
    LoadItemsByCategory(FCurrentCategoryID);
end;

procedure TfrmPOS.pnlItemDetailsClick(Sender: TObject);
begin

end;

procedure TfrmPOS.tmrClockTimer(Sender: TObject);
begin
  UpdateClock;
end;

procedure TfrmPOS.UpdateClock;
begin
  lblDateTime.Caption := FormatDateTime('yyyy-mm-dd hh:nn:ss', Now);
end;

procedure TfrmPOS.InitializeForm;
begin
  // Set user and shift info
  lblUser.Caption := 'User: ' + FCurrentUser;
  lblShift.Caption := 'Shift: ' + FCurrentShift;
  
  // Load initial data
  LoadCategories;
end;

procedure TfrmPOS.InitializeCartGrid;
begin
  if not Assigned(sgCart) then Exit;
  
  with sgCart do
  begin
    Clear;
    ColCount := 4;
    RowCount := 1; // Header row + data rows
    
    // Set column headers
    Cells[0, 0] := 'Item';
    Cells[1, 0] := 'Qty';
    Cells[2, 0] := 'Price';
    Cells[3, 0] := 'Total';
    
    // Set column widths
    ColWidths[0] := 200; // Item name
    ColWidths[1] := 50;  // Quantity
    ColWidths[2] := 80;  // Price
    ColWidths[3] := 80;  // Total
    
    // Make the grid read-only
    Options := Options - [goEditing];
    
    // Enable row selection and set up event handlers
    Options := Options + [goRowSelect, goThumbTracking];
    OnSelectCell := @sgCartSelectCell;
    OnKeyPress := @sgCartKeyPress;
    
    // Initialize selection
    FSelectedCartRow := -1;
    FQuantityInput := '';
  end;
end;

function TfrmPOS.CategoryHasItems(CategoryID: Integer): Boolean;
var
  ItemQuery: TSQLQuery;
begin
  if (CategoryID = 0) then
  begin
    Result := True;
    Exit;
  end;
  Result := False;
  try
    // Get items for this category
    ItemQuery := dmMain.GetPOSItemsByCategory(CategoryID);
    if Assigned(ItemQuery) and ItemQuery.Active then
    begin
      Result := not ItemQuery.IsEmpty;
      ItemQuery.Close;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error checking items for category ' + IntToStr(CategoryID) + ': ' + E.Message);
      // In case of error, assume category has items to be safe
      Result := True;
    end;
  end;
end;

procedure TfrmPOS.LoadCategories;
var
  CategoryQuery: TSQLQuery;
  btn: TCategoryButton;
  Image: TBGRABitmap;
  i, ButtonWidth, ButtonHeight: Integer;
  ScrollBox: TScrollBox;
begin
  // Clear existing controls in the categories panel
  while pnlCategories.ControlCount > 0 do
    pnlCategories.Controls[0].Free;

  try
    // Create a scrollbox to handle many categories
    ScrollBox := TScrollBox.Create(pnlCategories);
    ScrollBox.Parent := pnlCategories;
    ScrollBox.Align := alClient;
    ScrollBox.BorderStyle := bsNone;
    ScrollBox.HorzScrollBar.Visible := True;
    ScrollBox.VertScrollBar.Visible := False;
    
    // Calculate touchscreen-optimized button size
    ButtonWidth := 150;
    ButtonHeight := 120;
    
    // Add 'All' category button first
    btn := TCategoryButton.Create(ScrollBox);
    try
      btn.Parent := ScrollBox;
      btn.CategoryID := 0; // 0 means all categories
      btn.Caption := 'All';
      btn.Width := ButtonWidth;
      btn.Height := ButtonHeight;
      btn.Left := 5;
      btn.Top := 5;
      btn.OnClick := @CategoryButtonClick;
      btn.Font.Style := [fsBold];
      
      // Set default image or leave it blank
      // You can add a default image for 'All' category here if needed
    except
      on E: Exception do
      begin
        btn.Free;
        if Assigned(Logger) then
          Logger.LogError('Error creating All categories button: ' + E.Message);
        raise;
      end;
    end;

    // Get categories from database
    CategoryQuery := dmMain.GetPOSCategories;
    if not Assigned(CategoryQuery) or not CategoryQuery.Active then
      raise Exception.Create('Failed to load categories');

    i := 1; // Start from 1 because we already added the 'All' button
    CategoryQuery.First;
    while not CategoryQuery.EOF do
    begin
      // Check if this category has any items
      if CategoryHasItems(CategoryQuery.FieldByName('id').AsInteger) then
      begin
        btn := TCategoryButton.Create(ScrollBox);
        try
          btn.Parent := ScrollBox;
          btn.CategoryID := CategoryQuery.FieldByName('id').AsInteger;
          btn.Caption := CategoryQuery.FieldByName('category_name').AsString;
          
          // Set touchscreen-optimized button size and position
          btn.Width := ButtonWidth;
          btn.Height := ButtonHeight;
          btn.Left := 8 + ((i-1) mod 4) * (ButtonWidth + 12); // 4 columns with better spacing
          btn.Top := 8 + ((i-1) div 4) * (ButtonHeight + 12);

          // Apply touchscreen-friendly styling
          btn.Font.Name := 'Segoe UI';
          btn.Font.Style := [fsBold];
          btn.Font.Height := -18;
          
          // Load category image if available
          if not CategoryQuery.FieldByName('picture').IsNull then
          begin
            try
              Image := LoadImageFromBlob(TBlobField(CategoryQuery.FieldByName('picture')));
              if Assigned(Image) then
              begin
                btn.Image := Image;
                // Don't free the image here - TCategoryButton will take ownership
              end;
            except
              on E: Exception do
                if Assigned(Logger) then
                  Logger.LogError('Error loading category image: ' + E.Message);
            end;
          end;
          
          btn.OnClick := @CategoryButtonClick;
          
          Inc(i);
        except
          on E: Exception do
          begin
            btn.Free;
            if Assigned(Logger) then
              Logger.LogError('Error creating category button: ' + E.Message);
            // Continue with next category instead of raising the exception
          end;
        end;
      end;
      CategoryQuery.Next;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('Error loading categories: ' + E.Message);
      if Assigned(Logger) then
        Logger.LogError('LoadCategories error: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.LoadItemsByCategory(CategoryID: Integer);
var
  ItemButton: TItemButton;
  ColCount, X, Y: Integer;
  ItemWidth, ItemHeight: Integer;
  ScrollBox: TScrollBox;
  SQL: string;
  ItemsPanel: TPanel;
begin
  // Clear existing controls in the items panel
  while pnlItems.ControlCount > 0 do
    pnlItems.Controls[0].Free;
    
  // Create a scrollbox to handle many items
  ItemsPanel := TPanel.Create(pnlItems);
  ItemsPanel.Parent := pnlItems;
  ItemsPanel.Align := alClient;
  ItemsPanel.BevelOuter := bvNone;
  ItemsPanel.Caption := '';
  
  ScrollBox := TScrollBox.Create(ItemsPanel);
  ScrollBox.Parent := ItemsPanel;
  ScrollBox.Align := alClient;
  ScrollBox.BorderStyle := bsNone;
  ScrollBox.HorzScrollBar.Visible := False;
  ScrollBox.VertScrollBar.Visible := True;
  
  // Set up grid layout
  ColCount := 4; // 4 items per row
  ItemWidth := 100;
  ItemHeight := 100;
  X := 10;
  Y := 10;
  
  try
    // Use DataModule to get items
    if CategoryID = 0 then
    begin
      // Load all items from all categories
      SQL := 'SELECT * FROM items WHERE is_active = 1 ORDER BY item_name';
      if not dmMain.qryItems.Active then
        dmMain.qryItems.Open
      else begin
        dmMain.qryItems.Close;
        dmMain.qryItems.SQL.Text := SQL;
        dmMain.qryItems.Open;
      end;
    end
    else
    begin
      // Load items for specific category
      if not dmMain.GetItems(CategoryID) then
      begin
        ShowMessage('Failed to load items');
        Exit;
      end;
    end;
    
    with dmMain.qryItems do
    begin
      First;
      while not EOF do
      begin
        ItemButton := TItemButton.Create(ScrollBox);
        try
          with ItemButton do
          begin
            Parent := ScrollBox;
            Left := X;
            Top := Y;
            Width := ItemWidth;
            Height := ItemHeight;
            Caption := FieldByName('item_name').AsString;
            ItemID := FieldByName('id').AsInteger;
            Price := FieldByName('unit_price').AsCurrency;
            
            // Set button appearance
            Layout := blGlyphTop;
            Margin := 8;
            Spacing := 8;
            Font.Height := -12;
            //WordWrap := True;
            
            // Load item image if available
            if not FieldByName('picture').IsNull then
            begin
              try
                Image := LoadImageFromBlob(TBlobField(FieldByName('picture')));
                if Assigned(Image) then
                begin
                  ItemButton.Image := Image;
                  // Don't free the image here - TItemButton will take ownership
                end;
              except
                on E: Exception do
                  if Assigned(Logger) then
                    Logger.LogError('Error loading item image: ' + E.Message);
              end;
            end;
            
            OnClick := @ItemButtonClick;
          end;
          
          // Position next button
          X := X + ItemWidth + 10;
          if X + ItemWidth > ScrollBox.ClientWidth then
          begin
            X := 10;
            Y := Y + ItemHeight + 10;
          end;
          
          Next;
        except
          on E: Exception do
          begin
            ItemButton.Free;
            if Assigned(Logger) then
              Logger.LogError('Error creating item button: ' + E.Message);
            Next;
          end;
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('Error loading items: ' + E.Message);
      if Assigned(Logger) then
        Logger.LogError('LoadItemsByCategory error: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.ItemButtonClick(Sender: TObject);
var
  ItemButton: TItemButton;
  ItemID: Integer;
  ItemCode, ItemName: string;
  UnitPrice: Currency;
  ItemFound: Boolean;
begin
  if Sender is TItemButton then
  begin
    ItemButton := TItemButton(Sender);
    ItemID := ItemButton.ItemID;
    
    // Try to get the item details from the database
    ItemFound := False;
    if dmMain.qryItems.Active then
    begin
      // Save the current position
      dmMain.qryItems.DisableControls;
      try
        dmMain.qryItems.First;
        while not dmMain.qryItems.EOF do
        begin
          if dmMain.qryItems.FieldByName('id').AsInteger = ItemID then
          begin
            ItemCode := dmMain.qryItems.FieldByName('item_code').AsString;
            ItemName := dmMain.qryItems.FieldByName('item_name').AsString;
            UnitPrice := dmMain.qryItems.FieldByName('unit_price').AsCurrency;
            ItemFound := True;
            Break;
          end;
          dmMain.qryItems.Next;
        end;
      finally
        dmMain.qryItems.EnableControls;
      end;
    end;
    
    if not ItemFound then
    begin
      // Fallback to button values if not found in dataset
      ItemCode := '';
      ItemName := ItemButton.Caption;
      UnitPrice := ItemButton.Price;
    end;
    
    // Add item to sale with quantity 1 by default
    AddItemToSale(ItemID, ItemCode, ItemName, UnitPrice, 1);
    
    // Update the cart grid
    UpdateSaleGrid;
    
    // Update the totals
    CalculateTotals;
    
    // Show item details in the item details panel
    ShowItemDetails(ItemID, ItemName, UnitPrice, ItemButton.Image);
    
    // Log the item addition for debugging
    //if Assigned(Logger) then
    //begin
    //  Logger.LogInfo(Format('Item added - ID: %d, Name: %s, Price: %.2f, Qty: 1', 
    //    [ItemID, ItemName, UnitPrice]));
    //end;
  end;
end;

procedure TfrmPOS.CategoryButtonClick(Sender: TObject);
var
  i: Integer;
  Btn: TCategoryButton;
begin
  if Sender is TCategoryButton then
  begin
    Btn := TCategoryButton(Sender);
    FCurrentCategoryID := Btn.CategoryID;
    
    // Highlight the selected category button
    for i := 0 to pnlCategories.ControlCount - 1 do
    begin
      if pnlCategories.Controls[i] is TCategoryButton then
      begin
        if pnlCategories.Controls[i] = Btn then
          TCategoryButton(pnlCategories.Controls[i]).Font.Style := [fsBold, fsUnderline]
        else
          TCategoryButton(pnlCategories.Controls[i]).Font.Style := [];
      end;
    end;
    
    // Always show both panels
    pnlCategories.Visible := True;
    pnlItems.Visible := True;

    if FCurrentCategoryID = 0 then // 'All' category selected
    begin
      // Show all items from all categories
      LoadItemsByCategory(0);
    end
    else
    begin
      // Show items from the selected category
      LoadItemsByCategory(FCurrentCategoryID);
    end;
  end;
end;

procedure TfrmPOS.ShowItemDetails(ItemID: Integer; const ItemName: string; Price: Currency; Image: TBGRABitmap);
var
  ScaledImage: TBGRABitmap;
  Ratio: Double;
  NewWidth, NewHeight: Integer;
  DestRect: TRect;
  Bmp: TBitmap;
begin
  try
    // Set item details with larger font and centered text
    lblItemName.Caption := ItemName;
    lblItemPrice.Caption := Format('$%.2f', [Price]);
    
    // Clear previous image
    imgItem.Picture.Bitmap.SetSize(0, 0);
    
    if Assigned(Image) then
    begin
      try
        // Create a new bitmap for the image
        Bmp := TBitmap.Create;
        try
          // Set size to match the image control
          Bmp.Width := imgItem.Width;
          Bmp.Height := imgItem.Height;
          
          // Fill with black background (in case of transparent images)
          Bmp.Canvas.Brush.Color := clBlack;
          Bmp.Canvas.FillRect(0, 0, Bmp.Width, Bmp.Height);
          
          // Calculate aspect ratio preserving dimensions
          if (Image.Width > 0) and (Image.Height > 0) then
          begin
            // Calculate dimensions to fill the control while maintaining aspect ratio
            if (Bmp.Width / Bmp.Height) > (Image.Width / Image.Height) then
            begin
              // Fit to height
              NewWidth := Round(Bmp.Height * (Image.Width / Image.Height));
              NewHeight := Bmp.Height;
              DestRect := Rect((Bmp.Width - NewWidth) div 2, 0, 
                             (Bmp.Width + NewWidth) div 2, NewHeight);
            end
            else
            begin
              // Fit to width
              NewWidth := Bmp.Width;
              NewHeight := Round(Bmp.Width * (Image.Height / Image.Width));
              DestRect := Rect(0, (Bmp.Height - NewHeight) div 2, 
                             NewWidth, (Bmp.Height + NewHeight) div 2);
            end;
            
            // Draw the image centered and scaled
            Bmp.Canvas.StretchDraw(DestRect, Image.Bitmap);
          end;
          
          // Assign the bitmap to the image control
          imgItem.Picture.Bitmap.Assign(Bmp);
        finally
          Bmp.Free;
        end;
      except
        on E: Exception do
          if Assigned(Logger) then
            Logger.LogError('Error showing item image: ' + E.Message);
      end;
    end;
    
    // Ensure the details panel is visible
    pnlItemDetails.Visible := True;
    pnlItems.Visible := True;
    pnlItemDetails.Invalidate;
    Application.ProcessMessages;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in ShowItemDetails: ' + E.Message);
      raise; // Re-raise the exception after logging
    end;
  end;
end;

procedure TfrmPOS.btnDelClick(Sender: TObject);
var
  i: Integer;
begin
  // Check if there's a selected row in the cart
  if (FSelectedCartRow <= 0) or (FSelectedCartRow > FSaleItemCount) then
  begin
    ShowMessage('Please select an item to delete');
    Exit;
  end;

  try
    // Remove the selected item from the array
    for i := FSelectedCartRow - 1 to FSaleItemCount - 2 do
      FSaleItems[i] := FSaleItems[i + 1];
    
    // Decrease the item count
    Dec(FSaleItemCount);
    
    // Clear the last item in the array to prevent memory leaks
    if FSaleItemCount > 0 then
      SetLength(FSaleItems, FSaleItemCount)
    else
      SetLength(FSaleItems, 0);
    
    // Update the cart display and totals
    UpdateSaleGrid;
    CalculateTotals;
    
    // Reset the selected row
    if FSaleItemCount > 0 then
    begin
      if FSelectedCartRow > FSaleItemCount then
        FSelectedCartRow := FSaleItemCount;
      sgCart.Row := FSelectedCartRow;
    end
    else
    begin
      FSelectedCartRow := -1;
      // Clear the item details panel when cart is empty
      lblItemName.Caption := '';
      lblItemPrice.Caption := '';
      imgItem.Picture.Clear;
    end;
    
    // Clear any quantity input
    FQuantityInput := '';
    UpdateQuantityDisplay(1);
    
  except
    on E: Exception do
      ShowMessage('Error deleting item: ' + E.Message);
  end;
end;

procedure TfrmPOS.btnBackspaceClick(Sender: TObject);
var
  LogPrefix: string;
  backspace: char;
begin
  LogPrefix := '[btnBackspaceClick] ';
  backspace := #8;
  
  // If we have a valid cart row selected, handle backspace
  if (FSelectedCartRow > 0) and (FSelectedCartRow <= FSaleItemCount) then
  begin
    // Ensure the grid has focus
    if ActiveControl <> sgCart then
    begin
      //Logger.LogInfo(LogPrefix + 'Setting focus to sgCart');
      sgCart.SetFocus;
    end;
    
    //Logger.LogInfo(LogPrefix + Format('Processing backspace for row %d, Current FQuantityInput: %s', 
    //  [FSelectedCartRow, FQuantityInput]));
    
    // Simulate backspace key press
    sgCartKeyPress(sgCart, backspace);
    
    //Logger.LogInfo(LogPrefix + Format('After backspace - FQuantityInput: %s', [FQuantityInput]));
  end
  else
  begin
    Logger.LogError(LogPrefix + 'No valid row selected for backspace operation');
  end;
end;

procedure TfrmPOS.btnNumberClick(Sender: TObject);
var
  Btn: TBitBtn;
  NumValue: Integer;
  Key: Char;
  LogPrefix: string;
begin
  LogPrefix := Format('[btnNumberClick] ', []);
  
  if not (Sender is TBitBtn) then 
  begin
    Logger.LogError(LogPrefix + 'Sender is not a TBitBtn');
    Exit;
  end;
  
  Btn := TBitBtn(Sender);
  
  // Get the numeric value from the button caption
  if not TryStrToInt(Btn.Caption, NumValue) and (Btn.Caption <> '.') then
  begin
    Logger.LogError(LogPrefix + 'Not a numeric button: ' + Btn.Caption);
    Exit;
  end;
  
  // Log the current state
  //Logger.LogInfo(LogPrefix + Format('Button: %s, FSelectedCartRow: %d, FSaleItemCount: %d',
  //  [Btn.Caption,
  //   FSelectedCartRow, FSaleItemCount]));
  
  // If we have a valid cart row selected, handle quantity editing
  if (FSelectedCartRow > 0) and (FSelectedCartRow <= FSaleItemCount) then
  begin
    // Ensure the grid has focus
    if ActiveControl <> sgCart then
    begin
      //Logger.LogInfo(LogPrefix + 'Setting focus to sgCart');
      sgCart.SetFocus;
    end;
    
    //Logger.LogInfo(LogPrefix + Format('Editing cart row %d, Current FQuantityInput: %s', 
    //  [FSelectedCartRow, FQuantityInput]));
    
    // Simulate key press for the cart grid
    Key := Btn.Caption[1];
    //Logger.LogInfo(LogPrefix + Format('Sending key ''%s'' to sgCartKeyPress', [Key]));
    sgCartKeyPress(sgCart, Key);

    //Logger.LogInfo(LogPrefix + Format('After key press - FQuantityInput: %s', [FQuantityInput]));
  end
end;

procedure TfrmPOS.btnPayClick(Sender: TObject);
begin
  try
    // Check if there are items in the cart
    if FSaleItemCount = 0 then
    begin
      ShowMessage('No items in cart. Please add items before processing payment.');
      Exit;
    end;

    // Calculate totals before payment
    CalculateTotals;

    // Process payment
    if ProcessPayment then
    begin
      // Save the sale
      if SaveSale then
      begin
        ShowMessage('Sale completed successfully!');
        // Clear the cart for next sale
        ClearSale;
        // Optionally show receipt preview
        // ShowReceipt(LastSaleID);
      end
      else
        ShowMessage('Error saving sale. Please try again.');
    end;

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in payment processing: ' + E.Message);
      ShowMessage('Payment processing error: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.btnClearClick(Sender: TObject);
begin
  // Clear current sale with confirmation
  if MessageDlg('Clear Current Sale', 'Are you sure you want to clear the current sale?',
     mtConfirmation, [mbYes, mbNo], 0) = mrYes then
  begin
    ClearSale;
    if Assigned(Logger) then
      Logger.LogInfo('Sale cleared by user');
  end;
end;

procedure TfrmPOS.btnQuantityClick(Sender: TObject);
var
  QuantityForm: TfrmQuantityInput;
  SelectedRow: Integer;
begin
  try
    // Check if an item is selected in the cart
    SelectedRow := sgCart.Row;
    if (SelectedRow < 1) or (SelectedRow >= sgCart.RowCount) then
    begin
      ShowMessage('Please select an item from the cart to change quantity.');
      Exit;
    end;

    // Show quantity input form
    QuantityForm := TfrmQuantityInput.Create(Self);
    try
      QuantityForm.SetItemInfo(
        sgCart.Cells[1, SelectedRow], // Item name
        StrToCurrDef(sgCart.Cells[2, SelectedRow], 0), // Unit price
        StrToIntDef(sgCart.Cells[3, SelectedRow], 1)   // Current quantity
      );

      if QuantityForm.ShowModal = mrOK then
      begin
        // Update quantity in cart
        sgCart.Cells[3, SelectedRow] := IntToStr(QuantityForm.Quantity);
        sgCart.Cells[4, SelectedRow] := FormatFloat('#,##0.00',
          QuantityForm.Quantity * StrToCurrDef(sgCart.Cells[2, SelectedRow], 0));

        // Recalculate totals
        CalculateTotals;

        if Assigned(Logger) then
          Logger.LogInfo('Quantity updated for item: ' + sgCart.Cells[1, SelectedRow]);
      end;
    finally
      QuantityForm.Free;
    end;

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error in quantity adjustment: ' + E.Message);
      ShowMessage('Error adjusting quantity: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.btnDiscountClick(Sender: TObject);
var
  DiscountStr: string;
  DiscountValue: Currency;
  DiscountType: string;
begin
  try
    // Check if there are items in the cart
    if FSaleItemCount = 0 then
    begin
      ShowMessage('No items in cart to apply discount.');
      Exit;
    end;

    // Get discount amount from user
    DiscountStr := InputBox('Apply Discount', 'Enter discount amount or percentage (e.g., 5.00 or 10%):', '0.00');

    if DiscountStr = '' then
      Exit; // User cancelled

    // Check if it's a percentage
    if Pos('%', DiscountStr) > 0 then
    begin
      DiscountType := 'PERCENT';
      DiscountStr := StringReplace(DiscountStr, '%', '', [rfReplaceAll]);
      DiscountValue := StrToCurrDef(DiscountStr, 0);

      if (DiscountValue < 0) or (DiscountValue > 100) then
      begin
        ShowMessage('Percentage discount must be between 0% and 100%.');
        Exit;
      end;

      // Calculate percentage discount
      FDiscountAmount := (FSubtotal * DiscountValue) / 100;
    end
    else
    begin
      DiscountType := 'AMOUNT';
      DiscountValue := StrToCurrDef(DiscountStr, 0);

      if DiscountValue < 0 then
      begin
        ShowMessage('Discount amount cannot be negative.');
        Exit;
      end;

      if DiscountValue > FSubtotal then
      begin
        ShowMessage('Discount amount cannot exceed subtotal.');
        Exit;
      end;

      FDiscountAmount := DiscountValue;
    end;

    // Recalculate totals
    CalculateTotals;

    if Assigned(Logger) then
      Logger.LogInfo('Discount applied: ' + DiscountType + ' = ' + CurrToStr(DiscountValue));

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error applying discount: ' + E.Message);
      ShowMessage('Error applying discount: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.btnVoidClick(Sender: TObject);
var
  SelectedRow: Integer;
  ItemName: string;
  i: Integer;
begin
  try
    // Check if there are items in the cart
    if FSaleItemCount = 0 then
    begin
      ShowMessage('No items in cart to void.');
      Exit;
    end;

    // Get selected row from cart
    SelectedRow := sgCart.Row;

    if (SelectedRow < 1) or (SelectedRow >= sgCart.RowCount) then
    begin
      ShowMessage('Please select an item from the cart to void.');
      Exit;
    end;

    // Get item name for confirmation
    ItemName := sgCart.Cells[1, SelectedRow];

    // Confirm void action
    if MessageDlg('Void Item',
                  'Are you sure you want to void "' + ItemName + '"?',
                  mtConfirmation, [mbYes, mbNo], 0) = mrYes then
    begin
      // Remove item from FSaleItems array
      for i := SelectedRow - 1 to FSaleItemCount - 2 do
      begin
        FSaleItems[i] := FSaleItems[i + 1];
      end;

      // Decrease count
      Dec(FSaleItemCount);

      // Update cart display
      UpdateCartDisplay;

      // Recalculate totals
      CalculateTotals;

      if Assigned(Logger) then
        Logger.LogInfo('Item voided: ' + ItemName);
    end;

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error voiding item: ' + E.Message);
      ShowMessage('Error voiding item: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.btnPrintClick(Sender: TObject);
var
  ReceiptText: TStringList;
  i: Integer;
  PrintDialog: TPrintDialog;
begin
  try
    // Check if there are items to print
    if FSaleItemCount = 0 then
    begin
      ShowMessage('No items to print. Please add items to cart first.');
      Exit;
    end;

    // Create receipt content
    ReceiptText := TStringList.Create;
    try
      // Header
      ReceiptText.Add('========================================');
      ReceiptText.Add('         SNOOKER CENTER POS');
      ReceiptText.Add('========================================');
      ReceiptText.Add('Date: ' + FormatDateTime('dd/mm/yyyy hh:nn', Now));
      ReceiptText.Add('Cashier: ' + FCurrentUser);
      ReceiptText.Add('Receipt #: ' + FormatDateTime('yyyymmddhhnnss', Now));
      ReceiptText.Add('----------------------------------------');

      // Items
      for i := 0 to FSaleItemCount - 1 do
      begin
        ReceiptText.Add(Format('%-20s %3d x %8s = %8s',
          [Copy(FSaleItems[i].ItemName, 1, 20),
           FSaleItems[i].Quantity,
           FormatFloat('#,##0.00', FSaleItems[i].UnitPrice),
           FormatFloat('#,##0.00', FSaleItems[i].LineTotal)]));
      end;

      ReceiptText.Add('----------------------------------------');
      ReceiptText.Add(Format('Subtotal: %25s', [FormatFloat('#,##0.00', FSubtotal)]));

      if FDiscountAmount > 0 then
        ReceiptText.Add(Format('Discount: %25s', [FormatFloat('-#,##0.00', FDiscountAmount)]));

      ReceiptText.Add(Format('TOTAL: %28s', [FormatFloat('#,##0.00', FTotal)]));
      ReceiptText.Add('========================================');
      ReceiptText.Add('       Thank you for your visit!');
      ReceiptText.Add('========================================');

      // Show print preview dialog
      if MessageDlg('Print Receipt',
                    'Print receipt for this transaction?' + #13#10#13#10 +
                    'Preview:' + #13#10 + ReceiptText.Text,
                    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
      begin
        // Here you would normally send to thermal printer
        // For now, we'll show a simple print dialog
        PrintDialog := TPrintDialog.Create(Self);
        try
          if PrintDialog.Execute then
          begin
            // In a real implementation, you would format and send to printer
            ShowMessage('Receipt sent to printer successfully!');

            if Assigned(Logger) then
              Logger.LogInfo('Receipt printed for transaction');
          end;
        finally
          PrintDialog.Free;
        end;
      end;

    finally
      ReceiptText.Free;
    end;

  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error printing receipt: ' + E.Message);
      ShowMessage('Error printing receipt: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.sgCartSelectCell(Sender: TObject; aCol, aRow: Integer;
  var CanSelect: Boolean);
begin
  //Logger.LogInfo(Format('[sgCartSelectCell] Row: %d, FSaleItemCount: %d', [aRow, FSaleItemCount]));
  
  if (aRow > 0) and (aRow <= FSaleItemCount) then
  begin
    FSelectedCartRow := aRow;
    FQuantityInput := ''; // Reset quantity input when selecting a new row
    //Logger.LogInfo(Format('[sgCartSelectCell] Selected row %d, reset FQuantityInput', [aRow]));
    
    // Ensure the grid has focus for keyboard input
    if not sgCart.Focused then
    begin
      //Logger.LogInfo('[sgCartSelectCell] Setting focus to sgCart');
      sgCart.SetFocus;
    end;
  end
  else
  begin
    FSelectedCartRow := -1;
    FQuantityInput := '';
    // Logger.LogInfo('[sgCartSelectCell] Invalid row selection, reset FSelectedCartRow and FQuantityInput');
  end;
end;

procedure TfrmPOS.sgCartKeyPress(Sender: TObject; var Key: char);
var
  PrevInput: string;
begin
  //Logger.LogInfo(Format('[sgCartKeyPress] Key: ''%s'' (ASCII %d), FQuantityInput before: ''%s''', 
  //  [Key, Ord(Key), FQuantityInput]));
    
  if (Key in ['0'..'9']) or (Key = #8) then
  begin
    PrevInput := FQuantityInput;
    
    if Key = #8 then // Backspace
    begin
      if FQuantityInput <> '' then
      begin
        Delete(FQuantityInput, Length(FQuantityInput), 1);
        //Logger.LogInfo(Format('[sgCartKeyPress] Backspace - Input changed from ''%s'' to ''%s''', 
        //  [PrevInput, FQuantityInput]));
      end
      else
      begin
        Logger.LogInfo('[sgCartKeyPress] Backspace pressed but FQuantityInput is empty');
      end;
    end
    else // Numeric key
    begin
      FQuantityInput := FQuantityInput + Key;
      //Logger.LogInfo(Format('[sgCartKeyPress] Added ''%s'' - Input changed from ''%s'' to ''%s''', 
      //  [Key, PrevInput, FQuantityInput]));
    end;
    
    //Logger.LogInfo(Format('[sgCartKeyPress] Calling ProcessQuantityInput with FQuantityInput: ''%s''', [FQuantityInput]));
    ProcessQuantityInput;
    Key := #0; // Consume the key
  end
  else
  begin
    //Logger.LogInfo(Format('[sgCartKeyPress] Ignored key: ''%s'' (ASCII %d)', [Key, Ord(Key)]));
    Key := #0;
  end;
end;

procedure TfrmPOS.ProcessQuantityInput;
var
  NewQuantity: Integer;
  LogPrefix: string;
  PrevQuantity: Integer;
  PrevLineTotal: Currency;
begin
  LogPrefix := Format('[ProcessQuantityInput] Row: %d, Input: %s - ', [FSelectedCartRow, FQuantityInput]);
  
  if (FSelectedCartRow <= 0) or (FSelectedCartRow > FSaleItemCount) then
  begin
    Logger.LogInfo(LogPrefix + 'Invalid row selection');
    Exit;
  end;
  
  if FQuantityInput = '' then
  begin
    Logger.LogInfo(LogPrefix + 'Empty quantity input');
    Exit;
  end;
  
  try
    // Save previous values for logging
    PrevQuantity := FSaleItems[FSelectedCartRow-1].Quantity;
    PrevLineTotal := FSaleItems[FSelectedCartRow-1].LineTotal;
    
    NewQuantity := StrToInt(FQuantityInput);
    
    if NewQuantity <= 0 then
    begin
      Logger.LogInfo(LogPrefix + Format('Invalid quantity: %d (must be > 0)', [NewQuantity]));
      Exit;
    end;
    
    //Logger.LogInfo(LogPrefix + Format('Updating quantity from %d to %d', [PrevQuantity, NewQuantity]));
    
    // Update the sale item
    FSaleItems[FSelectedCartRow-1].Quantity := NewQuantity;
    FSaleItems[FSelectedCartRow-1].LineTotal := FSaleItems[FSelectedCartRow-1].UnitPrice * NewQuantity;
    
    // Update the grid
    sgCart.Cells[1, FSelectedCartRow] := IntToStr(NewQuantity);
    sgCart.Cells[3, FSelectedCartRow] := Format('%.2f', [FSaleItems[FSelectedCartRow-1].LineTotal]);
    
    //Logger.LogInfo(LogPrefix + Format('Line total updated from %.2f to %.2f', 
    //  [PrevLineTotal, FSaleItems[FSelectedCartRow-1].LineTotal]));
    
    // Recalculate totals
    CalculateTotals;
    
  except
    on E: Exception do
    begin
      Logger.LogError(LogPrefix + 'Error processing quantity: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.FormKeyPress(Sender: TObject; var Key: char);
begin
  // If the cart has focus and a row is selected, handle key presses for quantity input
  if (ActiveControl = sgCart) and (FSelectedCartRow > 0) and (FSelectedCartRow <= FSaleItemCount) then
  begin
    if Key in ['0'..'9', #8] then // Numeric keys or backspace
    begin
      sgCartKeyPress(sgCart, Key);
      Key := #0; // Consume the key
      Exit;
    end;
  end;
  
  // Handle other keyboard input
  // case Key of
  //   #27: if Assigned(btnBack.OnClick) then btnBack.OnClick(btnBack); // ESC key
  //   '1'..'9', '0', '.':
  //     if not (ActiveControl = sgCart) then // Only handle number keys if cart doesn't have focus
  //       btnNumberClick(Self.FindComponent('btn' + Key));
  //   #13: if Assigned(btnPay.OnClick) then btnPay.OnClick(btnPay);  // Enter key
  //   #8:  if Assigned(btnBackspace.OnClick) and not (ActiveControl = sgCart) then 
  //          btnBackspace.OnClick(btnBackspace); // Backspace (only if cart doesn't have focus)
  // end;
end;

procedure TfrmPOS.SetupButtonStyles;
var
  i: Integer;
  Btn: TBitBtn;
begin
  // Setup touchscreen-optimized button styles
  for i := 0 to ComponentCount - 1 do
  begin
    if Components[i] is TBitBtn then
    begin
      Btn := TBitBtn(Components[i]);

      // Apply touchscreen-friendly properties
      Btn.ParentFont := False;
      Btn.Font.Name := 'Segoe UI';
      Btn.Font.Style := [fsBold];

      // Set minimum touch target sizes
      if Btn.Height < 60 then
        Btn.Height := 80;
      if Btn.Width < 60 then
        Btn.Width := 100;

      // Add visual feedback for touch
      Btn.BorderSpacing.Around := 4;

      // Color coding based on button function
      if Pos('btn', Btn.Name) = 1 then
      begin
        if (Btn.Name = 'btnPay') then
        begin
          Btn.Color := $4CAF50;  // Green for primary action
          Btn.Font.Color := clWhite;
          Btn.Font.Height := -24;
        end
        else if (Btn.Name = 'btnClear') or (Btn.Name = 'btnVoid') then
        begin
          Btn.Color := $F44336;  // Red for destructive actions
          Btn.Font.Color := clWhite;
          Btn.Font.Height := -24;
        end
        else if (Btn.Name = 'btnQuantity') or (Btn.Name = 'btnPrint') then
        begin
          Btn.Color := $2196F3;  // Blue for secondary actions
          Btn.Font.Color := clWhite;
          Btn.Font.Height := -24;
        end
        else if (Btn.Name = 'btnDiscount') then
        begin
          Btn.Color := $FF9800;  // Orange for warning actions
          Btn.Font.Color := clWhite;
          Btn.Font.Height := -24;
        end
        else
        begin
          // Number buttons and others
          Btn.Color := clWhite;
          Btn.Font.Color := $333333;
          Btn.Font.Height := -24;
        end;
      end;
    end;
  end;
  
  // Special styling for action buttons

  btnPay.Font.Color := clBlue;
  

  btnClear.Font.Color := clBlue;
  

  btnVoid.Font.Color := clBlue;
end;

function TfrmPOS.LoadImageFromBlob(BlobField: TBlobField): TBGRABitmap;
var
  Stream: TMemoryStream;
begin
  Result := nil;
  if not BlobField.IsNull then
  begin
    Stream := TMemoryStream.Create;
    try
      BlobField.SaveToStream(Stream);
      if Stream.Size > 0 then
      begin
        Stream.Position := 0;
        Result := TBGRABitmap.Create(Stream);
      end;
    finally
      Stream.Free;
    end;
  end;
end;

function TfrmPOS.SaveSale: Boolean;
var
  SaleID: Integer;
  i: Integer;
  ReceiptNo: string;
begin
  if FSaleItemCount = 0 then Exit;
  
  try
    // Get next receipt number
    ReceiptNo := dmMain.GetNextReceiptNumber;
    
    // Start transaction in DataModule
    if not dmMain.transMain.Active then
      dmMain.transMain.StartTransaction;
      
    try
      // Create sale header
      SaleID := dmMain.CreateSale(
        ReceiptNo,                    // ReceiptNo
        FCurrentUserID,               // UserID
        FCurrentShiftType,            // ShiftType
        FSubtotal,                    // Subtotal
        FDiscountAmount,              // Discount
        FTotal                        // Total
      );
      
      if SaleID <= 0 then
        raise Exception.Create('Failed to create sale record');
      
      // Add sale items
      for i := 0 to FSaleItemCount - 1 do
      begin
        if not dmMain.AddSaleItem(
          SaleID,                     // SaleID
          FSaleItems[i].ItemID,       // ItemID
          FSaleItems[i].Quantity,     // Quantity
          FSaleItems[i].UnitPrice     // UnitPrice
        ) then
          raise Exception.CreateFmt('Failed to add item %s to sale', [FSaleItems[i].ItemName]);
          
        // Update stock
        if not dmMain.UpdateItemStock(FSaleItems[i].ItemID, FSaleItems[i].Quantity) then
          Logger.LogWarning(Format('Failed to update stock for item %d', [FSaleItems[i].ItemID]));
      end;
      
      // Record payment
      if not dmMain.UpdateSalePayment(
        SaleID,                       // SaleID
        FPaymentMethod,               // PaymentMethod
        FAmountTendered,              // AmountReceived
        FChangeDue,                   // ChangeAmount
        '',                           // CardNumber
        '',                           // AuthCode
        '',                           // QRCode
        ''                            // QRReference
      ) then
        raise Exception.Create('Failed to record payment');
      
      // Commit transaction
      dmMain.transMain.Commit;
      
      // Log the sale
      if Assigned(Logger) then
        Logger.LogInfo(Format('Sale completed: %s, Items: %d, Total: %.2f', 
          [ReceiptNo, FSaleItemCount, FTotal]));
      
      // Return success
      Result := True;
// Update the end of the SaveSale method (around line 673)
if Result then
begin
  ShowReceipt(SaleID);
  ClearSale; // Clear the current sale after successful payment and receipt
end;        
    except
      on E: Exception do
      begin
        // Rollback on error
        dmMain.transMain.Rollback;
        if Assigned(Logger) then
          Logger.LogError('Error saving sale: ' + E.Message);
        raise;
      end;
    end;
  except
    on E: Exception do
    begin
      MessageDlg('Error', 'Failed to save sale: ' + E.Message, mtError, [mbOK], 0);
      Result := False;
    end;
  end;
end;

function TfrmPOS.ProcessPayment: Boolean;
var
  PaymentForm: TfrmPayment;
  PaymentResult: TModalResult;
begin
  Result := False;
  PaymentForm := TfrmPayment.Create(Self);
  try
    PaymentForm.SetPaymentInfo(FPaymentMethod, FTotal);
    PaymentResult := PaymentForm.ShowModal;
    
    if PaymentResult = mrOK then
    begin
      FPaymentMethod := PaymentForm.PaymentMethod;
      FAmountTendered := PaymentForm.AmountReceived;
      FChangeDue := PaymentForm.ChangeAmount;
      
      if SameText(FPaymentMethod, 'CARD') then
      begin
        FCardLastFour := RightStr(PaymentForm.CardNumber, 4);
        FCardAuthCode := PaymentForm.AuthCode;
      end
      else if SameText(FPaymentMethod, 'QR') then
      begin
        FQRReference := PaymentForm.QRReference;
      end;
      
      Result := True;
    end;
  finally
    PaymentForm.Free;
  end;
end;

// In frmPOS.pas, update the ShowReceipt method:
procedure TfrmPOS.ShowReceipt(SaleID: Integer);
var
  ReceiptForm: TfrmReceiptPreview;
begin
  ReceiptForm := TfrmReceiptPreview.Create(nil);
  try
    ReceiptForm.SetSaleInfo(
      SaleID, 
      FPaymentMethod, 
      FAmountTendered, 
      FChangeDue
    );
    ReceiptForm.ShowModal;
  finally
    ReceiptForm.Free;
  end;
end;

procedure TfrmPOS.AddItemToSale(ItemID: Integer; const ItemCode, ItemName: string; 
  UnitPrice: Currency; Quantity: Integer = 1);
var
  i: Integer;
  Found: Boolean;
begin
  Found := False;
  
  // Check if item already exists in sale
  for i := 0 to FSaleItemCount - 1 do
  begin
    if FSaleItems[i].ItemID = ItemID then
    begin
      // Update quantity if item already in sale
      FSaleItems[i].Quantity := FSaleItems[i].Quantity + Quantity;
      FSaleItems[i].LineTotal := FSaleItems[i].Quantity * UnitPrice;
      Found := True;
      Break;
    end;
  end;
  
  // If item not found, add new item to sale
  if not Found then
  begin
    // Increase array size
    SetLength(FSaleItems, FSaleItemCount + 1);
    
    // Add new item
    FSaleItems[FSaleItemCount].ItemID := ItemID;
    FSaleItems[FSaleItemCount].ItemCode := ItemCode;
    FSaleItems[FSaleItemCount].ItemName := ItemName;
    FSaleItems[FSaleItemCount].Quantity := Quantity;
    FSaleItems[FSaleItemCount].UnitPrice := UnitPrice;
    FSaleItems[FSaleItemCount].LineTotal := Quantity * UnitPrice;
    
    // Increment item count
    Inc(FSaleItemCount);
  end;
  
  // Update the UI
  UpdateSaleGrid;
  CalculateTotals;
end;

procedure TfrmPOS.ClearSale;
begin
  SetLength(FSaleItems, 0);
  FSaleItemCount := 0;
  FSubtotal := 0;
  FTax := 0;
  FTotal := 0;
  FDiscountAmount := 0;
  FAmountTendered := 0;
  FChangeDue := 0;
  UpdateSaleGrid;
  UpdateTotals;
end;

procedure TfrmPOS.UpdateSaleGrid;
var
  i: Integer;
  WasSelected: Boolean;
  SelectedRow: Integer;
begin
  // Remember if we had a selection and which row it was
  WasSelected := (FSelectedCartRow > 0) and (FSelectedCartRow < sgCart.RowCount);
  if WasSelected then
    SelectedRow := FSelectedCartRow
  else
    SelectedRow := -1;
  
  // Clear the grid
  sgCart.RowCount := 1; // Keep header row
  
  // Add items to the grid
  for i := 0 to FSaleItemCount - 1 do
  begin
    sgCart.RowCount := sgCart.RowCount + 1;
    sgCart.Cells[0, i+1] := FSaleItems[i].ItemName;
    sgCart.Cells[1, i+1] := IntToStr(FSaleItems[i].Quantity);
    sgCart.Cells[2, i+1] := Format('%.2f', [FSaleItems[i].UnitPrice]);
    sgCart.Cells[3, i+1] := Format('%.2f', [FSaleItems[i].LineTotal]);
  end;
  
  // Auto-size columns
  sgCart.AutoSizeColumns;
  
  // Restore selection if needed
  if WasSelected and (SelectedRow <= FSaleItemCount) then
  begin
    if SelectedRow > sgCart.RowCount - 1 then
      SelectedRow := sgCart.RowCount - 1;
    if SelectedRow > 0 then
    begin
      sgCart.Selection := TGridRect(Rect(0, SelectedRow, sgCart.ColCount-1, SelectedRow));
      FSelectedCartRow := SelectedRow;
    end;
  end
  else if FSaleItemCount > 0 then
  begin
    sgCart.Selection := TGridRect(Rect(0, 1, sgCart.ColCount-1, 1));
    FSelectedCartRow := 1;
  end
  else
  begin
    FSelectedCartRow := -1;
    FQuantityInput := '';
  end;
  
  // Update totals
  CalculateTotals;
end;

procedure TfrmPOS.CalculateTotals;
var
  i: Integer;
  Subtotal: Currency;
begin
  // Calculate subtotal
  Subtotal := 0;
  for i := 0 to FSaleItemCount - 1 do
  begin
    Subtotal := Subtotal + FSaleItems[i].LineTotal;
  end;
  
  // Calculate tax (assuming 10% tax rate as an example)
  FTax := Subtotal * 0.1; // 10% tax rate
  
  // Calculate total
  FTotal := Subtotal + FTax;
  
  // Update the UI
  UpdateTotals;
end;

procedure TfrmPOS.UpdateTotals;
var
  i: Integer;
  SubTotal, Tax, Total: Currency;
begin
  try
    // Calculate subtotal
    SubTotal := 0;
    for i := 0 to High(FSaleItems) do
      SubTotal := SubTotal + FSaleItems[i].LineTotal;
    
    // Calculate tax (if any)
    Tax := 0; // TODO: Implement tax calculation if needed
    
    // Calculate total
    Total := SubTotal + Tax;
    
    // Update UI
    lblSubtotalValue.Caption := Format('$%.2f', [SubTotal]);
    lblTaxValue.Caption := Format('$%.2f', [Tax]);
    lblTotalValue.Caption := Format('$%.2f', [Total]);
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error updating totals: ' + E.Message);
    end;
  end;
end;

procedure TfrmPOS.UpdateQuantityDisplay(Quantity: Integer);
var i: integer;
begin
  // Update the quantity display in the UI
  if Assigned(FQuantityEdit) then
    FQuantityEdit.Text := IntToStr(Quantity);
    
  // If we have a current item, update its quantity in the cart
  if Assigned(FCurrentItem) then
  begin
    // Find the item in the sale and update its quantity
    for i := 0 to FSaleItemCount - 1 do
    begin
      if FSaleItems[i].ItemID = FCurrentItem.ItemID then
      begin
        FSaleItems[i].Quantity := Quantity;
        FSaleItems[i].LineTotal := Quantity * FSaleItems[i].UnitPrice;
        UpdateSaleGrid;
        CalculateTotals;
        Break;
      end;
    end;
  end;
end;

procedure TfrmPOS.ClearInput;
begin
  // Clear the input buffer and reset the display
  FCurrentInput := '0';
  
  // Clear the quantity edit if it exists
  if Assigned(FQuantityEdit) then
    FQuantityEdit.Text := '1';
    
  // Clear the amount edit if it exists
  if Assigned(FAmountEdit) then
    FAmountEdit.Text := '0';
    
  // Reset the current item reference
  FCurrentItem := nil;
end;

end.
