unit frmPayment;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, ComCtrls, Logging;

type
  TfrmPayment = class(TForm)
    pnlTop: TPanel;
    lblPaymentMethod: TLabel;
    lblPaymentMethodValue: TLabel;
    lblAmount: TLabel;
    lblAmountValue: TLabel;
    
    pnlMain: TPanel;
    pcPayment: TPageControl;
    
    // Cash payment tab
    tsCash: TTabSheet;
    lblAmountReceived: TLabel;
    edtAmountReceived: TEdit;
    lblChange: TLabel;
    edtChange: TEdit;
    btnCalculate: TBitBtn;
    
    // Card payment tab
    tsCard: TTabSheet;
    lblCardNumber: TLabel;
    edtCardNumber: TEdit;
    lblAuthCode: TLabel;
    edtAuthCode: TEdit;
    btnProcessCard: TBitBtn;
    lblCardStatus: TLabel;
    
    // QR payment tab
    tsQR: TTabSheet;
    lblQRCode: TLabel;
    edtQRCode: TEdit;
    lblQRReference: TLabel;
    edtQRReference: TEdit;
    btnGenerateQR: TBitBtn;
    btnVerifyQR: TBitBtn;
    lblQRStatus: TLabel;
    
    pnlButtons: TPanel;
    btnOK: TBitBtn;
    btnCancel: TBitBtn;
    
    procedure FormCreate(Sender: TObject);
    procedure btnCalculateClick(Sender: TObject);
    procedure btnProcessCardClick(Sender: TObject);
    procedure btnGenerateQRClick(Sender: TObject);
    procedure btnVerifyQRClick(Sender: TObject);
    procedure edtAmountReceivedChange(Sender: TObject);
    procedure pcPaymentChange(Sender: TObject);
    
  private
    FPaymentMethod: string;
    FAmount: Currency;
    FAmountReceived: Currency;
    FChangeAmount: Currency;
    FCardNumber: string;
    FAuthCode: string;
    FQRCode: string;
    FQRReference: string;
    
    procedure CalculateChange;
    procedure ValidatePayment;
    procedure ApplyTouchscreenStyling;
    
  public
    procedure SetPaymentInfo(const PaymentMethod: string; Amount: Currency);
    property PaymentMethod: string read FPaymentMethod;
    property AmountReceived: Currency read FAmountReceived;
    property ChangeAmount: Currency read FChangeAmount;
    property CardNumber: string read FCardNumber;
    property AuthCode: string read FAuthCode;
    property QRCode: string read FQRCode;
    property QRReference: string read FQRReference;
  end;

var
  formPayment: TfrmPayment;

implementation

{$R *.lfm}

procedure TfrmPayment.FormCreate(Sender: TObject);
begin
  FAmount := 0;
  FAmountReceived := 0;
  FChangeAmount := 0;
  btnOK.Enabled := False;
end;

procedure TfrmPayment.SetPaymentInfo(const PaymentMethod: string; Amount: Currency);
begin
  FPaymentMethod := PaymentMethod;
  FAmount := Amount;

  lblPaymentMethodValue.Caption := PaymentMethod;
  lblAmountValue.Caption := '$' + FormatFloat('#,##0.00', Amount);
  edtAmountReceived.Text := FormatFloat('#,##0.00', Amount);

  // Set active tab based on payment method
  if PaymentMethod = 'CASH' then
    pcPayment.ActivePage := tsCash
  else if PaymentMethod = 'CARD' then
    pcPayment.ActivePage := tsCard
  else if PaymentMethod = 'QR' then
    pcPayment.ActivePage := tsQR;

  // Apply touchscreen-friendly styling
  ApplyTouchscreenStyling;

  CalculateChange;
end;

procedure TfrmPayment.btnCalculateClick(Sender: TObject);
begin
  CalculateChange;
end;

procedure TfrmPayment.CalculateChange;
begin
  try
    FAmountReceived := StrToCurrDef(edtAmountReceived.Text, 0);
    FChangeAmount := FAmountReceived - FAmount;

    edtChange.Text := '$' + FormatFloat('#,##0.00', FChangeAmount);

    if FChangeAmount < 0 then
    begin
      edtChange.Font.Color := clRed;
      edtChange.Color := $FFE6E6; // Light red background
      btnOK.Enabled := False;
      btnOK.Caption := 'INSUFFICIENT';
    end
    else
    begin
      edtChange.Font.Color := $4CAF50; // Green
      edtChange.Color := $E8F5E8; // Light green background
      btnOK.Enabled := True;
      btnOK.Caption := 'CONFIRM';
    end;

  except
    on E: Exception do
    begin
      edtChange.Text := 'Error';
      edtChange.Font.Color := clRed;
      edtChange.Color := $FFE6E6;
      btnOK.Enabled := False;
      btnOK.Caption := 'ERROR';
    end;
  end;
end;

procedure TfrmPayment.edtAmountReceivedChange(Sender: TObject);
begin
  CalculateChange;
end;

procedure TfrmPayment.btnProcessCardClick(Sender: TObject);
begin
  try
    FCardNumber := edtCardNumber.Text;
    
    if Length(FCardNumber) < 4 then
    begin
      ShowMessage('Please enter a valid card number.');
      edtCardNumber.SetFocus;
      Exit;
    end;
    
    // Simulate card processing
    lblCardStatus.Caption := 'Processing...';
    lblCardStatus.Font.Color := clBlue;
    Application.ProcessMessages;
    
    Sleep(2000); // Simulate processing time
    
    // Generate mock authorization code
    FAuthCode := 'AUTH' + FormatDateTime('hhnnss', Now);
    edtAuthCode.Text := FAuthCode;
    
    lblCardStatus.Caption := 'Approved';
    lblCardStatus.Font.Color := clGreen;
    
    btnOK.Enabled := True;
    
  except
    on E: Exception do
    begin
      lblCardStatus.Caption := 'Error: ' + E.Message;
      lblCardStatus.Font.Color := clRed;
      btnOK.Enabled := False;
      
      if Assigned(Logger) then
        Logger.LogError('Card processing error: ' + E.Message);
    end;
  end;
end;

procedure TfrmPayment.btnGenerateQRClick(Sender: TObject);
begin
  try
    // Generate QR code (mock implementation)
    FQRCode := 'QR' + FormatDateTime('yyyymmddhhnnss', Now);
    edtQRCode.Text := FQRCode;
    
    lblQRStatus.Caption := 'QR Code generated. Waiting for payment...';
    lblQRStatus.Font.Color := clBlue;
    
    btnVerifyQR.Enabled := True;
    
  except
    on E: Exception do
    begin
      lblQRStatus.Caption := 'Error generating QR code';
      lblQRStatus.Font.Color := clRed;
      
      if Assigned(Logger) then
        Logger.LogError('QR generation error: ' + E.Message);
    end;
  end;
end;

procedure TfrmPayment.btnVerifyQRClick(Sender: TObject);
begin
  try
    FQRCode := edtQRCode.Text;
    
    if FQRCode = '' then
    begin
      ShowMessage('Please generate or enter QR code first.');
      Exit;
    end;
    
    // Simulate payment verification
    lblQRStatus.Caption := 'Verifying payment...';
    lblQRStatus.Font.Color := clBlue;
    Application.ProcessMessages;
    
    Sleep(1500); // Simulate verification time
    
    // Generate reference number
    FQRReference := 'REF' + FormatDateTime('hhnnss', Now);
    edtQRReference.Text := FQRReference;
    
    lblQRStatus.Caption := 'Payment verified successfully';
    lblQRStatus.Font.Color := clGreen;
    
    btnOK.Enabled := True;
    
  except
    on E: Exception do
    begin
      lblQRStatus.Caption := 'Payment verification failed';
      lblQRStatus.Font.Color := clRed;
      btnOK.Enabled := False;
      
      if Assigned(Logger) then
        Logger.LogError('QR verification error: ' + E.Message);
    end;
  end;
end;

procedure TfrmPayment.pcPaymentChange(Sender: TObject);
begin
  ValidatePayment;
end;

procedure TfrmPayment.ValidatePayment;
begin
  btnOK.Enabled := False;
  
  case pcPayment.ActivePageIndex of
    0: // Cash
      CalculateChange;
    1: // Card
      btnOK.Enabled := (edtCardNumber.Text <> '') and (edtAuthCode.Text <> '');
    2: // QR
      btnOK.Enabled := (edtQRCode.Text <> '') and (edtQRReference.Text <> '');
  end;
end;

procedure TfrmPayment.ApplyTouchscreenStyling;
var
  i: Integer;
  Btn: TBitBtn;
begin
  // Apply touchscreen-friendly styling to all buttons
  for i := 0 to ComponentCount - 1 do
  begin
    if Components[i] is TBitBtn then
    begin
      Btn := TBitBtn(Components[i]);

      // Ensure minimum touch target size
      if Btn.Height < 50 then
        Btn.Height := 60;
      if Btn.Width < 80 then
        Btn.Width := 120;

      // Apply consistent font styling
      Btn.ParentFont := False;
      Btn.Font.Name := 'Segoe UI';
      Btn.Font.Style := [fsBold];

      if Btn.Font.Height > -16 then
        Btn.Font.Height := -20;
    end;
  end;

  // Style input fields for better touch interaction
  edtAmountReceived.Font.Height := -24;
  edtAmountReceived.Height := 50;
  edtChange.Font.Height := -24;
  edtChange.Height := 50;

  // Style card input fields if they exist
  if Assigned(edtCardNumber) then
  begin
    edtCardNumber.Font.Height := -20;
    edtCardNumber.Height := 50;
  end;

  if Assigned(edtAuthCode) then
  begin
    edtAuthCode.Font.Height := -20;
    edtAuthCode.Height := 50;
  end;

  // Style QR input fields if they exist
  if Assigned(edtQRCode) then
  begin
    edtQRCode.Font.Height := -20;
    edtQRCode.Height := 50;
  end;

  if Assigned(edtQRReference) then
  begin
    edtQRReference.Font.Height := -20;
    edtQRReference.Height := 50;
  end;
end;

end.
