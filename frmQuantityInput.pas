unit frmQuantityInput;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, Spin;

type
  TfrmQuantityInput = class(TForm)
    pnlMain: TPanel;
    lblItem: TLabel;
    lblItemName: TLabel;
    lblPrice: TLabel;
    lblPriceValue: TLabel;
    
    pnlQuantity: TPanel;
    lblQuantity: TLabel;
    seQuantity: TSpinEdit;
    
    pnlTotal: TPanel;
    lblLineTotal: TLabel;
    lblLineTotalValue: TLabel;
    
    pnlButtons: TPanel;
    btnOK: TBitBtn;
    btnCancel: TBitBtn;
    
    procedure FormCreate(Sender: TObject);
    procedure seQuantityChange(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    
  private
    FItemName: string;
    FUnitPrice: Currency;
    FQuantity: Integer;
    
    procedure UpdateTotal;
    
  public
    procedure SetItemInfo(const ItemName: string; UnitPrice: Currency; InitialQuantity: Integer = 1);
    property Quantity: Integer read FQuantity;
  end;

var
  formQuantityInput: TfrmQuantityInput;

implementation

{$R *.lfm}

procedure TfrmQuantityInput.FormCreate(Sender: TObject);
begin
  FQuantity := 1;
  seQuantity.Value := 1;
  seQuantity.MinValue := 1;
  seQuantity.MaxValue := 9999;
end;

procedure TfrmQuantityInput.FormShow(Sender: TObject);
begin
  seQuantity.SetFocus;
  seQuantity.SelectAll;
end;

procedure TfrmQuantityInput.SetItemInfo(const ItemName: string; UnitPrice: Currency; InitialQuantity: Integer = 1);
begin
  FItemName := ItemName;
  FUnitPrice := UnitPrice;
  FQuantity := InitialQuantity;
  
  lblItemName.Caption := ItemName;
  lblPriceValue.Caption := FormatFloat('#,##0.00', UnitPrice);
  seQuantity.Value := InitialQuantity;
  
  UpdateTotal;
end;

procedure TfrmQuantityInput.seQuantityChange(Sender: TObject);
begin
  UpdateTotal;
end;

procedure TfrmQuantityInput.UpdateTotal;
var
  Total: Currency;
begin
  Total := seQuantity.Value * FUnitPrice;
  lblLineTotalValue.Caption := FormatFloat('#,##0.00', Total);
end;

procedure TfrmQuantityInput.btnOKClick(Sender: TObject);
begin
  if seQuantity.Value > 0 then
  begin
    FQuantity := seQuantity.Value;
    ModalResult := mrOK;
  end
  else
    ShowMessage('Quantity must be greater than 0.');
end;

procedure TfrmQuantityInput.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

end.
