unit frmReceiptPreview;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, ComCtrls, Logging, DataModule, ConfigUtils, PrinterUtils, StringUtils;

type
  TfrmReceiptPreview = class(TForm)
    pnlTop: TPanel;
    lblTitle: TLabel;
    
    pnlMain: TPanel;
    memoReceipt: TMemo;
    
    pnlButtons: TPanel;
    btnPrint: TBitBtn;
    btnEmail: TBitBtn;
    btnSave: TBitBtn;
    btnClose: TBitBtn;
    
    SaveDialog1: TSaveDialog;
    
    procedure FormCreate(Sender: TObject);
    procedure btnPrintClick(Sender: TObject);
    procedure btnEmailClick(Sender: TObject);
    procedure btnSaveClick(Sender: TObject);
    procedure btnCloseClick(Sender: TObject);
    
  private
    FSaleID: Integer;
    FPaymentMethod: string;
    FAmountTendered: Currency;
    FChangeDue: Currency;
    FReceiptContent: TStringList;
    
    procedure BuildReceiptContent;
    
  public
    destructor Destroy; override;
  procedure SetSaleInfo(SaleID: Integer; const PaymentMethod: string; 
    AmountTendered, ChangeDue: Currency);
  end;

var
  formReceiptPreview: TfrmReceiptPreview;

implementation

{$R *.lfm}

procedure TfrmReceiptPreview.FormCreate(Sender: TObject);
begin
  FReceiptContent := TStringList.Create;
  memoReceipt.Font.Name := 'Courier New';
  memoReceipt.Font.Size := 9;
  memoReceipt.ReadOnly := True;
  memoReceipt.ScrollBars := ssVertical;
end;

procedure TfrmReceiptPreview.SetSaleInfo(SaleID: Integer; 
  const PaymentMethod: string; AmountTendered, ChangeDue: Currency);
begin
  try
    FSaleID := SaleID;
    FPaymentMethod := PaymentMethod;
    FAmountTendered := AmountTendered;
    FChangeDue := ChangeDue;
    
    if FSaleID > 0 then
    begin
      BuildReceiptContent;
      if Assigned(FReceiptContent) and (FReceiptContent.Count > 0) then
        memoReceipt.Lines.Assign(FReceiptContent)
      else
      begin
        memoReceipt.Lines.Clear;
        memoReceipt.Lines.Add('Error: Unable to load receipt data');
      end;
    end
    else
    begin
      memoReceipt.Lines.Clear;
      memoReceipt.Lines.Add('Error: Invalid Sale ID');
    end;
  except
    on E: Exception do
    begin
      memoReceipt.Lines.Clear;
      memoReceipt.Lines.Add('Error loading receipt: ' + E.Message);
      if Assigned(Logger) then
        Logger.LogError('SetSaleInfo error: ' + E.Message);
    end;
  end;
end;

procedure TfrmReceiptPreview.BuildReceiptContent;
var
  CompanyName, CompanyAddress, CompanyPhone: string;
  ReceiptNo, SaleDate, Username, ShiftType: string;
  Subtotal, DiscountAmount, TotalAmount: Currency;
  PaymentMethod: string;
  AmountReceived, ChangeAmount: Currency;
begin
  try
    if not Assigned(FReceiptContent) then
      FReceiptContent := TStringList.Create;
      
    FReceiptContent.Clear;
    
    if not Assigned(dmMain) then
    begin
      FReceiptContent.Add('Error: Database not available');
      Exit;
    end;

    // Get sale details
    if dmMain.GetSaleDetails(FSaleID) then
    begin
      if dmMain.qrySales.EOF then
      begin
        FReceiptContent.Add('Error: Sale not found');
        Exit;
      end;
      
      // Safely get field values
      ReceiptNo := dmMain.qrySales.FieldByName('RECEIPT_NO').AsString;
      SaleDate := FormatDateTime('dd/mm/yyyy hh:nn', dmMain.qrySales.FieldByName('SALE_DATE').AsDateTime);
      Username := dmMain.qrySales.FieldByName('USERNAME').AsString; 
      ShiftType := dmMain.qrySales.FieldByName('SHIFT_TYPE').AsString;
      Subtotal := dmMain.qrySales.FieldByName('SUBTOTAL').AsCurrency;
      DiscountAmount := dmMain.qrySales.FieldByName('DISCOUNT_AMOUNT').AsCurrency;
      TotalAmount := dmMain.qrySales.FieldByName('TOTAL_AMOUNT').AsCurrency;
      PaymentMethod := dmMain.qrySales.FieldByName('PAYMENT_METHOD').AsString;
      AmountReceived := dmMain.qrySales.FieldByName('AMOUNT_RECEIVED').AsCurrency;
      ChangeAmount := dmMain.qrySales.FieldByName('CHANGE_AMOUNT').AsCurrency;
    end
    else
    begin
      FReceiptContent.Add('Error: Unable to load sale details');
      Exit;
    end;

    // Get company info safely
    if Assigned(Config) then
    begin
      CompanyName := Config.GetValue('Company', 'Name', 'POS System');
      CompanyAddress := Config.GetValue('Company', 'Address', '');
      CompanyPhone := Config.GetValue('Company', 'Phone', '');
    end
    else
    begin
      CompanyName := 'POS System';
      CompanyAddress := '';
      CompanyPhone := '';
    end;

    // Build receipt header
    FReceiptContent.Add(StringOfChar('=', 40));
    FReceiptContent.Add(CenterText(CompanyName, 40));
    if CompanyAddress <> '' then
      FReceiptContent.Add(CenterText(CompanyAddress, 40));
    if CompanyPhone <> '' then
      FReceiptContent.Add(CenterText('Tel: ' + CompanyPhone, 40));
    FReceiptContent.Add(StringOfChar('=', 40));

    // Sale info
    FReceiptContent.Add('Receipt: ' + ReceiptNo);
    FReceiptContent.Add('Date: ' + SaleDate);
    FReceiptContent.Add('Cashier: ' + Username);
    FReceiptContent.Add('Shift: ' + ShiftType);
    FReceiptContent.Add(StringOfChar('-', 40));

    // Get and display items
    if dmMain.GetSaleItems(FSaleID) then
    begin
      if not dmMain.qrySaleItems.EOF then
      begin
        dmMain.qrySaleItems.First;
        while not dmMain.qrySaleItems.EOF do
        begin
          try
            // Get item name - you might need to join with items table
            FReceiptContent.Add(dmMain.qrySaleItems.FieldByName('ITEM_NAME').AsString); 
            FReceiptContent.Add(Format('  %d x %s = %s', [
              dmMain.qrySaleItems.FieldByName('QUANTITY').AsInteger,
              FormatFloat('#,##0.00', dmMain.qrySaleItems.FieldByName('UNIT_PRICE').AsCurrency),
              FormatFloat('#,##0.00', dmMain.qrySaleItems.FieldByName('TOTAL_PRICE').AsCurrency)
            ]));

            
            dmMain.qrySaleItems.Next;
          except
            on E: Exception do
            begin
              FReceiptContent.Add('Error reading item: ' + E.Message);
              Break;
            end;
          end;
        end;
      end
      else
        FReceiptContent.Add('No items found');
    end
    else
      FReceiptContent.Add('Error loading items');

    // Totals
    FReceiptContent.Add(StringOfChar('-', 40));
    FReceiptContent.Add('Subtotal: ' + FormatFloat('#,##0.00', Subtotal));

    if DiscountAmount > 0 then
      FReceiptContent.Add('Discount: ' + FormatFloat('#,##0.00', DiscountAmount));

    FReceiptContent.Add('TOTAL: ' + FormatFloat('#,##0.00', TotalAmount));

    // Payment info
    FReceiptContent.Add('');
    FReceiptContent.Add('Payment: ' + PaymentMethod);

    if PaymentMethod = 'CASH' then
    begin
      FReceiptContent.Add('Received: ' + FormatFloat('#,##0.00', AmountReceived));
      FReceiptContent.Add('Change: ' + FormatFloat('#,##0.00', ChangeAmount));
    end;

    // Footer
    FReceiptContent.Add(StringOfChar('=', 40));
    FReceiptContent.Add(CenterText('Thank You!', 40));
    FReceiptContent.Add(CenterText('Please Come Again', 40));
    FReceiptContent.Add(StringOfChar('=', 40));

  except
    on E: Exception do
    begin
      if Assigned(FReceiptContent) then
      begin
        FReceiptContent.Clear;
        FReceiptContent.Add('Error building receipt: ' + E.Message);
      end;
      if Assigned(Logger) then
        Logger.LogError('BuildReceiptContent error: ' + E.Message);
    end;
  end;
end;

// Add destructor to clean up
destructor TfrmReceiptPreview.Destroy;
begin
  if Assigned(FReceiptContent) then
    FReceiptContent.Free;
  inherited Destroy;
end;

procedure TfrmReceiptPreview.btnPrintClick(Sender: TObject);
begin
  try
    if Assigned(FReceiptContent) and (FReceiptContent.Count > 0) then
    begin
      if Assigned(Config) then
        PrintReceipt(FReceiptContent, Config.GetValue('Application', 'ReceiptPrinter', ''))
      else
        PrintReceipt(FReceiptContent, '');
      ShowMessage('Receipt sent to printer.');
    end
    else
      ShowMessage('No receipt data to print.');
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Print error: ' + E.Message);
      ShowMessage('Print error: ' + E.Message);
    end;
  end;
end;

procedure TfrmReceiptPreview.btnEmailClick(Sender: TObject);
var
  Email: string;
begin
  try
    if not Assigned(FReceiptContent) or (FReceiptContent.Count = 0) then
    begin
      ShowMessage('No receipt data to email.');
      Exit;
    end;
    
    Email := '';
    if InputQuery('Email Receipt', 'Enter email address:', Email) then
    begin
      // TODO: Implement email functionality
      ShowMessage('Email functionality not implemented yet.' + #13#10 + 
                  'Email: ' + Email);
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnEmailClick error: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmReceiptPreview.btnSaveClick(Sender: TObject);
begin
  try
    if not Assigned(FReceiptContent) or (FReceiptContent.Count = 0) then
    begin
      ShowMessage('No receipt data to save.');
      Exit;
    end;
    
    SaveDialog1.FileName := 'Receipt_' + IntToStr(FSaleID) + '.txt';
    SaveDialog1.Filter := 'Text files (*.txt)|*.txt|All files (*.*)|*.*';
    SaveDialog1.DefaultExt := 'txt';
    
    if SaveDialog1.Execute then
    begin
      try
        FReceiptContent.SaveToFile(SaveDialog1.FileName);
        ShowMessage('Receipt saved to: ' + SaveDialog1.FileName);
      except
        on E: Exception do
        begin
          if Assigned(Logger) then
            Logger.LogError('Save error: ' + E.Message);
          ShowMessage('Save error: ' + E.Message);
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnSaveClick error: ' + E.Message);
      ShowMessage('Error: ' + E.Message);
    end;
  end;
end;

procedure TfrmReceiptPreview.btnCloseClick(Sender: TObject);
begin
  try
    ModalResult := mrOK;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnCloseClick error: ' + E.Message);
      Close;
    end;
  end;
end;

// Make CenterText function safer
function CenterText(const Text: string; Width: Integer): string;
var
  Spaces: Integer;
  SafeText: string;
begin
  try
    SafeText := Text;
    if SafeText = '' then
      SafeText := ' ';
      
    if Length(SafeText) >= Width then
      Result := Copy(SafeText, 1, Width)
    else
    begin
      Spaces := (Width - Length(SafeText)) div 2;
      if Spaces < 0 then Spaces := 0;
      Result := StringOfChar(' ', Spaces) + SafeText;
    end;
  except
    Result := Text; // Fallback to original text
  end;
end;

end.
