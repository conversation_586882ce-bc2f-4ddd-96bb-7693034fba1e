object frmReports: TfrmReports
  Left = 122
  Height = 700
  Top = 81
  Width = 1000
  BorderIcons = [biSystemMenu, biMinimize]
  BorderStyle = bsSingle
  Caption = 'Reports'
  ClientHeight = 700
  ClientWidth = 1000
  Position = poScreenCenter
  LCLVersion = '3.8.0.0'
  object pnlMain: TPanel
    Left = 0
    Height = 700
    Top = 0
    Width = 1000
    Align = alClient
    ClientHeight = 700
    ClientWidth = 1000
    TabOrder = 0
    object pnlHeader: TPanel
      Left = 1
      Height = 60
      Top = 1
      Width = 998
      Align = alTop
      ClientHeight = 60
      ClientWidth = 998
      TabOrder = 0
      object lblTitle: TLabel
        Left = 16
        Height = 21
        Top = 20
        Width = 58
        Caption = 'Reports'
        Font.Height = -16
        Font.Style = [fsBold]
        ParentFont = False
      end
    end
    object pnlControls: TPanel
      Left = 1
      Height = 120
      Top = 61
      Width = 998
      Align = alTop
      ClientHeight = 120
      ClientWidth = 998
      TabOrder = 1
      object lblReportType: TLabel
        Left = 16
        Height = 15
        Top = 16
        Width = 66
        Caption = 'Report Type:'
      end
      object cmbReportType: TComboBox
        Left = 100
        Height = 23
        Top = 13
        Width = 200
        ItemHeight = 15
        Items.Strings = (
          'Daily Sales Report'
          'Shift Report'
          'Product Sales Report'
          'Payment Method Report'
          'User Sales Report'
        )
        Style = csDropDownList
        TabOrder = 0
        OnChange = cmbReportTypeChange
      end
      object lblStartDate: TLabel
        Left = 16
        Height = 15
        Top = 48
        Width = 54
        Caption = 'Start Date:'
      end
      object dtpStartDate: TDateTimePicker
        Left = 100
        Height = 23
        Top = 45
        Width = 83
        CenturyFrom = 1941
        MaxDate = 2958465
        MinDate = -53780
        TabOrder = 1
        TrailingSeparator = False
        TextForNullDate = 'NULL'
        LeadingZeros = True
        Kind = dtkDate
        TimeFormat = tf24
        TimeDisplay = tdHMS
        DateMode = dmComboBox
        Date = 45292
        Time = 0.99998842592322
        UseDefaultSeparators = True
        HideDateTimeParts = []
        MonthNames = 'Long'
      end
      object lblEndDate: TLabel
        Left = 240
        Height = 15
        Top = 48
        Width = 50
        Caption = 'End Date:'
      end
      object dtpEndDate: TDateTimePicker
        Left = 310
        Height = 23
        Top = 45
        Width = 83
        CenturyFrom = 1941
        MaxDate = 2958465
        MinDate = -53780
        TabOrder = 2
        TrailingSeparator = False
        TextForNullDate = 'NULL'
        LeadingZeros = True
        Kind = dtkDate
        TimeFormat = tf24
        TimeDisplay = tdHMS
        DateMode = dmComboBox
        Date = 45292
        Time = 0.99998842592322
        UseDefaultSeparators = True
        HideDateTimeParts = []
        MonthNames = 'Long'
      end
      object lblShift: TLabel
        Left = 16
        Height = 15
        Top = 80
        Width = 61
        Caption = 'Select Shift:'
        Visible = False
      end
      object cmbShift: TComboBox
        Left = 100
        Height = 23
        Top = 77
        Width = 330
        ItemHeight = 15
        Style = csDropDownList
        TabOrder = 3
        Visible = False
        OnChange = cmbShiftChange
      end
      object btnGenerate: TButton
        Left = 450
        Height = 30
        Top = 45
        Width = 100
        Caption = 'Generate'
        TabOrder = 4
        OnClick = btnGenerateClick
      end
      object btnGenerateShiftReport: TButton
        Left = 450
        Height = 30
        Top = 77
        Width = 120
        Caption = 'Generate Report'
        Enabled = False
        TabOrder = 5
        Visible = False
        OnClick = btnGenerateShiftReportClick
      end
      object btnPrint: TButton
        Left = 570
        Height = 30
        Top = 45
        Width = 80
        Caption = 'Print'
        Enabled = False
        TabOrder = 6
        OnClick = btnPrintClick
      end
      object btnExport: TButton
        Left = 670
        Height = 30
        Top = 45
        Width = 80
        Caption = 'Export'
        Enabled = False
        TabOrder = 7
        OnClick = btnExportClick
      end
    end
    object pnlContent: TPanel
      Left = 1
      Height = 518
      Top = 181
      Width = 998
      Align = alClient
      ClientHeight = 518
      ClientWidth = 998
      TabOrder = 2
      object pgcReports: TPageControl
        Left = 1
        Height = 516
        Top = 1
        Width = 996
        ActivePage = tsPreview
        Align = alClient
        TabIndex = 1
        TabOrder = 0
        object tsGrid: TTabSheet
          Caption = 'Grid View'
          ClientHeight = 488
          ClientWidth = 988
          object sgReport: TStringGrid
            Left = 0
            Height = 488
            Top = 0
            Width = 988
            Align = alClient
            FixedCols = 0
            RowCount = 1
            TabOrder = 0
            ColWidths = (
              150
              100
              100
              100
              150
            )
          end
        end
        object tsPreview: TTabSheet
          Caption = 'Print Preview'
          ClientHeight = 488
          ClientWidth = 988
          object memoPreview: TMemo
            Left = 0
            Height = 488
            Top = 0
            Width = 988
            Align = alClient
            Font.Name = 'Courier New'
            Font.Pitch = fpFixed
            ParentFont = False
            ReadOnly = True
            ScrollBars = ssAutoBoth
            TabOrder = 0
          end
        end
        object tsChart: TTabSheet
          Caption = 'Chart View'
          ClientHeight = 488
          ClientWidth = 988
          object lblChartPlaceholder: TLabel
            Left = 400
            Height = 15
            Top = 236
            Width = 194
            Caption = 'Chart functionality not implemented'
            ParentColor = False
          end
        end
      end
    end
  end
  object dlgSave: TSaveDialog
    DefaultExt = '.txt'
    Filter = 'Text Files|*.txt|CSV Files|*.csv|All Files|*.*'
    Left = 920
    Top = 16
  end
end
