unit frmReports;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Buttons, Grids, ComCtrls, DataModule, Logging, DateUtils, DateTimePicker ;

type
  TfrmReports = class(TForm)
    // Main panels
    pnlMain: TPanel;
    pnlHeader: TPanel;
    pnlControls: TPanel;
    pnlContent: TPanel;
    
    // Header
    lblTitle: TLabel;
    
    // Controls
    lblReportType: TLabel;
    cmbReportType: TComboBox;
    lblStartDate: TLabel;
    dtpStartDate: TDateTimePicker;
    lblEndDate: TLabel;
    dtpEndDate: TDateTimePicker;
    lblShift: TLabel;
    cmbShift: TComboBox;
    btnGenerate: TButton;
    btnGenerateShiftReport: TButton;
    btnPrint: TButton;
    btnExport: TButton;
    
    // Content display
    pgcReports: TPageControl;
    tsGrid: TTabSheet;
    tsPreview: TTabSheet;
    tsChart: TTabSheet;
    sgReport: TStringGrid;
    memoPreview: TMemo;
    lblChartPlaceholder: TLabel;
    
    // Dialog
    dlgSave: TSaveDialog;
    
    // Event handlers
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure cmbReportTypeChange(Sender: TObject);
    procedure cmbShiftChange(Sender: TObject);
    procedure btnGenerateClick(Sender: TObject);
    procedure btnGenerateShiftReportClick(Sender: TObject);
    procedure btnPrintClick(Sender: TObject);
    procedure btnExportClick(Sender: TObject);
    
  private
    procedure LoadShifts;
    procedure LoadShiftReport;
    procedure GenerateShiftReport;
    procedure PrintShiftReport(ShiftID: Integer);
    procedure GenerateDailyReport;
    procedure GenerateProductReport;
    procedure GeneratePaymentReport;
    procedure GenerateUserReport;
    
  public
    { Public declarations }
  end;

var
  formReports: TfrmReports;

implementation

uses
  PrinterUtils, StringUtils;

{$R *.lfm}

// Remove the unused ShiftFilter variable and fix the procedures
procedure TfrmReports.FormCreate(Sender: TObject);
begin
  try
    // Initialize combo box
    cmbShift.Style := csDropDownList;
    cmbShift.ItemIndex := -1;
    
    // Initialize report type combo
    cmbReportType.ItemIndex := 0;
    
    // Set initial visibility
    cmbReportTypeChange(nil);
    
    if Assigned(Logger) then
      Logger.LogInfo('Reports form created');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error creating reports form: ' + E.Message);
      ShowMessage('Error initializing reports form: ' + E.Message);
    end;
  end;
end;

procedure TfrmReports.FormShow(Sender: TObject);
begin
  try
    // Set default dates
    dtpStartDate.Date := Date;
    dtpEndDate.Date := Date;
    
    // Load initial data based on report type
    if cmbReportType.ItemIndex = 1 then // Shift Report
      LoadShifts;
      
    if Assigned(Logger) then
      Logger.LogInfo('Reports form shown');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error showing reports form: ' + E.Message);
      ShowMessage('Error loading reports form: ' + E.Message);
    end;
  end;
end;

procedure TfrmReports.cmbReportTypeChange(Sender: TObject);
begin
  try
    // Show/hide controls based on report type
    case cmbReportType.ItemIndex of
      0: begin // Daily Sales Report
        lblShift.Visible := False;
        cmbShift.Visible := False;
        btnGenerateShiftReport.Visible := False;
        lblStartDate.Visible := True;
        dtpStartDate.Visible := True;
        lblEndDate.Visible := True;
        dtpEndDate.Visible := True;
        btnGenerate.Visible := True;
      end;
      1: begin // Shift Report
        lblShift.Visible := True;
        cmbShift.Visible := True;
        btnGenerateShiftReport.Visible := True;
        lblStartDate.Visible := False;
        dtpStartDate.Visible := False;
        lblEndDate.Visible := False;
        dtpEndDate.Visible := False;
        btnGenerate.Visible := False;
        
        // Load shifts when shift report is selected
        LoadShifts;
      end;
      2, 3, 4: begin // Product, Payment, User Reports
        lblShift.Visible := False;
        cmbShift.Visible := False;
        btnGenerateShiftReport.Visible := False;
        lblStartDate.Visible := True;
        dtpStartDate.Visible := True;
        lblEndDate.Visible := True;
        dtpEndDate.Visible := True;
        btnGenerate.Visible := True;
      end;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('cmbReportTypeChange error: ' + E.Message);
    end;
  end;
end;

procedure TfrmReports.LoadShifts;
begin
  try
    cmbShift.Items.Clear;
    cmbShift.Items.Add('-- Select a Shift --');
    
    // Get all shifts (you may want to limit this to recent shifts)
    if dmMain.GetAllShifts then
    begin
      dmMain.qryShifts.First;
      while not dmMain.qryShifts.EOF do
      begin
        cmbShift.Items.AddObject(
          Format('Shift %d - %s (%s) - %s', [
            dmMain.qryShifts.FieldByName('shift_id').AsInteger,
            dmMain.qryShifts.FieldByName('shift_type').AsString,
            dmMain.qryShifts.FieldByName('username').AsString,
            FormatDateTime('dd/mm/yyyy hh:nn', dmMain.qryShifts.FieldByName('start_time').AsDateTime)
          ]),
          TObject(PtrInt(dmMain.qryShifts.FieldByName('shift_id').AsInteger))
        );
        dmMain.qryShifts.Next;
      end;
    end;
    
    cmbShift.ItemIndex := 0;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('LoadShifts error: ' + E.Message);
      ShowMessage('Error loading shifts: ' + E.Message);
    end;
  end;
end;

procedure TfrmReports.cmbShiftChange(Sender: TObject);
begin
  // Enable/disable the generate report button based on selection
  btnGenerateShiftReport.Enabled := (cmbShift.ItemIndex > 0);
end;

procedure TfrmReports.btnGenerateClick(Sender: TObject);
begin
  try
    case cmbReportType.ItemIndex of
      0: GenerateDailyReport;
      2: GenerateProductReport;
      3: GeneratePaymentReport;
      4: GenerateUserReport;
    end;
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('btnGenerateClick error: ' + E.Message);
      ShowMessage('Error generating report: ' + E.Message);
    end;
  end;
end;

procedure TfrmReports.btnGenerateShiftReportClick(Sender: TObject);
begin
  LoadShiftReport;
end;

procedure TfrmReports.LoadShiftReport;
var
  ShiftID: Integer;
  TotalSales: Currency;
begin
  try
    // Get ShiftID from selected shift in combo box
    if (cmbShift.ItemIndex <= 0) or (cmbShift.ItemIndex >= cmbShift.Items.Count) then
    begin
      ShowMessage('Please select a valid shift first.');
      Exit;
    end;
    
    // Get the ShiftID from the combo box object
    ShiftID := PtrInt(cmbShift.Items.Objects[cmbShift.ItemIndex]);
    
    // Now use the ShiftID to get shift data
    if dmMain.GetShiftSummary(ShiftID) then
    begin
      // Load shift data into the report
      sgReport.RowCount := 2; // Header + 1 data row for now
      sgReport.Cells[0, 0] := 'Shift ID';
      sgReport.Cells[1, 0] := 'User';
      sgReport.Cells[2, 0] := 'Start Time';
      sgReport.Cells[3, 0] := 'End Time';
      sgReport.Cells[4, 0] := 'Total Sales';
      
      sgReport.Cells[0, 1] := IntToStr(ShiftID);
      sgReport.Cells[1, 1] := dmMain.qryShifts.FieldByName('username').AsString;
      sgReport.Cells[2, 1] := FormatDateTime('dd/mm/yyyy hh:nn', dmMain.qryShifts.FieldByName('start_time').AsDateTime);
      
      if not dmMain.qryShifts.FieldByName('end_time').IsNull then
        sgReport.Cells[3, 1] := FormatDateTime('dd/mm/yyyy hh:nn', dmMain.qryShifts.FieldByName('end_time').AsDateTime)
      else
        sgReport.Cells[3, 1] := 'Active';
        
      // Get total sales for this shift
      TotalSales := 0;
      if dmMain.GetShiftSales(ShiftID) then
      begin
        dmMain.qrySales.First;
        while not dmMain.qrySales.EOF do
        begin
          TotalSales := TotalSales + dmMain.qrySales.FieldByName('total_amount').AsCurrency;
          dmMain.qrySales.Next;
        end;
        sgReport.Cells[4, 1] := FormatFloat('#,##0.00', TotalSales);
      end
      else
        sgReport.Cells[4, 1] := '0.00';
        
      // Enable print and export buttons
      btnPrint.Enabled := True;
      btnExport.Enabled := True;
    end
    else
    begin
      ShowMessage('Unable to load shift data for Shift ID: ' + IntToStr(ShiftID));
    end;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('LoadShiftReport error: ' + E.Message);
      ShowMessage('Error loading shift report: ' + E.Message);
    end;
  end;
end;

// Add placeholder implementations for other report types
procedure TfrmReports.GenerateDailyReport;
begin
  ShowMessage('Daily report generation not yet implemented');
end;

procedure TfrmReports.GenerateProductReport;
begin
  ShowMessage('Product report generation not yet implemented');
end;

procedure TfrmReports.GeneratePaymentReport;
begin
  ShowMessage('Payment report generation not yet implemented');
end;

procedure TfrmReports.GenerateUserReport;
begin
  ShowMessage('User report generation not yet implemented');
end;

procedure TfrmReports.GenerateShiftReport;
begin
  LoadShiftReport;
end;

procedure TfrmReports.PrintShiftReport(ShiftID: Integer);
begin
  ShowMessage('Print functionality not yet implemented');
end;

procedure TfrmReports.btnPrintClick(Sender: TObject);
begin
  ShowMessage('Print functionality not yet implemented');
end;

procedure TfrmReports.btnExportClick(Sender: TObject);
begin
  ShowMessage('Export functionality not yet implemented');
end;

end.
