unit frmSales;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls, StringUtils,
  Grids, ComCtrls, Buttons, DateUtils, DataModule, Logging, DateTimeCtrls, DateTimePicker;

type
  TfrmSales = class(TForm)
    // Main panels
    pnlTop: TPanel;
    pnlMain: TPanel;
    pnlButtons: TPanel;
    
    // Report type selection
    lblReportType: TLabel;
    rbDailySales: TRadioButton;
    rbItemSales: TRadioButton;
    rbUserSales: TRadioButton;
    
    // Date range
    lblDateFrom: TLabel;
    lblDateTo: TLabel;
    dtpDateFrom: TDateTimePicker;
    dtpDateTo: TDateTimePicker;
    
    // Additional filters
    lblUser: TLabel;
    cmbUser: TComboBox;
    lblShift: TLabel;
    cmbShift: TComboBox;
    
    // Results grid
    sgResults: TStringGrid;
    
    // Summary panel
    pnlSummary: TPanel;
    lblTotalSales: TLabel;
    lblTotalAmount: TLabel;
    edtTotalSales: TEdit;
    edtTotalAmount: TEdit;
    
    // Buttons
    btnGenerate: TBitBtn;
    btnPrint: TBitBtn;
    btnExport: TBitBtn;
    btnClose: TBitBtn;
    
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure rbDailySalesChange(Sender: TObject);
    procedure rbItemSalesChange(Sender: TObject);
    procedure rbUserSalesChange(Sender: TObject);
    procedure btnGenerateClick(Sender: TObject);
    procedure btnPrintClick(Sender: TObject);
    procedure btnExportClick(Sender: TObject);
    procedure btnCloseClick(Sender: TObject);
    
  private
    FReportType: string;
    FTotalSales: Integer;
    FTotalAmount: Currency;
    
    procedure InitializeForm;
    procedure LoadUsers;
    procedure SetupGrid;
    procedure GenerateDailySalesReport;
    procedure GenerateItemSalesReport;
    procedure GenerateUserSalesReport;
    procedure UpdateSummary;
    procedure PrintReport;
    procedure ExportToCSV;
    function GetStartOfMonth(ADate: TDate): TDate;
    function GetEndOfMonth(ADate: TDate): TDate;
    
  public
    procedure SetReportType(const ReportType: string);
  end;

var
  formSales: TfrmSales;

implementation

uses
  frmReceiptPreview, PrinterUtils;

{$R *.lfm}

procedure TfrmSales.FormCreate(Sender: TObject);
begin
  try
    InitializeForm;
    
    if Assigned(Logger) then
      Logger.LogInfo('Sales reports form created');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error creating sales reports form: ' + E.Message);
      ShowMessage('Error initializing form: ' + E.Message);
    end;
  end;
end;

procedure TfrmSales.FormShow(Sender: TObject);
begin
  try
    LoadUsers;
    SetupGrid;
    
    // Set default date range (current month)
    dtpDateFrom.Date := GetStartOfMonth(Date);
    dtpDateTo.Date := GetEndOfMonth(Date);
    
    if Assigned(Logger) then
      Logger.LogInfo('Sales reports form shown');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error showing sales reports form: ' + E.Message);
      ShowMessage('Error loading form: ' + E.Message);
    end;
  end;
end;

function TfrmSales.GetStartOfMonth(ADate: TDate): TDate;
var
  Year, Month, Day: Word;
begin
  DecodeDate(ADate, Year, Month, Day);
  Result := EncodeDate(Year, Month, 1);
end;

function TfrmSales.GetEndOfMonth(ADate: TDate): TDate;
var
  Year, Month, Day: Word;
begin
  DecodeDate(ADate, Year, Month, Day);
  if Month = 12 then
  begin
    Year := Year + 1;
    Month := 1;
  end
  else
    Month := Month + 1;
  Result := EncodeDate(Year, Month, 1) - 1;
end;

procedure TfrmSales.InitializeForm;
begin
  Caption := 'Sales Reports';
  
  // Initialize totals
  FTotalSales := 0;
  FTotalAmount := 0;
  
  // Set default report type
  FReportType := 'DAILY_SALES';
  rbDailySales.Checked := True;
  
  // Initialize shift combo
  cmbShift.Items.Clear;
  cmbShift.Items.Add('All Shifts');
  cmbShift.Items.Add('DAY');
  cmbShift.Items.Add('NIGHT');
  cmbShift.ItemIndex := 0;
  
  // Initialize summary
  edtTotalSales.Text := '0';
  edtTotalAmount.Text := '0.00';
  edtTotalSales.ReadOnly := True;
  edtTotalAmount.ReadOnly := True;
  
  // Button setup
  btnGenerate.Caption := 'Generate Report';
  btnPrint.Caption := 'Print';
  btnExport.Caption := 'Export CSV';
  btnClose.Caption := 'Close';
  
  btnPrint.Enabled := False;
  btnExport.Enabled := False;
end;

procedure TfrmSales.LoadUsers;
begin
  try
    cmbUser.Items.Clear;
    cmbUser.Items.Add('All Users');
    
    if dmMain.GetUsers then
    begin
      dmMain.qryUsers.First;
      while not dmMain.qryUsers.EOF do
      begin
        cmbUser.Items.Add(dmMain.qryUsers.FieldByName('full_name').AsString);
        dmMain.qryUsers.Next;
      end;
    end;
    
    cmbUser.ItemIndex := 0;
    
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error loading users: ' + E.Message);
    end;
  end;
end;

procedure TfrmSales.SetupGrid;
begin
  sgResults.ColCount := 6;
  sgResults.RowCount := 1;
  sgResults.FixedRows := 1;
  
  // Default headers (will be updated based on report type)
  sgResults.Cells[0, 0] := 'Date';
  sgResults.Cells[1, 0] := 'Receipt No';
  sgResults.Cells[2, 0] := 'User';
  sgResults.Cells[3, 0] := 'Items';
  sgResults.Cells[4, 0] := 'Amount';
  sgResults.Cells[5, 0] := 'Payment';
  
  sgResults.ColWidths[0] := 100;
  sgResults.ColWidths[1] := 120;
  sgResults.ColWidths[2] := 120;
  sgResults.ColWidths[3] := 80;
  sgResults.ColWidths[4] := 100;
  sgResults.ColWidths[5] := 80;
end;

procedure TfrmSales.SetReportType(const ReportType: string);
begin
  FReportType := ReportType;
  
  case FReportType of
    'DAILY_SALES': rbDailySales.Checked := True;
    'ITEM_SALES': rbItemSales.Checked := True;
    'USER_SALES': rbUserSales.Checked := True;
  end;
  
  // Update form based on report type
  if FReportType = 'DAILY_SALES' then
    rbDailySalesChange(nil)
  else if FReportType = 'ITEM_SALES' then
    rbItemSalesChange(nil)
  else if FReportType = 'USER_SALES' then
    rbUserSalesChange(nil);
end;

procedure TfrmSales.rbDailySalesChange(Sender: TObject);
begin
  if rbDailySales.Checked then
  begin
    FReportType := 'DAILY_SALES';
    Caption := 'Daily Sales Report';
    
    // Setup grid for daily sales
    sgResults.Cells[0, 0] := 'Date';
    sgResults.Cells[1, 0] := 'Receipt No';
    sgResults.Cells[2, 0] := 'User';
    sgResults.Cells[3, 0] := 'Items';
    sgResults.Cells[4, 0] := 'Amount';
    sgResults.Cells[5, 0] := 'Payment';
    
    // Show/hide relevant controls
    lblUser.Visible := True;
    cmbUser.Visible := True;
    lblShift.Visible := True;
    cmbShift.Visible := True;
  end;
end;

procedure TfrmSales.rbItemSalesChange(Sender: TObject);
begin
  if rbItemSales.Checked then
  begin
    FReportType := 'ITEM_SALES';
    Caption := 'Item Sales Report';
    
    // Setup grid for item sales
    sgResults.Cells[0, 0] := 'Item Code';
    sgResults.Cells[1, 0] := 'Item Name';
    sgResults.Cells[2, 0] := 'Qty Sold';
    sgResults.Cells[3, 0] := 'Unit Price';
    sgResults.Cells[4, 0] := 'Total Amount';
    sgResults.Cells[5, 0] := 'Profit';
    
    // Show/hide relevant controls
    lblUser.Visible := True;
    cmbUser.Visible := True;
    lblShift.Visible := True;
    cmbShift.Visible := True;
  end;
end;

procedure TfrmSales.rbUserSalesChange(Sender: TObject);
begin
  if rbUserSales.Checked then
  begin
    FReportType := 'USER_SALES';
    Caption := 'User Sales Report';
    
    // Setup grid for user sales
    sgResults.Cells[0, 0] := 'User';
    sgResults.Cells[1, 0] := 'Sales Count';
    sgResults.Cells[2, 0] := 'Total Amount';
    sgResults.Cells[3, 0] := 'Avg Sale';
    sgResults.Cells[4, 0] := 'Shift';
    sgResults.Cells[5, 0] := 'Performance';
    
    // Show/hide relevant controls
    lblUser.Visible := False;
    cmbUser.Visible := False;
    lblShift.Visible := True;
    cmbShift.Visible := True;
  end;
end;

procedure TfrmSales.btnGenerateClick(Sender: TObject);
begin
  try
    Screen.Cursor := crHourGlass;
    
    case FReportType of
      'DAILY_SALES': GenerateDailySalesReport;
      'ITEM_SALES': GenerateItemSalesReport;
      'USER_SALES': GenerateUserSalesReport;
    end;
    
    UpdateSummary;
    
    btnPrint.Enabled := sgResults.RowCount > 1;
    btnExport.Enabled := sgResults.RowCount > 1;
    
    if Assigned(Logger) then
      Logger.LogInfo('Generated ' + FReportType + ' report');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error generating report: ' + E.Message);
      ShowMessage('Error generating report: ' + E.Message);
    end;
  end;
  Screen.Cursor := crDefault;
end;

procedure TfrmSales.GenerateDailySalesReport;
var
  Row: Integer;
  UserFilter, ShiftFilter: string;
begin
  // Get filters
  if cmbUser.ItemIndex > 0 then
    UserFilter := cmbUser.Text
  else
    UserFilter := '';
    
  if cmbShift.ItemIndex > 0 then
    ShiftFilter := cmbShift.Text
  else
    ShiftFilter := '';
  
  if dmMain.GetDailySalesReport(dtpDateFrom.Date, dtpDateTo.Date, UserFilter, ShiftFilter) then
  begin
    sgResults.RowCount := dmMain.qryReports.RecordCount + 1;
    Row := 1;
    FTotalSales := 0;
    FTotalAmount := 0;
    
    dmMain.qryReports.First;
    while not dmMain.qryReports.EOF do
    begin
      sgResults.Cells[0, Row] := FormatDateTime('dd/mm/yyyy', dmMain.qryReports.FieldByName('sale_date').AsDateTime);
      sgResults.Cells[1, Row] := dmMain.qryReports.FieldByName('receipt_no').AsString;
      sgResults.Cells[2, Row] := dmMain.qryReports.FieldByName('user_name').AsString;
      sgResults.Cells[3, Row] := dmMain.qryReports.FieldByName('item_count').AsString;
      sgResults.Cells[4, Row] := FormatFloat('#,##0.00', dmMain.qryReports.FieldByName('total_amount').AsCurrency);
      sgResults.Cells[5, Row] := dmMain.qryReports.FieldByName('payment_method').AsString;
      
      Inc(FTotalSales);
      FTotalAmount := FTotalAmount + dmMain.qryReports.FieldByName('total_amount').AsCurrency;
      
      Inc(Row);
      dmMain.qryReports.Next;
    end;
  end;
end;

procedure TfrmSales.GenerateItemSalesReport;
var
  Row: Integer;
  UserFilter, ShiftFilter: string;
begin
  // Get filters
  if cmbUser.ItemIndex > 0 then
    UserFilter := cmbUser.Text
  else
    UserFilter := '';
    
  if cmbShift.ItemIndex > 0 then
    ShiftFilter := cmbShift.Text
  else
    ShiftFilter := '';
  
  if dmMain.GetItemSalesReport(dtpDateFrom.Date, dtpDateTo.Date, UserFilter, ShiftFilter) then
  begin
    sgResults.RowCount := dmMain.qryReports.RecordCount + 1;
    Row := 1;
    FTotalSales := 0;
    FTotalAmount := 0;
    
    dmMain.qryReports.First;
    while not dmMain.qryReports.EOF do
    begin
      sgResults.Cells[0, Row] := dmMain.qryReports.FieldByName('item_code').AsString;
      sgResults.Cells[1, Row] := dmMain.qryReports.FieldByName('item_name').AsString;
      sgResults.Cells[2, Row] := dmMain.qryReports.FieldByName('total_qty').AsString;
      sgResults.Cells[3, Row] := FormatFloat('#,##0.00', dmMain.qryReports.FieldByName('unit_price').AsCurrency);
      sgResults.Cells[4, Row] := FormatFloat('#,##0.00', dmMain.qryReports.FieldByName('total_amount').AsCurrency);
      sgResults.Cells[5, Row] := FormatFloat('#,##0.00', dmMain.qryReports.FieldByName('profit').AsCurrency);
      
      Inc(FTotalSales);
      FTotalAmount := FTotalAmount + dmMain.qryReports.FieldByName('total_amount').AsCurrency;
      
      Inc(Row);
      dmMain.qryReports.Next;
    end;
  end;
end;

procedure TfrmSales.GenerateUserSalesReport;
var
  Row: Integer;
  ShiftFilter: string;
begin
  // Get filters
  if cmbShift.ItemIndex > 0 then
    ShiftFilter := cmbShift.Text
  else
    ShiftFilter := '';
  
  if dmMain.GetUserSalesReport(dtpDateFrom.Date, dtpDateTo.Date, ShiftFilter) then
  begin
    sgResults.RowCount := dmMain.qryReports.RecordCount + 1;
    Row := 1;
    FTotalSales := 0;
    FTotalAmount := 0;
    
    dmMain.qryReports.First;
    while not dmMain.qryReports.EOF do
    begin
      sgResults.Cells[0, Row] := dmMain.qryReports.FieldByName('user_name').AsString;
      sgResults.Cells[1, Row] := dmMain.qryReports.FieldByName('sales_count').AsString;
      sgResults.Cells[2, Row] := FormatFloat('#,##0.00', dmMain.qryReports.FieldByName('total_amount').AsCurrency);
      sgResults.Cells[3, Row] := FormatFloat('#,##0.00', dmMain.qryReports.FieldByName('avg_sale').AsCurrency);
      sgResults.Cells[4, Row] := dmMain.qryReports.FieldByName('shift_type').AsString;
      sgResults.Cells[5, Row] := dmMain.qryReports.FieldByName('performance').AsString + '%';
      
      FTotalSales := FTotalSales + dmMain.qryReports.FieldByName('sales_count').AsInteger;
      FTotalAmount := FTotalAmount + dmMain.qryReports.FieldByName('total_amount').AsCurrency;
      
      Inc(Row);
      dmMain.qryReports.Next;
    end;
  end;
end;

procedure TfrmSales.UpdateSummary;
begin
  edtTotalSales.Text := IntToStr(FTotalSales);
  edtTotalAmount.Text := FormatFloat('#,##0.00', FTotalAmount);
end;

procedure TfrmSales.btnPrintClick(Sender: TObject);
begin
  try
    PrintReport;
    
    if Assigned(Logger) then
      Logger.LogInfo('Printed ' + FReportType + ' report');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error printing report: ' + E.Message);
      ShowMessage('Error printing report: ' + E.Message);
    end;
  end;
end;

procedure TfrmSales.PrintReport;
var
  PrintLines: TStringList;
  i, j: Integer;
  Line: string;
  PrinterName: string;
begin
  PrintLines := TStringList.Create;
  try
    // Header
    PrintLines.Add(CenterText('SALES REPORT', 80));
    PrintLines.Add(CenterText(Caption, 80));
    PrintLines.Add(CenterText('Date: ' + FormatDateTime('dd/mm/yyyy', dtpDateFrom.Date) + 
                             ' to ' + FormatDateTime('dd/mm/yyyy', dtpDateTo.Date), 80));
    PrintLines.Add(StringOfChar('=', 80));
    PrintLines.Add('');
    
    // Column headers
    Line := '';
    for j := 0 to sgResults.ColCount - 1 do
    begin
      Line := Line + PadRight(sgResults.Cells[j, 0], 12) + ' ';
    end;
    PrintLines.Add(Line);
    PrintLines.Add(StringOfChar('-', 80));
    
    // Data rows
    for i := 1 to sgResults.RowCount - 1 do
    begin
      Line := '';
      for j := 0 to sgResults.ColCount - 1 do
      begin
        Line := Line + PadRight(sgResults.Cells[j, i], 12) + ' ';
      end;
      PrintLines.Add(Line);
    end;
    
    // Summary
    PrintLines.Add(StringOfChar('-', 80));
    PrintLines.Add('Total Sales: ' + edtTotalSales.Text);
    PrintLines.Add('Total Amount: ' + edtTotalAmount.Text);
    PrintLines.Add('');
    PrintLines.Add('Generated: ' + FormatDateTime('dd/mm/yyyy hh:nn:ss', Now));
    
    // Get printer name (you can make this configurable)
    PrinterName := ''; // Empty string will use default printer
    
    // Print using existing PrintReceipt procedure
    PrintReceipt(PrintLines, PrinterName);
    
  finally
    PrintLines.Free;
  end;
end;

procedure TfrmSales.btnExportClick(Sender: TObject);
begin
  try
    ExportToCSV;
    
    if Assigned(Logger) then
      Logger.LogInfo('Exported ' + FReportType + ' report to CSV');
      
  except
    on E: Exception do
    begin
      if Assigned(Logger) then
        Logger.LogError('Error exporting report: ' + E.Message);
      ShowMessage('Error exporting report: ' + E.Message);
    end;
  end;
end;

procedure TfrmSales.ExportToCSV;
var
  SaveDialog: TSaveDialog;
  CSVFile: TextFile;
  i, j: Integer;
  Line: string;
begin
  SaveDialog := TSaveDialog.Create(Self);
  try
    SaveDialog.Title := 'Export Report to CSV';
    SaveDialog.Filter := 'CSV Files (*.csv)|*.csv|All Files (*.*)|*.*';
    SaveDialog.DefaultExt := 'csv';
    SaveDialog.FileName := FReportType + '_' + FormatDateTime('yyyymmdd', Date) + '.csv';
    
    if SaveDialog.Execute then
    begin
      AssignFile(CSVFile, SaveDialog.FileName);
      Rewrite(CSVFile);
      
      try
        // Write headers
        Line := '';
        for j := 0 to sgResults.ColCount - 1 do
        begin
          if j > 0 then Line := Line + ',';
          Line := Line + '"' + sgResults.Cells[j, 0] + '"';
        end;
        WriteLn(CSVFile, Line);
        
        // Write data
        for i := 1 to sgResults.RowCount - 1 do
        begin
          Line := '';
          for j := 0 to sgResults.ColCount - 1 do
          begin
            if j > 0 then Line := Line + ',';
            Line := Line + '"' + sgResults.Cells[j, i] + '"';
          end;
          WriteLn(CSVFile, Line);
        end;
        
        ShowMessage('Report exported successfully to: ' + SaveDialog.FileName);
        
      finally
        CloseFile(CSVFile);
      end;
    end;
    
  finally
    SaveDialog.Free;
  end;
end;

procedure TfrmSales.btnCloseClick(Sender: TObject);
begin
  Close;
end;

// Helper functions
function CenterText(const Text: string; Width: Integer): string;
var
  Spaces: Integer;
begin
  if Length(Text) >= Width then
    Result := Copy(Text, 1, Width)
  else
  begin
    Spaces := (Width - Length(Text)) div 2;
    Result := StringOfChar(' ', Spaces) + Text;
  end;
end;

function PadRight(const Text: string; Width: Integer): string;
begin
  Result := Text + StringOfChar(' ', Width - Length(Text));
  if Length(Result) > Width then
    Result := Copy(Result, 1, Width);
end;

end.
