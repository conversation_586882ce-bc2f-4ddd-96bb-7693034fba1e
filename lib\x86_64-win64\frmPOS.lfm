object frmPOS: TfrmPOS
  Left = 278
  Height = 680
  Top = 16
  Width = 928
  BorderStyle = bsSingle
  Caption = 'POS - Point of Sale'
  ClientHeight = 680
  ClientWidth = 928
  Color = clBtnFace
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  KeyPreview = True
  OnClose = FormClose
  OnCreate = FormCreate
  OnKeyPress = FormKeyPress
  OnShow = FormShow
  LCLVersion = '3.8.0.0'
  WindowState = wsMaximized
  object pnlMain: TPanel
    Left = 0
    Height = 680
    Top = 0
    Width = 928
    Align = alClient
    BevelOuter = bvNone
    ClientHeight = 680
    ClientWidth = 928
    Color = clGray
    ParentBackground = False
    ParentColor = False
    TabOrder = 0
    object pnlHeader: TPanel
      Left = 0
      Height = 60
      Top = 0
      Width = 928
      Align = alTop
      BevelOuter = bvNone
      ClientHeight = 60
      ClientWidth = 928
      Color = 3947580
      ParentBackground = False
      ParentColor = False
      TabOrder = 0
      object lblDateTime: TLabel
        Left = 704
        Height = 23
        Top = 20
        Width = 204
        Alignment = taRightJustify
        Anchors = [akTop, akRight]
        Caption = '2023-01-01 12:00:00'
        Font.Color = clWhite
        Font.Height = -19
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
      end
      object lblUser: TLabel
        Left = 20
        Height = 23
        Top = 10
        Width = 56
        Caption = 'User: '
        Font.Color = clWhite
        Font.Height = -19
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
      end
      object lblShift: TLabel
        Left = 20
        Height = 23
        Top = 35
        Width = 51
        Caption = 'Shift: '
        Font.Color = clWhite
        Font.Height = -19
        Font.Name = 'Tahoma'
        ParentColor = False
        ParentFont = False
      end
    end
    object pnlCategories: TPanel
      Left = 0
      Height = 132
      Top = 60
      Width = 928
      Align = alTop
      Anchors = [akTop]
      BevelOuter = bvNone
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 1
    end
    object pnlItems: TPanel
      Left = 0
      Height = 388
      Top = 192
      Width = 454
      Align = alLeft
      BevelOuter = bvNone
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 2
      Wordwrap = True
    end
    object pnlItemDetails: TPanel
      Left = 454
      Height = 388
      Top = 192
      Width = 100
      Align = alClient
      BevelOuter = bvNone
      ClientHeight = 388
      ClientWidth = 100
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 3
      Wordwrap = True
      object imgItem: TImage
        Left = 0
        Height = 268
        Top = 0
        Width = 100
        Align = alClient
        Center = True
        Proportional = True
        Stretch = True
      end
      object pnlItemInfo: TPanel
        Left = 0
        Height = 120
        Top = 268
        Width = 100
        Align = alBottom
        BevelOuter = bvNone
        ClientHeight = 120
        ClientWidth = 100
        Color = clBlack
        ParentBackground = False
        ParentColor = False
        TabOrder = 0
        Wordwrap = True
        object lblItemName: TLabel
          Left = 0
          Height = 60
          Top = 0
          Width = 100
          Align = alClient
          Alignment = taCenter
          AutoSize = False
          Font.Color = clWhite
          Font.Height = -24
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          Layout = tlCenter
          ParentColor = False
          ParentFont = False
          Transparent = False
          WordWrap = True
        end
        object lblItemPrice: TLabel
          Left = 0
          Height = 60
          Top = 60
          Width = 100
          Align = alBottom
          Alignment = taCenter
          AutoSize = False
          Font.Color = clLime
          Font.Height = -24
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          Layout = tlCenter
          ParentColor = False
          ParentFont = False
          Transparent = False
        end
      end
    end
    object pnlCartContainer: TPanel
      Left = 454
      Height = 388
      Top = 192
      Width = 100
      Align = alClient
      BevelOuter = bvNone
      ClientHeight = 388
      ClientWidth = 100
      ParentBackground = False
      TabOrder = 4
      object pnlCartHeader: TPanel
        Left = 0
        Height = 40
        Top = 0
        Width = 100
        Align = alTop
        BevelOuter = bvNone
        ClientHeight = 40
        ClientWidth = 100
        Color = 3947580
        ParentBackground = False
        ParentColor = False
        TabOrder = 0
        object lblCart: TLabel
          Left = 10
          Height = 23
          Top = 10
          Width = 40
          Caption = 'Cart'
          Font.Color = clWhite
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
      end
      object sgCart: TStringGrid
        Left = 5
        Height = 348
        Top = 40
        Width = 90
        Align = alClient
        AutoFillColumns = True
        BorderSpacing.Left = 5
        BorderSpacing.Right = 5
        ColCount = 4
        DefaultColWidth = 80
        DefaultRowHeight = 30
        FixedCols = 0
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goRowSelect]
        RowCount = 1
        TabOrder = 1
        ColWidths = (
          22
          22
          21
          21
        )
      end
    end
    object pnlKeypad: TPanel
      Left = 554
      Height = 388
      Top = 192
      Width = 374
      Align = alRight
      Anchors = [akTop, akRight]
      BevelOuter = bvNone
      ClientHeight = 388
      ClientWidth = 374
      Color = clSilver
      ParentBackground = False
      ParentColor = False
      TabOrder = 6
      object pnlNumpad: TPanel
        Left = 16
        Height = 286
        Top = 0
        Width = 354
        BevelOuter = bvNone
        ClientHeight = 286
        ClientWidth = 354
        Color = clSilver
        ParentBackground = False
        ParentColor = False
        TabOrder = 7
        object btn7: TBitBtn
          Left = 10
          Height = 60
          Top = 10
          Width = 100
          Caption = '7'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 0
        end
        object btn8: TBitBtn
          Left = 120
          Height = 60
          Top = 10
          Width = 100
          Caption = '8'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 1
        end
        object btn9: TBitBtn
          Left = 230
          Height = 60
          Top = 10
          Width = 100
          Caption = '9'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 2
        end
        object btn4: TBitBtn
          Left = 10
          Height = 60
          Top = 80
          Width = 100
          Caption = '4'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 3
        end
        object btn5: TBitBtn
          Left = 120
          Height = 60
          Top = 80
          Width = 100
          Caption = '5'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 4
        end
        object btn6: TBitBtn
          Left = 230
          Height = 60
          Top = 80
          Width = 100
          Caption = '6'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 5
        end
        object btn1: TBitBtn
          Left = 10
          Height = 60
          Top = 150
          Width = 100
          Caption = '1'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 6
        end
        object btn2: TBitBtn
          Left = 120
          Height = 60
          Top = 150
          Width = 100
          Caption = '2'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 7
        end
        object btn3: TBitBtn
          Left = 230
          Height = 60
          Top = 150
          Width = 100
          Caption = '3'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 8
        end
        object btnDecimal: TBitBtn
          Left = 10
          Height = 60
          Top = 220
          Width = 62
          Caption = '.'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 9
        end
        object btn0: TBitBtn
          Left = 88
          Height = 60
          Top = 220
          Width = 48
          Caption = '0'
          Color = clNone
          OnClick = btnNumberClick
          TabOrder = 10
        end
        object btnBackspace: TBitBtn
          Left = 152
          Height = 60
          Top = 220
          Width = 68
          Caption = '⌫'
          Color = clNone
          OnClick = btnBackspaceClick
          TabOrder = 11
        end
      end
      object btnPay: TBitBtn
        Left = 10
        Height = 60
        Top = 312
        Width = 106
        Caption = 'PAY'
        Color = clNone
        OnClick = btnPayClick
        TabOrder = 1
      end
      object btnClear: TBitBtn
        Left = 10
        Height = 60
        Top = 376
        Width = 106
        Caption = 'CLEAR'
        Color = clNone
        OnClick = btnClearClick
        TabOrder = 2
      end
      object btnQuantity: TBitBtn
        Left = 128
        Height = 60
        Top = 376
        Width = 110
        Caption = 'QTY'
        Color = clNone
        OnClick = btnQuantityClick
        TabOrder = 3
      end
      object btnDiscount: TBitBtn
        Left = 250
        Height = 60
        Top = 312
        Width = 110
        Caption = 'DISC'
        Color = clNone
        OnClick = btnDiscountClick
        TabOrder = 4
      end
      object btnVoid: TBitBtn
        Left = 128
        Height = 60
        Top = 312
        Width = 110
        Caption = 'VOID'
        Color = clNone
        OnClick = btnVoidClick
        TabOrder = 5
      end
      object btnPrint: TBitBtn
        Left = 248
        Height = 60
        Top = 376
        Width = 112
        Caption = 'PRINT'
        Color = clNone
        OnClick = btnPrintClick
        TabOrder = 6
      end
      object btnDel: TBitBtn
        Left = 272
        Height = 57
        Top = 224
        Width = 72
        Caption = 'DEL'
        OnClick = btnDelClick
        TabOrder = 0
      end
    end
    object pnlCart: TPanel
      Left = 0
      Height = 100
      Top = 580
      Width = 928
      Align = alBottom
      BevelOuter = bvNone
      ClientHeight = 100
      ClientWidth = 928
      Color = clWhite
      ParentBackground = False
      ParentColor = False
      TabOrder = 5
      object pnlCartFooter: TPanel
        Left = 0
        Height = 100
        Top = 0
        Width = 928
        Align = alClient
        BevelOuter = bvNone
        ClientHeight = 100
        ClientWidth = 928
        Color = clWhite
        ParentBackground = False
        ParentColor = False
        TabOrder = 0
        object lblSubtotal: TLabel
          Left = 700
          Height = 23
          Top = 10
          Width = 88
          Alignment = taRightJustify
          Caption = 'Subtotal:'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
        object lblSubtotalValue: TLabel
          Left = 800
          Height = 23
          Top = 10
          Width = 54
          Caption = '$0.00'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
        object lblTax: TLabel
          Left = 710
          Height = 23
          Top = 35
          Width = 96
          Alignment = taRightJustify
          Caption = 'Tax (10%):'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          ParentColor = False
          ParentFont = False
        end
        object lblTaxValue: TLabel
          Left = 800
          Height = 23
          Top = 35
          Width = 46
          Caption = '$0.00'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          ParentColor = False
          ParentFont = False
        end
        object lblTotal: TLabel
          Left = 900
          Height = 23
          Top = 10
          Width = 56
          Alignment = taRightJustify
          Caption = 'Total:'
          Font.Color = clWindowText
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
        object lblTotalValue: TLabel
          Left = 970
          Height = 23
          Top = 10
          Width = 54
          Caption = '$0.00'
          Font.Color = clGreen
          Font.Height = -19
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentColor = False
          ParentFont = False
        end
      end
    end
  end
  object tmrClock: TTimer
    OnTimer = tmrClockTimer
    Left = 144
    Top = 24
  end
end
