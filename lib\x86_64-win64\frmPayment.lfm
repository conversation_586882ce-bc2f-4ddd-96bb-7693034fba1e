object frmPayment: TfrmPayment
  Left = 350
  Height = 400
  Top = 250
  Width = 450
  Caption = 'Payment Processing'
  ClientHeight = 400
  ClientWidth = 450
  Position = poScreenCenter
  LCLVersion = '2.2.6.0'
  object pnlTop: TPanel
    Left = 0
    Height = 80
    Top = 0
    Width = 450
    Align = alTop
    ClientHeight = 80
    ClientWidth = 450
    TabOrder = 0
    object lblPaymentMethod: TLabel
      Left = 8
      Height = 15
      Top = 8
      Width = 89
      Caption = 'Payment Method:'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object lblPaymentMethodValue: TLabel
      Left = 103
      Height = 15
      Top = 8
      Width = 30
      Caption = 'CASH'
      Font.Color = clBlue
      ParentFont = False
    end
    object lblAmount: TLabel
      Left = 8
      Height = 15
      Top = 32
      Width = 43
      Caption = 'Amount:'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object lblAmountValue: TLabel
      Left = 57
      Height = 20
      Top = 30
      Width = 50
      Caption = '0.00'
      Font.Color = clRed
      Font.Size = 12
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object pnlMain: TPanel
    Left = 0
    Height = 270
    Top = 80
    Width = 450
    Align = alClient
    ClientHeight = 270
    ClientWidth = 450
    TabOrder = 1
    object pcPayment: TPageControl
      Left = 1
      Height = 268
      Top = 1
      Width = 448
      ActivePage = tsCash
      Align = alClient
      TabIndex = 0
      TabOrder = 0
      object tsCash: TTabSheet
        Caption = 'Cash Payment'
        ClientHeight = 240
        ClientWidth = 440
        object lblAmountReceived: TLabel
          Left = 8
          Height = 15
          Top = 16
          Width = 94
          Caption = 'Amount Received:'
        end
        object edtAmountReceived: TEdit
          Left = 8
          Height = 23
          Top = 36
          Width = 150
          TabOrder = 0
          Text = '0.00'
        end
        object lblChange: TLabel
          Left = 8
          Height = 15
          Top = 72
          Width = 41
          Caption = 'Change:'
        end
        object edtChange: TEdit
          Left = 8
          Height = 23
          Top = 92
          Width = 150
          ReadOnly = True
          TabOrder = 1
          Text = '0.00'
        end
        object btnCalculate: TBitBtn
          Left = 168
          Height = 25
          Top = 36
          Width = 75
          Caption = 'Calculate'
          TabOrder = 2
        end
      end
      object tsCard: TTabSheet
        Caption = 'Card Payment'
        ClientHeight = 240
        ClientWidth = 440
        object lblCardNumber: TLabel
          Left = 8
          Height = 15
          Top = 16
          Width = 72
          Caption = 'Card Number:'
        end
        object edtCardNumber: TEdit
          Left = 8
          Height = 23
          Top = 36
          Width = 200
          TabOrder = 0
        end
        object lblAuthCode: TLabel
          Left = 8
          Height = 15
          Top = 72
          Width = 94
          Caption = 'Authorization Code:'
        end
        object edtAuthCode: TEdit
          Left = 8
          Height = 23
          Top = 92
          Width = 150
          TabOrder = 1
        end
        object btnProcessCard: TBitBtn
          Left = 8
          Height = 30
          Top = 130
          Width = 100
          Caption = 'Process Card'
          TabOrder = 2
        end
        object lblCardStatus: TLabel
          Left = 120
          Height = 15
          Top = 138
          Width = 200
          Caption = 'Ready to process...'
          Font.Color = clGray
          ParentFont = False
        end
      end
      object tsQR: TTabSheet
        Caption = 'QR Payment'
        ClientHeight = 240
        ClientWidth = 440
        object lblQRCode: TLabel
          Left = 8
          Height = 15
          Top = 16
          Width = 49
          Caption = 'QR Code:'
        end
        object edtQRCode: TEdit
          Left = 8
          Height = 23
          Top = 36
          Width = 200
          TabOrder = 0
        end
        object lblQRReference: TLabel
          Left = 8
          Height = 15
          Top = 72
          Width = 105
          Caption = 'Reference Number:'
        end
        object edtQRReference: TEdit
          Left = 8
          Height = 23
          Top = 92
          Width = 150
          TabOrder = 1
        end
        object btnGenerateQR: TBitBtn
          Left = 8
          Height = 30
          Top = 130
          Width = 100
          Caption = 'Generate QR'
          TabOrder = 2
        end
        object btnVerifyQR: TBitBtn
          Left = 120
          Height = 30
          Top = 130
          Width = 100
          Caption = 'Verify Payment'
          TabOrder = 3
        end
        object lblQRStatus: TLabel
          Left = 8
          Height = 15
          Top = 170
          Width = 200
          Caption = 'Waiting for payment...'
          Font.Color = clGray
          ParentFont = False
        end
      end
    end
  end
  object pnlButtons: TPanel
    Left = 0
    Height = 50
    Top = 350
    Width = 450
    Align = alBottom
    ClientHeight = 50
    ClientWidth = 450
    TabOrder = 2
    object btnOK: TBitBtn
      Left = 286
      Height = 30
      Top = 10
      Width = 75
      Caption = 'OK'
      Default = True
      Enabled = False
      ModalResult = 1
      TabOrder = 0
    end
    object btnCancel: TBitBtn
      Left = 367
      Height = 30
      Top = 10
      Width = 75
      Cancel = True
      Caption = 'Cancel'
      ModalResult = 2
      TabOrder = 1
    end
  end
end