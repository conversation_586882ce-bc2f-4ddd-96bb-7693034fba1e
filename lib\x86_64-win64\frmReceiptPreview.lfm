object frmReceiptPreview: TfrmReceiptPreview
  Left = 415
  Height = 600
  Top = 88
  Width = 500
  Caption = 'Receipt Preview'
  ClientHeight = 600
  ClientWidth = 500
  Position = poScreenCenter
  LCLVersion = '*******'
  object pnlTop: TPanel
    Left = 0
    Height = 40
    Top = 0
    Width = 500
    Align = alTop
    ClientHeight = 40
    ClientWidth = 500
    TabOrder = 0
    object lblTitle: TLabel
      Left = 8
      Height = 15
      Top = 12
      Width = 92
      Caption = 'Receipt Preview'
      Font.Style = [fsBold]
      ParentFont = False
    end
  end
  object pnlMain: TPanel
    Left = 0
    Height = 510
    Top = 40
    Width = 500
    Align = alClient
    ClientHeight = 510
    ClientWidth = 500
    TabOrder = 1
    object memoReceipt: TMemo
      Left = 1
      Height = 508
      Top = 1
      Width = 498
      Align = alClient
      Font.Height = -12
      Font.Name = 'Courier New'
      Font.Pitch = fpFixed
      ParentFont = False
      ReadOnly = True
      ScrollBars = ssVertical
      TabOrder = 0
    end
  end
  object pnlButtons: TPanel
    Left = 0
    Height = 50
    Top = 550
    Width = 500
    Align = alBottom
    ClientHeight = 50
    ClientWidth = 500
    TabOrder = 2
    object btnPrint: TBitBtn
      Left = 8
      Height = 30
      Top = 10
      Width = 75
      Caption = 'Print'
      OnClick = btnPrintClick
      TabOrder = 0
    end
    object btnEmail: TBitBtn
      Left = 91
      Height = 30
      Top = 10
      Width = 75
      Caption = 'Email'
      OnClick = btnEmailClick
      TabOrder = 1
    end
    object btnSave: TBitBtn
      Left = 174
      Height = 30
      Top = 10
      Width = 75
      Caption = 'Save'
      OnClick = btnSaveClick
      TabOrder = 2
    end
    object btnClose: TBitBtn
      Left = 417
      Height = 30
      Top = 10
      Width = 75
      Caption = 'Close'
      OnClick = btnCloseClick
      TabOrder = 3
    end
  end
  object SaveDialog1: TSaveDialog
    DefaultExt = '.txt'
    Filter = 'Text files|*.txt|All files|*.*'
    Options = [ofOverwritePrompt, ofHideReadOnly, ofEnableSizing]
    Left = 400
    Top = 8
  end
end
