[2025-08-17 18:02:17] [INFO] Application started
[2025-08-17 18:02:17] [INFO] Config database tables created/verified
[2025-08-17 18:02:17] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:02:17] [INFO] Application starting...
[2025-08-17 18:02:17] [INFO] Config database tables created/verified
[2025-08-17 18:02:17] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:02:17] [INFO] DataModule created and connected successfully with performance optimizations
[2025-08-17 18:02:17] [INFO] Login form created
[2025-08-17 18:02:17] [ERROR] Error in GetPOSCategories: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:02:32] [ERROR] LoadCategories error: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:02:32] [ERROR] Error in GetPOSCategories: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:02:34] [ERROR] LoadCategories error: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:02:34] [ERROR] Error in GetPOSCategories: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:02:37] [ERROR] LoadCategories error: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:02:37] [INFO] GetCategories successful, Count: 5
[2025-08-17 18:02:37] [INFO] GetItems successful, RecordCount: 6
[2025-08-17 18:06:12] [ERROR] CreateSale error: TIBConnection : Execute : 
 -violation of FOREIGN KEY constraint "FK_SALES_CASHIER" on table "SALES"
 -Foreign key reference target does not exist
 -Problematic key value is ("CASHIER_ID" = 1)
[2025-08-17 18:06:12] [ERROR] Error saving sale: Failed to create sale record
[2025-08-17 18:06:21] [INFO] ConfigUtils destroyed
[2025-08-17 18:06:21] [INFO] Application terminated
[2025-08-17 18:06:21] [INFO] DataModule destroyed and connection released
[2025-08-17 18:06:21] [INFO] ConfigUtils destroyed
[2025-08-17 18:06:23] [INFO] Application started
[2025-08-17 18:06:23] [INFO] Config database tables created/verified
[2025-08-17 18:06:23] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:06:23] [INFO] Application starting...
[2025-08-17 18:06:23] [INFO] Config database tables created/verified
[2025-08-17 18:06:23] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:06:23] [INFO] DataModule created and connected successfully with performance optimizations
[2025-08-17 18:06:23] [INFO] Login form created
[2025-08-17 18:06:23] [ERROR] Error in GetPOSCategories: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:12:23] [ERROR] LoadCategories error: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:12:23] [ERROR] Error in GetPOSCategories: TIBConnection : PrepareStatement : 
 -Dynamic SQL Error
 -SQL error code = -204
 -Table unknown
 -V_ACTIVE_CATEGORIES
 -At line 1, column 15
[2025-08-17 18:32:24] [INFO] Application started
[2025-08-17 18:32:24] [INFO] Config database tables created/verified
[2025-08-17 18:32:24] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:32:24] [INFO] Application starting...
[2025-08-17 18:32:24] [INFO] Config database tables created/verified
[2025-08-17 18:32:24] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:32:24] [INFO] DataModule created and connected successfully with performance optimizations
[2025-08-17 18:32:24] [INFO] Login form created
[2025-08-17 18:32:24] [INFO] Retrieved 5 active categories
[2025-08-17 18:32:24] [INFO] Retrieved 4 items for category 3
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 5
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 1
[2025-08-17 18:32:24] [INFO] Retrieved 2 items for category 4
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 2
[2025-08-17 18:32:24] [INFO] Retrieved 5 active categories
[2025-08-17 18:32:24] [INFO] Retrieved 4 items for category 3
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 5
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 1
[2025-08-17 18:32:24] [INFO] Retrieved 2 items for category 4
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 2
[2025-08-17 18:32:24] [INFO] Retrieved 5 active categories
[2025-08-17 18:32:24] [INFO] Retrieved 4 items for category 3
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 5
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 1
[2025-08-17 18:32:24] [INFO] Retrieved 2 items for category 4
[2025-08-17 18:32:24] [INFO] Retrieved 0 items for category 2
[2025-08-17 18:32:24] [INFO] GetCategories successful, Count: 5
[2025-08-17 18:32:24] [INFO] GetItems successful, RecordCount: 4
[2025-08-17 18:33:54] [INFO] ConfigUtils destroyed
[2025-08-17 18:33:54] [INFO] Application terminated
[2025-08-17 18:33:54] [INFO] DataModule destroyed and connection released
[2025-08-17 18:33:54] [INFO] ConfigUtils destroyed
[2025-08-17 18:48:14] [INFO] Application started
[2025-08-17 18:48:14] [INFO] Config database tables created/verified
[2025-08-17 18:48:14] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:48:14] [INFO] Application starting...
[2025-08-17 18:48:14] [INFO] Config database tables created/verified
[2025-08-17 18:48:14] [INFO] ConfigUtils initialized with SQLiteCipher database
[2025-08-17 18:48:14] [INFO] DataModule created and connected successfully with performance optimizations
[2025-08-17 18:48:14] [INFO] Login form created
[2025-08-17 18:48:14] [INFO] Retrieved 5 active categories
[2025-08-17 18:48:14] [INFO] Retrieved 4 items for category 3
[2025-08-17 18:48:14] [INFO] Retrieved 0 items for category 5
[2025-08-17 18:48:14] [INFO] Retrieved 0 items for category 1
[2025-08-17 18:48:14] [INFO] Retrieved 2 items for category 4
[2025-08-17 18:48:14] [INFO] Retrieved 0 items for category 2
[2025-08-17 18:48:14] [INFO] Retrieved 5 active categories
[2025-08-17 18:48:14] [INFO] Retrieved 4 items for category 3
[2025-08-17 18:48:14] [INFO] Retrieved 0 items for category 5
[2025-08-17 18:48:14] [INFO] Retrieved 0 items for category 1
[2025-08-17 18:48:14] [INFO] Retrieved 2 items for category 4
[2025-08-17 18:48:14] [INFO] Retrieved 0 items for category 2
[2025-08-17 18:48:15] [INFO] Retrieved 5 active categories
[2025-08-17 18:48:15] [INFO] Retrieved 4 items for category 3
[2025-08-17 18:48:15] [INFO] Retrieved 0 items for category 5
[2025-08-17 18:48:15] [INFO] Retrieved 0 items for category 1
[2025-08-17 18:48:15] [INFO] Retrieved 2 items for category 4
[2025-08-17 18:48:15] [INFO] Retrieved 0 items for category 2
[2025-08-17 18:48:15] [INFO] GetCategories successful, Count: 5
[2025-08-17 18:48:15] [INFO] GetItems successful, RecordCount: 4
[2025-08-17 18:48:22] [INFO] ConfigUtils destroyed
[2025-08-17 18:48:22] [INFO] Application terminated
[2025-08-17 18:48:22] [INFO] DataModule destroyed and connection released
[2025-08-17 18:48:22] [INFO] ConfigUtils destroyed
