program SnookerPOS;

{$mode objfpc}{$H+}

uses
  {$IFDEF UNIX}{$IFDEF UseCThreads}
  cthreads,
  {$ENDIF}{$ENDIF}
  DB, SQLDB, SysUtils, Controls, Dialogs, Interfaces,
  Forms, frmLogin, frmPOS, frmPayment, frmCashInOut, frmEndShift, frmReports,
  DataModule, Logging, ConfigUtils, PrinterUtils,
  MigrationUtils, printer4lazarus;

{$R *.res}

var
  Config: TConfigUtils;

begin
  RequireDerivedFormResource := True;
  Application.Scaled:=True;
  Application.Initialize;

  Logger.LogInfo('Application starting...');

  // Initialize configuration
  try
    Config := TConfigUtils.Create;
  except
    on E: Exception do
    begin
      Logger.LogError('Failed to load configuration: ' + E.Message);
      ShowMessage('Failed to load configuration: ' + E.Message);
      Halt(1);
    end;
  end;
  Application.CreateForm(TdmMain, dmMain);
  Application.CreateForm(TfrmLogin, formLogin);
  Application.CreateForm(TfrmPOS, formPOS);

  // Show login form
  //if formLogin.ShowModal = mrOK then
  //begin
    //Logger.LogInfo('User logged in successfully');
    try
      // Initialize and show POS form instead of main form
      formPOS.CurrentUser := 'Admin'; // formLogin.Username;
      formPOS.CurrentShift := 'DAY'; // You might want to get this from user or system
      formPOS.Show;
      formLogin.Free;
      Application.Run; // This will show the POS form
    except
      on E: Exception do
      begin
        Logger.LogError('Failed to initialize main form: ' + E.Message);
        Halt(1);
      end;
    end;
  //end
  //else
  //begin
  //  Logger.LogInfo('Login cancelled');
  //  Halt(0);
  //end;

  // Cleanup configuration (auto-created forms are automatically freed)
  try
    if Assigned(Config) then
    begin
     // Logger.LogInfo('Cleaning up configuration');
      Config.Free;
    end;
  except
    on E: Exception do
      Logger.LogError('Error during cleanup: ' + E.Message);
  end;

  Logger.LogInfo('Application terminated');
end.
