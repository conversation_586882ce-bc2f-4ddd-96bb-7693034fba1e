-- =====================================================
-- snpPOS Database Setup Script for Firebird
-- Database: snpPOS.fdb
-- =====================================================

-- Create Users table
CREATE TABLE USERS (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    USERNAME VARCHAR(50) UNIQUE NOT NULL,
    PASSWORD_HASH VARCHAR(255) NOT NULL,
    FULL_NAME VARCHAR(100) NOT NULL,
    ROLE VARCHAR(20) NOT NULL,
    IS_ACTIVE SMALLINT DEFAULT 1,
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    LAST_LOGIN TIMESTAMP
);

-- Create Categories table
CREATE TABLE CATEGORIES (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    CATEGORY_NAME VARCHAR(50) UNIQUE NOT NULL,
    IS_ACTIVE SMALLINT DEFAULT 1,
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create Items table
CREATE TABLE ITEMS (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    ITEM_CODE VARCHAR(50) UNIQUE NOT NULL,
    ITEM_NAME VARCHAR(100) NOT NULL,
    CATEGORY VARCHAR(50) NOT NULL,
    BARCODE VARCHAR(100),
    UNIT_PRICE DECIMAL(18, 2) NOT NULL,
    IS_ACTIVE SMALLINT DEFAULT 1,
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create Sales table
CREATE TABLE SALES (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    RECEIPT_NO VARCHAR(50) UNIQUE NOT NULL,
    USER_ID INTEGER NOT NULL,
    SALE_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    SHIFT_TYPE VARCHAR(20) NOT NULL,
    SUBTOTAL DECIMAL(18, 2) NOT NULL,
    DISCOUNT_AMOUNT DECIMAL(18, 2) DEFAULT 0,
    TOTAL_AMOUNT DECIMAL(18, 2) NOT NULL,
    PAYMENT_METHOD VARCHAR(20),
    AMOUNT_RECEIVED DECIMAL(18, 2),
    CHANGE_AMOUNT DECIMAL(18, 2),
    CARD_NUMBER VARCHAR(50),
    AUTH_CODE VARCHAR(50),
    QR_CODE BLOB SUB_TYPE TEXT,
    QR_REFERENCE VARCHAR(100),
    SHIFT_ID INTEGER,
    CONSTRAINT FK_SALES_USER FOREIGN KEY (USER_ID) REFERENCES USERS(ID)
);

-- Create Sale Items table
CREATE TABLE SALE_ITEMS (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    SALE_ID INTEGER NOT NULL,
    ITEM_ID INTEGER NOT NULL,
    QUANTITY INTEGER NOT NULL,
    UNIT_PRICE DECIMAL(18, 2) NOT NULL,
    TOTAL_PRICE DECIMAL(18, 2) NOT NULL,
    CONSTRAINT FK_SALE_ITEMS_SALE FOREIGN KEY (SALE_ID) REFERENCES SALES(ID),
    CONSTRAINT FK_SALE_ITEMS_ITEM FOREIGN KEY (ITEM_ID) REFERENCES ITEMS(ID)
);

-- Create Cash Transactions table
CREATE TABLE CASH_TRANSACTIONS (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    USER_ID INTEGER NOT NULL,
    TRANSACTION_TYPE VARCHAR(20) NOT NULL,
    AMOUNT DECIMAL(18, 2) NOT NULL,
    DESCRIPTION VARCHAR(255),
    SALE_ID INTEGER,
    SHIFT_TYPE VARCHAR(20) NOT NULL,
    TRANSACTION_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT FK_CASH_TRANS_USER FOREIGN KEY (USER_ID) REFERENCES USERS(ID),
    CONSTRAINT FK_CASH_TRANS_SALE FOREIGN KEY (SALE_ID) REFERENCES SALES(ID)
);

-- Create Shifts table
CREATE TABLE SHIFTS (
    ID INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    USER_ID INTEGER NOT NULL,
    SHIFT_TYPE VARCHAR(20) NOT NULL,
    START_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    END_TIME TIMESTAMP,
    OPENING_CASH DECIMAL(18, 2) NOT NULL,
    CLOSING_CASH DECIMAL(18, 2),
    NOTES BLOB SUB_TYPE TEXT,
    IS_CLOSED SMALLINT DEFAULT 0,
    CONSTRAINT FK_SHIFTS_USER FOREIGN KEY (USER_ID) REFERENCES USERS(ID)
);

-- Create indexes for better performance
CREATE INDEX IDX_USERS_USERNAME ON USERS(USERNAME);
CREATE INDEX IDX_USERS_ACTIVE ON USERS(IS_ACTIVE);
CREATE INDEX IDX_ITEMS_CODE ON ITEMS(ITEM_CODE);
CREATE INDEX IDX_ITEMS_BARCODE ON ITEMS(BARCODE);
CREATE INDEX IDX_ITEMS_CATEGORY ON ITEMS(CATEGORY);
CREATE INDEX IDX_ITEMS_ACTIVE ON ITEMS(IS_ACTIVE);
CREATE INDEX IDX_SALES_RECEIPT ON SALES(RECEIPT_NO);
CREATE INDEX IDX_SALES_DATE ON SALES(SALE_DATE);
CREATE INDEX IDX_SALES_USER ON SALES(USER_ID);
CREATE INDEX IDX_SALES_SHIFT ON SALES(SHIFT_TYPE);
CREATE INDEX IDX_SALE_ITEMS_SALE ON SALE_ITEMS(SALE_ID);
CREATE INDEX IDX_SALE_ITEMS_ITEM ON SALE_ITEMS(ITEM_ID);
CREATE INDEX IDX_CASH_TRANS_DATE ON CASH_TRANSACTIONS(TRANSACTION_DATE);
CREATE INDEX IDX_CASH_TRANS_TYPE ON CASH_TRANSACTIONS(TRANSACTION_TYPE);
CREATE INDEX IDX_CASH_TRANS_SHIFT ON CASH_TRANSACTIONS(SHIFT_TYPE);
CREATE INDEX IDX_SHIFTS_TYPE ON SHIFTS(SHIFT_TYPE);
CREATE INDEX IDX_SHIFTS_CLOSED ON SHIFTS(IS_CLOSED);
CREATE INDEX IDX_SHIFTS_DATE ON SHIFTS(START_TIME);

-- =====================================================
-- INSERT INITIAL DATA
-- =====================================================

-- Insert default admin user (password: admin123)
-- Note: You'll need to replace this hash with actual hash from your HashPassword function
INSERT INTO USERS (USERNAME, PASSWORD_HASH, FULL_NAME, ROLE) VALUES 
('admin', '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', 'System Administrator', 'ADMIN');

-- Insert default cashier user (password: cashier123)
INSERT INTO USERS (USERNAME, PASSWORD_HASH, FULL_NAME, ROLE) VALUES 
('cashier', '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', 'Default Cashier', 'CASHIER');

-- Insert default manager user (password: manager123)
INSERT INTO USERS (USERNAME, PASSWORD_HASH, FULL_NAME, ROLE) VALUES 
('manager', '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', 'Store Manager', 'MANAGER');

-- Insert default categories
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Food');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Beverages');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Snacks');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Dairy');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Bakery');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Cues');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Damages');
INSERT INTO CATEGORIES (CATEGORY_NAME) VALUES ('Miscellaneous');

-- Insert sample items
INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('FOOD001', 'White Bread', 'Bakery', '1234567890123', 2.50);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('FOOD002', 'Brown Bread', 'Bakery', '1234567890124', 3.00);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('BEV001', 'Coca Cola 330ml', 'Beverages', '1234567890125', 1.50);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('BEV002', 'Pepsi 330ml', 'Beverages', '1234567890126', 1.50);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('BEV003', 'Mineral Water 500ml', 'Beverages', '1234567890127', 1.00);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('SNACK001', 'Potato Chips', 'Snacks', '1234567890128', 2.00);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('SNACK002', 'Chocolate Bar', 'Snacks', '1234567890129', 1.75);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('DAIRY001', 'Fresh Milk 1L', 'Dairy', '1234567890130', 3.50);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('DAIRY002', 'Cheese Slice 200g', 'Dairy', '1234567890131', 4.00);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('DAIRY003', 'Yogurt Cup', 'Dairy', '1234567890132', 1.25);
INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('BAKERY001', 'Croissant', 'Bakery', '1234567890133', 1.50);
INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('BAKERY002', 'Danish Pastry', 'Bakery', '1234567890134', 2.00);
INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('CUES001', 'Cues Normal', 'Cues', '1234567890135', 100.00);
INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('CUES002', 'Cues Premium', 'Cues', '1234567890136', 250.00);
INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('MISC001', 'Plastic Bag Small', 'Miscellaneous', '1234567890141', 0.25);

INSERT INTO ITEMS (ITEM_CODE, ITEM_NAME, CATEGORY, BARCODE, UNIT_PRICE) VALUES 
('MISC002', 'Plastic Bag Large', 'Miscellaneous', '1234567890142', 0.50);

-- =====================================================
-- CREATE TRIGGERS FOR AUDIT TRAIL (Optional)
-- =====================================================

-- Trigger to update LAST_LOGIN when user authenticates
CREATE OR ALTER TRIGGER TRG_USERS_LOGIN
    FOR USERS
    ACTIVE BEFORE UPDATE POSITION 0
AS
BEGIN
    IF (OLD.LAST_LOGIN IS DISTINCT FROM NEW.LAST_LOGIN) THEN
    BEGIN
        NEW.LAST_LOGIN = CURRENT_TIMESTAMP;
    END
END;

-- =====================================================
-- CREATE STORED PROCEDURES (Optional)
-- =====================================================

-- Procedure to get next receipt number
CREATE OR ALTER PROCEDURE SP_GET_NEXT_RECEIPT_NO
RETURNS (RECEIPT_NO VARCHAR(50))
AS
DECLARE VARIABLE COUNTER INTEGER;
DECLARE VARIABLE TODAY_STR VARCHAR(8);
BEGIN
    TODAY_STR = CAST(EXTRACT(YEAR FROM CURRENT_DATE) AS VARCHAR(4)) ||
                LPAD(CAST(EXTRACT(MONTH FROM CURRENT_DATE) AS VARCHAR(2)), 2, '0') ||
                LPAD(CAST(EXTRACT(DAY FROM CURRENT_DATE) AS VARCHAR(2)), 2, '0');
    
    SELECT COUNT(*) + 1
    FROM SALES
    WHERE CAST(SALE_DATE AS DATE) = CURRENT_DATE
    INTO :COUNTER;
    
    RECEIPT_NO = 'R' || TODAY_STR || LPAD(CAST(COUNTER AS VARCHAR(4)), 4, '0');
END;

-- Procedure to calculate cash balance
CREATE OR ALTER PROCEDURE SP_GET_CASH_BALANCE(
    SHIFT_TYPE_PARAM VARCHAR(20),
    TRANS_DATE DATE
)
RETURNS (BALANCE DECIMAL(18,2))
AS
BEGIN
    SELECT COALESCE(
        SUM(CASE WHEN TRANSACTION_TYPE IN ('CASH_IN', 'SALE') THEN AMOUNT ELSE 0 END) -
        SUM(CASE WHEN TRANSACTION_TYPE = 'CASH_OUT' THEN AMOUNT ELSE 0 END), 0)
    FROM CASH_TRANSACTIONS
    WHERE SHIFT_TYPE = :SHIFT_TYPE_PARAM 
    AND CAST(TRANSACTION_DATE AS DATE) = :TRANS_DATE
    INTO :BALANCE;
END;

-- =====================================================
-- GRANT PERMISSIONS (Adjust as needed)
-- =====================================================

-- Grant permissions to SYSDBA (default admin)
GRANT ALL ON USERS TO SYSDBA;
GRANT ALL ON CATEGORIES TO SYSDBA;
GRANT ALL ON ITEMS TO SYSDBA;
GRANT ALL ON SALES TO SYSDBA;
GRANT ALL ON SALE_ITEMS TO SYSDBA;
GRANT ALL ON CASH_TRANSACTIONS TO SYSDBA;
GRANT ALL ON SHIFTS TO SYSDBA;

-- Grant execute permissions on procedures
GRANT EXECUTE ON PROCEDURE SP_GET_NEXT_RECEIPT_NO TO SYSDBA;
GRANT EXECUTE ON PROCEDURE SP_GET_CASH_BALANCE TO SYSDBA;

-- =====================================================
-- CREATE VIEWS FOR REPORTING (Optional)
-- =====================================================

-- View for daily sales summary
CREATE OR ALTER VIEW V_DAILY_SALES_SUMMARY AS
SELECT 
    CAST(s.SALE_DATE AS DATE) as SALE_DATE,
    s.SHIFT_TYPE,
    COUNT(s.ID) as TRANSACTION_COUNT,
    SUM(s.SUBTOTAL) as TOTAL_SUBTOTAL,
    SUM(s.DISCOUNT_AMOUNT) as TOTAL_DISCOUNT,
    SUM(s.TOTAL_AMOUNT) as TOTAL_SALES,
    SUM(CASE WHEN s.PAYMENT_METHOD = 'CASH' THEN s.TOTAL_AMOUNT ELSE 0 END) as CASH_SALES,
    SUM(CASE WHEN s.PAYMENT_METHOD = 'CARD' THEN s.TOTAL_AMOUNT ELSE 0 END) as CARD_SALES,
    SUM(CASE WHEN s.PAYMENT_METHOD = 'QR' THEN s.TOTAL_AMOUNT ELSE 0 END) as QR_SALES
FROM SALES s
GROUP BY CAST(s.SALE_DATE AS DATE), s.SHIFT_TYPE;

-- View for item sales summary
CREATE OR ALTER VIEW V_ITEM_SALES_SUMMARY AS
SELECT 
    i.ITEM_CODE,
    i.ITEM_NAME,
    i.CATEGORY,
    SUM(si.QUANTITY) as TOTAL_QTY_SOLD,
    SUM(si.TOTAL_PRICE) as TOTAL_REVENUE,
    AVG(si.UNIT_PRICE) as AVG_SELLING_PRICE,
    COUNT(DISTINCT si.SALE_ID) as TIMES_SOLD
FROM ITEMS i
INNER JOIN SALE_ITEMS si ON i.ID = si.ITEM_ID
INNER JOIN SALES s ON si.SALE_ID = s.ID
WHERE i.IS_ACTIVE = 1
GROUP BY i.ID, i.ITEM_CODE, i.ITEM_NAME, i.CATEGORY;

-- View for user performance
CREATE OR ALTER VIEW V_USER_PERFORMANCE AS
SELECT 
    u.USERNAME,
    u.FULL_NAME,
    u.ROLE,
    COUNT(s.ID) as TOTAL_SALES,
    SUM(s.TOTAL_AMOUNT) as TOTAL_REVENUE,
    AVG(s.TOTAL_AMOUNT) as AVG_SALE_AMOUNT,
    MAX(s.SALE_DATE) as LAST_SALE_DATE
FROM USERS u
LEFT JOIN SALES s ON u.ID = s.USER_ID
WHERE u.IS_ACTIVE = 1
GROUP BY u.ID, u.USERNAME, u.FULL_NAME, u.ROLE;

-- View for shift summary
CREATE OR ALTER VIEW V_SHIFT_SUMMARY AS
SELECT 
    sh.ID as SHIFT_ID,
    u.FULL_NAME as USER_NAME,
    sh.SHIFT_TYPE,
    sh.START_TIME,
    sh.END_TIME,
    sh.OPENING_CASH,
    sh.CLOSING_CASH,
    sh.IS_CLOSED,
    COUNT(s.ID) as SALES_COUNT,
    COALESCE(SUM(s.TOTAL_AMOUNT), 0) as TOTAL_SALES,
    COALESCE(SUM(CASE WHEN s.PAYMENT_METHOD = 'CASH' THEN s.TOTAL_AMOUNT ELSE 0 END), 0) as CASH_SALES
FROM SHIFTS sh
LEFT JOIN USERS u ON sh.USER_ID = u.ID
LEFT JOIN SALES s ON sh.ID = s.SHIFT_ID
GROUP BY sh.ID, u.FULL_NAME, sh.SHIFT_TYPE, sh.START_TIME, sh.END_TIME, 
         sh.OPENING_CASH, sh.CLOSING_CASH, sh.IS_CLOSED;

-- =====================================================
-- CREATE GENERATORS/SEQUENCES (If needed for custom numbering)
-- =====================================================

-- Generator for receipt numbers (alternative approach)
CREATE GENERATOR GEN_RECEIPT_NO;
SET GENERATOR GEN_RECEIPT_NO TO 1000;

-- Generator for transaction numbers
CREATE GENERATOR GEN_TRANSACTION_NO;
SET GENERATOR GEN_TRANSACTION_NO TO 1;

-- =====================================================
-- ADDITIONAL STORED PROCEDURES
-- =====================================================

-- Procedure to close shift
CREATE OR ALTER PROCEDURE SP_CLOSE_SHIFT(
    SHIFT_ID_PARAM INTEGER,
    CLOSING_CASH_PARAM DECIMAL(18,2),
    NOTES_PARAM BLOB SUB_TYPE TEXT
)
AS
BEGIN
    UPDATE SHIFTS 
    SET END_TIME = CURRENT_TIMESTAMP,
        CLOSING_CASH = :CLOSING_CASH_PARAM,
        NOTES = :NOTES_PARAM,
        IS_CLOSED = 1
    WHERE ID = :SHIFT_ID_PARAM;
END;

-- Procedure to get top selling items
CREATE OR ALTER PROCEDURE SP_GET_TOP_SELLING_ITEMS(
    LIMIT_COUNT INTEGER,
    DATE_FROM DATE,
    DATE_TO DATE
)
RETURNS (
    ITEM_CODE VARCHAR(50),
    ITEM_NAME VARCHAR(100),
    TOTAL_QTY INTEGER,
    TOTAL_REVENUE DECIMAL(18,2)
)
AS
BEGIN
    FOR SELECT 
        i.ITEM_CODE,
        i.ITEM_NAME,
        SUM(si.QUANTITY) as TOTAL_QTY,
        SUM(si.TOTAL_PRICE) as TOTAL_REVENUE
    FROM ITEMS i
    INNER JOIN SALE_ITEMS si ON i.ID = si.ITEM_ID
    INNER JOIN SALES s ON si.SALE_ID = s.ID
    WHERE CAST(s.SALE_DATE AS DATE) BETWEEN :DATE_FROM AND :DATE_TO
    GROUP BY i.ID, i.ITEM_CODE, i.ITEM_NAME
    ORDER BY SUM(si.QUANTITY) DESC
    ROWS :LIMIT_COUNT
    INTO :ITEM_CODE, :ITEM_NAME, :TOTAL_QTY, :TOTAL_REVENUE
    DO
        SUSPEND;
END;

-- Procedure to backup sales data
CREATE OR ALTER PROCEDURE SP_BACKUP_SALES_DATA(
    BACKUP_DATE DATE
)
AS
BEGIN
    -- This is a placeholder for backup logic
    -- In real implementation, you might copy data to archive tables
    -- or export to external files
    
    -- Example: Create backup table if not exists
    -- You can customize this based on your backup strategy
    NULL; -- Placeholder
END;

-- =====================================================
-- CREATE ROLES AND USERS (Optional - for security)
-- =====================================================

-- Create roles
CREATE ROLE ROLE_ADMIN;
CREATE ROLE ROLE_MANAGER;
CREATE ROLE ROLE_CASHIER;

-- Grant permissions to roles
GRANT ALL ON USERS TO ROLE_ADMIN;
GRANT ALL ON CATEGORIES TO ROLE_ADMIN;
GRANT ALL ON ITEMS TO ROLE_ADMIN;
GRANT ALL ON SALES TO ROLE_ADMIN;
GRANT ALL ON SALE_ITEMS TO ROLE_ADMIN;
GRANT ALL ON CASH_TRANSACTIONS TO ROLE_ADMIN;
GRANT ALL ON SHIFTS TO ROLE_ADMIN;

-- Manager permissions
GRANT SELECT, INSERT, UPDATE ON USERS TO ROLE_MANAGER;
GRANT ALL ON CATEGORIES TO ROLE_MANAGER;
GRANT ALL ON ITEMS TO ROLE_MANAGER;
GRANT SELECT ON SALES TO ROLE_MANAGER;
GRANT SELECT ON SALE_ITEMS TO ROLE_MANAGER;
GRANT SELECT ON CASH_TRANSACTIONS TO ROLE_MANAGER;
GRANT ALL ON SHIFTS TO ROLE_MANAGER;

-- Cashier permissions
GRANT SELECT ON USERS TO ROLE_CASHIER;
GRANT SELECT ON CATEGORIES TO ROLE_CASHIER;
GRANT SELECT ON ITEMS TO ROLE_CASHIER;
GRANT INSERT, SELECT ON SALES TO ROLE_CASHIER;
GRANT INSERT, SELECT ON SALE_ITEMS TO ROLE_CASHIER;
GRANT INSERT, SELECT ON CASH_TRANSACTIONS TO ROLE_CASHIER;
GRANT INSERT, SELECT, UPDATE ON SHIFTS TO ROLE_CASHIER;

-- Grant view permissions
GRANT SELECT ON V_DAILY_SALES_SUMMARY TO ROLE_ADMIN, ROLE_MANAGER;
GRANT SELECT ON V_ITEM_SALES_SUMMARY TO ROLE_ADMIN, ROLE_MANAGER;
GRANT SELECT ON V_USER_PERFORMANCE TO ROLE_ADMIN, ROLE_MANAGER;
GRANT SELECT ON V_SHIFT_SUMMARY TO ROLE_ADMIN, ROLE_MANAGER, ROLE_CASHIER;

-- =====================================================
-- SAMPLE TRANSACTIONS (Optional - for testing)
-- =====================================================

-- Sample shift
INSERT INTO SHIFTS (USER_ID, SHIFT_TYPE, OPENING_CASH) VALUES (1, 'MORNING', 100.00);

-- Sample sales transaction
INSERT INTO SALES (RECEIPT_NO, USER_ID, SHIFT_TYPE, SUBTOTAL, DISCOUNT_AMOUNT, TOTAL_AMOUNT, PAYMENT_METHOD, AMOUNT_RECEIVED, CHANGE_AMOUNT, SHIFT_ID) 
VALUES ('R202412010001', 1, 'MORNING', 10.00, 0.00, 10.00, 'CASH', 10.00, 0.00, 1);

-- Sample sale items
INSERT INTO SALE_ITEMS (SALE_ID, ITEM_ID, QUANTITY, UNIT_PRICE, TOTAL_PRICE) VALUES (1, 1, 2, 2.50, 5.00);
INSERT INTO SALE_ITEMS (SALE_ID, ITEM_ID, QUANTITY, UNIT_PRICE, TOTAL_PRICE) VALUES (1, 3, 3, 1.50, 4.50);
INSERT INTO SALE_ITEMS (SALE_ID, ITEM_ID, QUANTITY, UNIT_PRICE, TOTAL_PRICE) VALUES (1, 19, 1, 0.50, 0.50);

-- Sample cash transaction
INSERT INTO CASH_TRANSACTIONS (USER_ID, TRANSACTION_TYPE, AMOUNT, DESCRIPTION, SALE_ID, SHIFT_TYPE) 
VALUES (1, 'SALE', 10.00, 'Sale transaction', 1, 'MORNING');

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Procedure to clean old data (run periodically)
CREATE OR ALTER PROCEDURE SP_CLEANUP_OLD_DATA(
    DAYS_TO_KEEP INTEGER
)
AS
DECLARE VARIABLE CUTOFF_DATE DATE;
BEGIN
    CUTOFF_DATE = CURRENT_DATE - :DAYS_TO_KEEP;
    
    -- Delete old cash transactions (keep sales data)
    DELETE FROM CASH_TRANSACTIONS 
    WHERE TRANSACTION_DATE < :CUTOFF_DATE 
    AND TRANSACTION_TYPE NOT IN ('SALE');
    
    -- You can add more cleanup logic here
END;

-- Procedure to rebuild indexes (maintenance)
CREATE OR ALTER PROCEDURE SP_REBUILD_INDEXES
AS
BEGIN
    -- This would contain index rebuild commands
    -- Firebird automatically maintains indexes, but you can add custom logic here
    NULL; -- Placeholder
END;

-- =====================================================
-- COMMIT ALL CHANGES
-- =====================================================

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES (Run these to verify setup)
-- =====================================================

-- Check if all tables are created
SELECT RDB$RELATION_NAME 
FROM RDB$RELATIONS 
WHERE RDB$SYSTEM_FLAG = 0 
ORDER BY RDB$RELATION_NAME;

-- Check users count
SELECT COUNT(*) as USER_COUNT FROM USERS;

-- Check categories count  
SELECT COUNT(*) as CATEGORY_COUNT FROM CATEGORIES;

-- Check items count
SELECT COUNT(*) as ITEM_COUNT FROM ITEMS;

-- Check sample data
SELECT 'Users' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM USERS
UNION ALL
SELECT 'Categories', COUNT(*) FROM CATEGORIES  
UNION ALL
SELECT 'Items', COUNT(*) FROM ITEMS
UNION ALL
SELECT 'Sales', COUNT(*) FROM SALES
UNION ALL
SELECT 'Sale_Items', COUNT(*) FROM SALE_ITEMS
UNION ALL
SELECT 'Cash_Transactions', COUNT(*) FROM CASH_TRANSACTIONS
UNION ALL
SELECT 'Shifts', COUNT(*) FROM SHIFTS;

-- =====================================================
-- END OF SCRIPT
-- =====================================================