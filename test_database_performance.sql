-- Database Performance Testing Script for Snooker POS
-- Run this script to verify indexes and performance optimizations

-- Test 1: Verify all indexes are created
SELECT 
    RDB$INDEX_NAME as INDEX_NAME,
    RDB$RELATION_NAME as TABLE_NAME,
    RDB$UNIQUE_FLAG as IS_UNIQUE,
    RDB$INDEX_INACTIVE as IS_INACTIVE
FROM RDB$INDICES 
WHERE RDB$SYSTEM_FLAG = 0 
ORDER BY RDB$RELATION_NAME, RDB$INDEX_NAME;

-- Test 2: Verify views are created
SELECT 
    RDB$RELATION_NAME as VIEW_NAME,
    RDB$DESCRIPTION as <PERSON><PERSON><PERSON><PERSON>TION
FROM RDB$RELATIONS 
WHERE RDB$VIEW_BLR IS NOT NULL 
AND RDB$SYSTEM_FLAG = 0
ORDER BY RDB$RELATION_NAME;

-- Test 3: Verify stored procedures
SELECT 
    RDB$PROCEDURE_NAME as PROCEDURE_NAME,
    RDB$PROCEDURE_TYPE as PROC_TYPE
FROM RDB$PROCEDURES 
WHERE RDB$SYSTEM_FLAG = 0
ORDER BY RDB$PROCEDURE_NAME;

-- Test 4: Test case-insensitive search performance
-- This should use the computed index idx_items_name_upper
SELECT COUNT(*) as ITEMS_WITH_COFFEE
FROM items 
WHERE UPPER(item_name) CONTAINING 'COFFEE';

-- Test 5: Test exact match performance
-- This should use the exact index idx_items_name_exact
SELECT COUNT(*) as EXACT_MATCHES
FROM items 
WHERE item_name = 'Coffee';

-- Test 6: Test category search performance
SELECT COUNT(*) as BEVERAGE_ITEMS
FROM items i
INNER JOIN categories c ON i.category_id = c.id
WHERE UPPER(c.category_name) CONTAINING 'BEVERAGE';

-- Test 7: Test the optimized search stored procedure
SELECT * FROM sp_search_items('cof');
SELECT * FROM sp_search_items('COKE');
SELECT * FROM sp_search_items('bev');

-- Test 8: Test view performance
SELECT COUNT(*) as ACTIVE_CATEGORIES FROM v_active_categories;
SELECT COUNT(*) as ACTIVE_ITEMS FROM v_active_items;

-- Test 9: Test composite index performance
-- This should use idx_items_category_active
SELECT COUNT(*) as ITEMS_IN_CATEGORY_3
FROM items 
WHERE category_id = 3 AND is_active = 1;

-- Test 10: Test user authentication index
-- This should use idx_users_username_active
SELECT COUNT(*) as ACTIVE_ADMIN_USERS
FROM users 
WHERE username = 'admin' AND is_active = 1;

-- Performance Analysis Queries
-- These help identify slow queries and index usage

-- Check index statistics (Firebird 3.0+)
SELECT 
    MON$STAT_GROUP,
    MON$STAT_ID,
    MON$RECORD_SEQ_READS,
    MON$RECORD_IDX_READS,
    MON$RECORD_INSERTS,
    MON$RECORD_UPDATES,
    MON$RECORD_DELETES
FROM MON$RECORD_STATS
WHERE MON$STAT_GROUP = 1; -- Table statistics

-- Sample data insertion for testing (optional)
-- Uncomment these if you want to test with more data

/*
-- Insert additional test categories
INSERT INTO categories (category_name, description, is_active) VALUES
('Hot Beverages', 'Coffee, Tea, Hot Chocolate', 1),
('Cold Beverages', 'Soft Drinks, Juices, Water', 1),
('Alcoholic Beverages', 'Beer, Wine, Spirits', 1),
('Light Meals', 'Sandwiches, Salads, Wraps', 1),
('Desserts', 'Cakes, Ice Cream, Pastries', 1);

-- Insert additional test items
INSERT INTO items (item_code, item_name, category_id, unit_price, is_active) VALUES
('ESPRESSO', 'Espresso Coffee', 6, 2.50, 1),
('CAPPUCCINO', 'Cappuccino', 6, 3.50, 1),
('LATTE', 'Cafe Latte', 6, 4.00, 1),
('AMERICANO', 'Americano Coffee', 6, 3.00, 1),
('SPRITE', 'Sprite', 7, 2.50, 1),
('FANTA', 'Fanta Orange', 7, 2.50, 1),
('JUICE_APPLE', 'Apple Juice', 7, 3.00, 1),
('JUICE_ORANGE', 'Orange Juice', 7, 3.00, 1),
('BEER_LOCAL', 'Local Beer', 8, 5.00, 1),
('WINE_RED', 'Red Wine Glass', 8, 8.00, 1),
('SANDWICH_HAM', 'Ham Sandwich', 9, 6.50, 1),
('SANDWICH_CLUB', 'Club Sandwich', 9, 8.00, 1),
('SALAD_CAESAR', 'Caesar Salad', 9, 7.50, 1),
('CAKE_CHOC', 'Chocolate Cake', 10, 4.50, 1),
('ICE_VANILLA', 'Vanilla Ice Cream', 10, 3.50, 1);
*/

-- Performance timing test
-- Run this to measure query execution time
SET TERM ^ ;

EXECUTE BLOCK
AS
DECLARE VARIABLE start_time TIMESTAMP;
DECLARE VARIABLE end_time TIMESTAMP;
DECLARE VARIABLE duration INTEGER;
BEGIN
    -- Test search procedure performance
    start_time = CURRENT_TIMESTAMP;
    
    -- Perform multiple searches
    FOR SELECT * FROM sp_search_items('coffee') INTO :start_time DO
    BEGIN
        -- Just iterate through results
    END
    
    end_time = CURRENT_TIMESTAMP;
    duration = DATEDIFF(MILLISECOND, start_time, end_time);
    
    -- Output would go to application log in real implementation
    -- This is just for testing purposes
END^

SET TERM ; ^

-- Index usage analysis
-- This helps verify that indexes are being used effectively
SELECT 
    r.RDB$RELATION_NAME as TABLE_NAME,
    i.RDB$INDEX_NAME as INDEX_NAME,
    i.RDB$UNIQUE_FLAG as IS_UNIQUE,
    CASE i.RDB$INDEX_INACTIVE 
        WHEN 0 THEN 'ACTIVE' 
        ELSE 'INACTIVE' 
    END as STATUS
FROM RDB$RELATIONS r
LEFT JOIN RDB$INDICES i ON r.RDB$RELATION_NAME = i.RDB$RELATION_NAME
WHERE r.RDB$SYSTEM_FLAG = 0 
AND r.RDB$RELATION_TYPE = 0  -- Tables only
ORDER BY r.RDB$RELATION_NAME, i.RDB$INDEX_NAME;

-- Final verification: Test all optimized queries
-- These are the actual queries used by the POS application

-- 1. Category loading (used by LoadCategories)
SELECT * FROM v_active_categories;

-- 2. Items by category (used by LoadItemsByCategory)
SELECT * FROM v_active_items WHERE category_id = 3;

-- 3. Fast search (used by FastSearchItems)
SELECT * FROM sp_search_items('coke');

-- 4. User authentication (used by login)
SELECT id, password_hash, full_name, role 
FROM users 
WHERE username = 'admin' AND is_active = 1;

-- 5. Sales reporting (used by reports)
SELECT * FROM v_sales_summary 
WHERE sale_date >= CURRENT_DATE - 7;

COMMIT;
