write a complete F and B system for a Pool and Snooker center in Lazarus, complete with forms and units and project files. Make a POS like system with easy to pick items when doing a sale. Include cash in and cash out functions . Payment modes can be Cash or QR or Creaditt Card. Let there be 2 shifts, 6am to 6pm, User login is a must to record person on duty. At end of shift a Sales Report need to be printed on a receipt printer. Use SQLDB firebird 3 for data and use DEFAULT RETURNING ID for auto increment ID. Use Logging.pas ConfigUtils.pas ConnectionPool.pas and create a Datamodule in similar style. Do proper try finally and try except blocks