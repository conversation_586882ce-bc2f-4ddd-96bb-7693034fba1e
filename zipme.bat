@echo off

:: Create BACKUPS directory if it doesn't exist
if not exist "BACKUPS" mkdir "BACKUPS"

:: Get the current directory name
for %%I in (.) do set "CURRENT_DIR=%%~nI"

:: Create the backup file name using current date and time
set "BACKUP_FILE=BACKUPS\%CURRENT_DIR%_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.7z"

:: Create the backup using 7-Zip, excluding files listed in exclude.txt
"C:\Program Files\7-Zip\7z.exe" a -r "%BACKUP_FILE%" "*" -xr@"zexclude.txt"